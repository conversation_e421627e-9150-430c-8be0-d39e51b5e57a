"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(n){"function"==typeof define&&define.amd?define(n):n()}((function(){!function n(t){t.__load_patch("fetch",(function(n,t,e){var o=n.fetch;if("function"==typeof o){var c=n[e.symbol("fetch")];c&&(o=c);var r=n.Promise,i=e.symbol("thenPatched"),f=e.symbol("fetchTaskScheduling"),a=n.Response,u=function(){},l=function(n,o,c,a,l,s){return new Promise((function(h,d){var p=t.current.scheduleMacroTask(n,u,o,(function(){var n,o=t.current;try{o[f]=!0,n=c.apply(a,l)}catch(n){return void d(n)}finally{o[f]=!1}if(!(n instanceof r)){var u=n.constructor;u[i]||e.patchThen(u)}n.then((function(n){"notScheduled"!==p.state&&p.invoke(),h(n)}),(function(n){"notScheduled"!==p.state&&p.invoke(),d(n)}))}),(function(){null==s||s.abort()}))}))};n.fetch=function(){var n=Array.prototype.slice.call(arguments),e=n.length>1?n[1]:{},c=null==e?void 0:e.signal,r=new AbortController;return e.signal=r.signal,n[1]=e,c&&(c[t.__symbol__("addEventListener")]||c.addEventListener).call(c,"abort",(function(){r.abort()}),{once:!0}),l("fetch",{fetchArgs:n},o,this,n,r)},(null==a?void 0:a.prototype)&&["arrayBuffer","blob","formData","json","text"].filter((function(n){return"function"==typeof a.prototype[n]})).forEach((function(n){e.patchMethod(a.prototype,n,(function(t){return function(e,o){return l("Response.".concat(n),void 0,t,e,o,void 0)}}))}))}}))}(Zone)}));