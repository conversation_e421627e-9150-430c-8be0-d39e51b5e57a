/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["tt", [["AM", "PM"], u, u], u, [["Я", "Д", "С", "Ч", "П", "Җ", "Ш"], ["якш.", "дүш.", "сиш.", "чәр.", "пәнҗ.", "җом.", "шим."], ["якшәмбе", "дүшәмбе", "сишәмбе", "чәршәмбе", "пәнҗешәмбе", "җомга", "шимбә"], ["якш.", "дүш.", "сиш.", "чәр.", "пәнҗ.", "җом.", "шим."]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["гыйн.", "фев.", "мар.", "апр.", "май", "июнь", "июль", "авг.", "сент.", "окт.", "нояб.", "дек."], ["гыйнвар", "февраль", "март", "апрель", "май", "июнь", "июль", "август", "сентябрь", "октябрь", "ноябрь", "декабрь"]], u, [["б.э.к.", "милади"], u, ["безнең эрага кадәр", "милади"]], 1, [6, 0], ["dd.MM.y", "d MMM, y 'ел'", "d MMMM, y 'ел'", "d MMMM, y 'ел', EEEE"], ["H:mm", "H:mm:ss", "H:mm:ss z", "H:mm:ss zzzz"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "RUB", "₽", "Россия сумы", { "JPY": ["JP¥", "¥"], "RUB": ["₽"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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