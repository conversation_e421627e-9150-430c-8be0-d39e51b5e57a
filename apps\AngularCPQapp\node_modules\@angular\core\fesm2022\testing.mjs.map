{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../packages/core/testing/src/async.ts", "../../../../../../packages/core/testing/src/defer.ts", "../../../../../../packages/core/testing/src/test_bed_common.ts", "../../../../../../packages/core/testing/src/component_fixture.ts", "../../../../../../packages/core/testing/src/fake_async.ts", "../../../../../../packages/core/testing/src/metadata_overrider.ts", "../../../../../../packages/core/testing/src/resolvers.ts", "../../../../../../packages/core/testing/src/test_bed_compiler.ts", "../../../../../../packages/core/testing/src/test_bed.ts", "../../../../../../packages/core/testing/src/test_hooks.ts", "../../../../../../packages/core/testing/src/testing.ts", "../../../../../../packages/core/testing/public_api.ts", "../../../../../../packages/core/testing/index.ts", "../../../../../../packages/core/testing/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nexport function waitForAsync(fn: Function): (done: any) => any {\n  const _Zone: any = typeof Zone !== 'undefined' ? Zone : null;\n  if (!_Zone) {\n    return function() {\n      return Promise.reject(\n          'Zone is needed for the waitForAsync() test helper but could not be found. ' +\n          'Please make sure that your environment includes zone.js');\n    };\n  }\n  const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n  if (typeof asyncTest === 'function') {\n    return asyncTest(fn);\n  }\n  return function() {\n    return Promise.reject(\n        'zone-testing.js is needed for the async() test helper but could not be found. ' +\n        'Please make sure that your environment includes zone.js/testing');\n  };\n}\n\n/**\n * @deprecated use `waitForAsync()`, (expected removal in v12)\n * @see {@link waitForAsync}\n * @publicApi\n * */\nexport function async(fn: Function): (done: any) => any {\n  return waitForAsync(fn);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵCONTAINER_HEADER_OFFSET as CONTAINER_HEADER_OFFSET, ɵDeferBlockDetails as DeferBlockDetails, ɵDeferBlockState as DeferBlockState, ɵgetDeferBlocks as getDeferBlocks, ɵrenderDeferBlockState as renderDeferBlockState, ɵtriggerResourceLoading as triggerResourceLoading} from '@angular/core';\n\nimport type {ComponentFixture} from './component_fixture';\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n * @developerPreview\n */\nexport class DeferBlockFixture {\n  /** @nodoc */\n  constructor(\n      private block: DeferBlockDetails, private componentFixture: ComponentFixture<unknown>) {}\n\n  /**\n   * Renders the specified state of the defer fixture.\n   * @param state the defer state to render\n   */\n  async render(state: DeferBlockState): Promise<void> {\n    if (!hasStateTemplate(state, this.block)) {\n      const stateAsString = getDeferBlockStateNameFromEnum(state);\n      throw new Error(\n          `Tried to render this defer block in the \\`${stateAsString}\\` state, ` +\n          `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n    }\n    if (state === DeferBlockState.Complete) {\n      await triggerResourceLoading(this.block.tDetails, this.block.lView, this.block.tNode);\n    }\n    // If the `render` method is used explicitly - skip timer-based scheduling for\n    // `@placeholder` and `@loading` blocks and render them immediately.\n    const skipTimerScheduling = true;\n    renderDeferBlockState(state, this.block.tNode, this.block.lContainer, skipTimerScheduling);\n    this.componentFixture.detectChanges();\n  }\n\n  /**\n   * Retrieves all nested child defer block fixtures\n   * in a given defer block.\n   */\n  getDeferBlocks(): Promise<DeferBlockFixture[]> {\n    const deferBlocks: DeferBlockDetails[] = [];\n    // An LContainer that represents a defer block has at most 1 view, which is\n    // located right after an LContainer header. Get a hold of that view and inspect\n    // it for nested defer blocks.\n    const deferBlockFixtures = [];\n    if (this.block.lContainer.length >= CONTAINER_HEADER_OFFSET) {\n      const lView = this.block.lContainer[CONTAINER_HEADER_OFFSET];\n      getDeferBlocks(lView, deferBlocks);\n      for (const block of deferBlocks) {\n        deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n      }\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n}\n\nfunction hasStateTemplate(state: DeferBlockState, block: DeferBlockDetails) {\n  switch (state) {\n    case DeferBlockState.Placeholder:\n      return block.tDetails.placeholderTmplIndex !== null;\n    case DeferBlockState.Loading:\n      return block.tDetails.loadingTmplIndex !== null;\n    case DeferBlockState.Error:\n      return block.tDetails.errorTmplIndex !== null;\n    case DeferBlockState.Complete:\n      return true;\n    default:\n      return false;\n  }\n}\n\nfunction getDeferBlockStateNameFromEnum(state: DeferBlockState) {\n  switch (state) {\n    case DeferBlockState.Placeholder:\n      return 'Placeholder';\n    case DeferBlockState.Loading:\n      return 'Loading';\n    case DeferBlockState.Error:\n      return 'Error';\n    default:\n      return 'Main';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken, SchemaMetadata, ɵDeferBlockBehavior as DeferBlockBehavior} from '@angular/core';\n\n\n/** Whether test modules should be torn down by default. */\nexport const TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n\n/** Whether unknown elements in templates should throw by default. */\nexport const THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n\n/** Whether unknown properties in templates should throw by default. */\nexport const THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n\n/** Whether defer blocks should use manual triggering or play through normally. */\nexport const DEFER_BLOCK_DEFAULT_BEHAVIOR = DeferBlockBehavior.Playthrough;\n\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nexport class TestComponentRenderer {\n  insertRootElement(rootElementId: string) {}\n  removeAllRootElements?() {}\n}\n\n/**\n * @publicApi\n */\nexport const ComponentFixtureAutoDetect = new InjectionToken<boolean>('ComponentFixtureAutoDetect');\n\n/**\n * TODO(atscott): Make public API once we have decided if we want this error and how we want devs to\n * disable it.\n */\nexport const AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs =\n    new InjectionToken<boolean>('AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs');\n\n/**\n * @publicApi\n */\nexport const ComponentFixtureNoNgZone = new InjectionToken<boolean>('ComponentFixtureNoNgZone');\n\n/**\n * @publicApi\n */\nexport interface TestModuleMetadata {\n  providers?: any[];\n  declarations?: any[];\n  imports?: any[];\n  schemas?: Array<SchemaMetadata|any[]>;\n  teardown?: ModuleTeardownOptions;\n  /**\n   * Whether NG0304 runtime errors should be thrown when unknown elements are present in component's\n   * template. Defaults to `false`, where the error is simply logged. If set to `true`, the error is\n   * thrown.\n   * @see [NG8001](/errors/NG8001) for the description of the problem and how to fix it\n   */\n  errorOnUnknownElements?: boolean;\n  /**\n   * Whether errors should be thrown when unknown properties are present in component's template.\n   * Defaults to `false`, where the error is simply logged.\n   * If set to `true`, the error is thrown.\n   * @see [NG8002](/errors/NG8002) for the description of the error and how to fix it\n   */\n  errorOnUnknownProperties?: boolean;\n\n  /**\n   * Whether defer blocks should behave with manual triggering or play through normally.\n   * Defaults to `manual`.\n   */\n  deferBlockBehavior?: DeferBlockBehavior;\n}\n\n/**\n * @publicApi\n */\nexport interface TestEnvironmentOptions {\n  /**\n   * Configures the test module teardown behavior in `TestBed`.\n   */\n  teardown?: ModuleTeardownOptions;\n  /**\n   * Whether errors should be thrown when unknown elements are present in component's template.\n   * Defaults to `false`, where the error is simply logged.\n   * If set to `true`, the error is thrown.\n   * @see [NG8001](/errors/NG8001) for the description of the error and how to fix it\n   */\n  errorOnUnknownElements?: boolean;\n  /**\n   * Whether errors should be thrown when unknown properties are present in component's template.\n   * Defaults to `false`, where the error is simply logged.\n   * If set to `true`, the error is thrown.\n   * @see [NG8002](/errors/NG8002) for the description of the error and how to fix it\n   */\n  errorOnUnknownProperties?: boolean;\n}\n\n/**\n * Configures the test module teardown behavior in `TestBed`.\n * @publicApi\n */\nexport interface ModuleTeardownOptions {\n  /** Whether the test module should be destroyed after every test. Defaults to `true`. */\n  destroyAfterEach: boolean;\n\n  /** Whether errors during test module destruction should be re-thrown. Defaults to `true`. */\n  rethrowErrors?: boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ApplicationRef, ChangeDetectorRef, ComponentRef, DebugElement, ElementRef, getDebugNode, inject, NgZone, RendererFactory2, ViewRef, ɵDeferBlockDetails as DeferBlockDetails, ɵdetectChangesInViewIfRequired, ɵEffectScheduler as EffectScheduler, ɵgetDeferBlocks as getDeferBlocks, ɵNoopNgZone as NoopNgZone, ɵPendingTasks as PendingTasks,} from '@angular/core';\nimport {Subject, Subscription} from 'rxjs';\nimport {first} from 'rxjs/operators';\n\nimport {DeferBlockFixture} from './defer';\nimport {AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone,} from './test_bed_common';\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nexport abstract class ComponentFixture<T> {\n  /**\n   * The DebugElement associated with the root element of this component.\n   */\n  debugElement: DebugElement;\n\n  /**\n   * The instance of the root component class.\n   */\n  componentInstance: T;\n\n  /**\n   * The native element at the root of the component.\n   */\n  nativeElement: any;\n\n  /**\n   * The ElementRef for the element at the root of the component.\n   */\n  elementRef: ElementRef;\n\n  /**\n   * The ChangeDetectorRef for the component\n   */\n  changeDetectorRef: ChangeDetectorRef;\n\n  private _renderer: RendererFactory2|null|undefined;\n  private _isDestroyed: boolean = false;\n  /** @internal */\n  protected readonly _noZoneOptionIsSet = inject(ComponentFixtureNoNgZone, {optional: true});\n  /** @internal */\n  protected _ngZone: NgZone = this._noZoneOptionIsSet ? new NoopNgZone() : inject(NgZone);\n  /** @internal */\n  protected _effectRunner = inject(EffectScheduler);\n  // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n  // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n  // This is a crazy way of doing things but hey, it's the world we live in.\n  // The zoneless scheduler should instead do this more imperatively by attaching\n  // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n  // behavior.\n  /** @internal */\n  protected readonly _appRef = inject(ApplicationRef);\n  /** @internal */\n  protected readonly _testAppRef = this._appRef as unknown as TestAppRef;\n\n  // TODO(atscott): Remove this from public API\n  ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n\n  /** @nodoc */\n  constructor(public componentRef: ComponentRef<T>) {\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = <DebugElement>getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n  }\n\n  /**\n   * Trigger a change detection cycle for the component.\n   */\n  abstract detectChanges(checkNoChanges?: boolean): void;\n\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges(): void {\n    this.changeDetectorRef.checkNoChanges();\n  }\n\n  /**\n   * Set whether the fixture should autodetect changes.\n   *\n   * Also runs detectChanges once so that any existing change is detected.\n   */\n  abstract autoDetectChanges(autoDetect?: boolean): void;\n\n  /**\n   * Return whether the fixture is currently stable or has async tasks that have not been completed\n   * yet.\n   */\n  abstract isStable(): boolean;\n\n  /**\n   * Get a promise that resolves when the fixture is stable.\n   *\n   * This can be used to resume testing after events have triggered asynchronous activity or\n   * asynchronous change detection.\n   */\n  abstract whenStable(): Promise<any>;\n\n  /**\n   * Retrieves all defer block fixtures in the component fixture.\n   *\n   * @developerPreview\n   */\n  getDeferBlocks(): Promise<DeferBlockFixture[]> {\n    const deferBlocks: DeferBlockDetails[] = [];\n    const lView = (this.componentRef.hostView as any)['_lView'];\n    getDeferBlocks(lView, deferBlocks);\n\n    const deferBlockFixtures = [];\n    for (const block of deferBlocks) {\n      deferBlockFixtures.push(new DeferBlockFixture(block, this));\n    }\n\n    return Promise.resolve(deferBlockFixtures);\n  }\n\n  private _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer as RendererFactory2 | null;\n  }\n\n  /**\n   * Get a promise that resolves when the ui state is stable following animations.\n   */\n  whenRenderingDone(): Promise<any> {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n\n  /**\n   * Trigger component destruction.\n   */\n  destroy(): void {\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      this._isDestroyed = true;\n    }\n  }\n}\n\n/**\n * ComponentFixture behavior that actually attaches the component to the application to ensure\n * behaviors between fixture and application do not diverge. `detectChanges` is disabled by default\n * (instead, tests should wait for the scheduler to detect changes), `whenStable` is directly the\n * `ApplicationRef.isStable`, and `autoDetectChanges` cannot be disabled.\n */\nexport class ScheduledComponentFixture<T> extends ComponentFixture<T> {\n  private readonly disableDetectChangesError =\n      inject(AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs, {optional: true}) ?? false;\n  private readonly pendingTasks = inject(PendingTasks);\n\n  initialize(): void {\n    this._appRef.attachView(this.componentRef.hostView);\n  }\n\n  override detectChanges(checkNoChanges: boolean = true): void {\n    if (!this.disableDetectChangesError) {\n      throw new Error(\n          'Do not use `detectChanges` directly when using zoneless change detection.' +\n              ' Instead, wait for the next render or `fixture.whenStable`.',\n      );\n    } else if (!checkNoChanges) {\n      throw new Error(\n          'Cannot disable `checkNoChanges` in this configuration. ' +\n              'Use `fixture.componentRef.hostView.changeDetectorRef.detectChanges()` instead.',\n      );\n    }\n    this._effectRunner.flush();\n    this._appRef.tick();\n    this._effectRunner.flush();\n  }\n\n  override isStable(): boolean {\n    return !this.pendingTasks.hasPendingTasks.value;\n  }\n\n  override whenStable(): Promise<boolean> {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    }\n    return this._appRef.isStable.pipe(first((stable) => stable)).toPromise().then(() => true);\n  }\n\n  override autoDetectChanges(autoDetect?: boolean|undefined): void {\n    throw new Error('Cannot call autoDetectChanges when using change detection scheduling.');\n  }\n}\n\ninterface TestAppRef {\n  externalTestViews: Set<ViewRef>;\n  beforeRender: Subject<boolean>;\n  afterTick: Subject<void>;\n}\n\n/**\n * ComponentFixture behavior that attempts to act as a \"mini application\".\n */\nexport class PseudoApplicationComponentFixture<T> extends ComponentFixture<T> {\n  private _subscriptions = new Subscription();\n  private _autoDetect = inject(ComponentFixtureAutoDetect, {optional: true}) ?? false;\n  private _isStable: boolean = true;\n  private _promise: Promise<boolean>|null = null;\n  private _resolve: ((result: boolean) => void)|null = null;\n  private afterTickSubscription: Subscription|undefined = undefined;\n  private beforeRenderSubscription: Subscription|undefined = undefined;\n\n  initialize(): void {\n    // Create subscriptions outside the NgZone so that the callbacks run outside\n    // of NgZone.\n    this._ngZone.runOutsideAngular(() => {\n      this._subscriptions.add(\n          this._ngZone.onUnstable.subscribe({\n            next: () => {\n              this._isStable = false;\n            },\n          }),\n      );\n      this._subscriptions.add(\n          this._ngZone.onMicrotaskEmpty.subscribe({\n            next: () => {\n              if (this._autoDetect) {\n                // Do a change detection run with checkNoChanges set to true to check\n                // there are no changes on the second run.\n                this.detectChanges(true);\n              }\n            },\n          }),\n      );\n      this._subscriptions.add(\n          this._ngZone.onStable.subscribe({\n            next: () => {\n              this._isStable = true;\n              // Check whether there is a pending whenStable() completer to resolve.\n              if (this._promise !== null) {\n                // If so check whether there are no pending macrotasks before resolving.\n                // Do this check in the next tick so that ngZone gets a chance to update the state\n                // of pending macrotasks.\n                queueMicrotask(() => {\n                  if (!this._ngZone.hasPendingMacrotasks) {\n                    if (this._promise !== null) {\n                      this._resolve!(true);\n                      this._resolve = null;\n                      this._promise = null;\n                    }\n                  }\n                });\n              }\n            },\n          }),\n      );\n\n      this._subscriptions.add(\n          this._ngZone.onError.subscribe({\n            next: (error: any) => {\n              throw error;\n            },\n          }),\n      );\n    });\n  }\n\n  override detectChanges(checkNoChanges = true): void {\n    this._effectRunner.flush();\n    // Run the change detection inside the NgZone so that any async tasks as part of the change\n    // detection are captured by the zone and can be waited for in isStable.\n    this._ngZone.run(() => {\n      this.changeDetectorRef.detectChanges();\n      if (checkNoChanges) {\n        this.checkNoChanges();\n      }\n    });\n    // Run any effects that were created/dirtied during change detection. Such effects might become\n    // dirty in response to input signals changing.\n    this._effectRunner.flush();\n  }\n\n  override isStable(): boolean {\n    return this._isStable && !this._ngZone.hasPendingMacrotasks;\n  }\n\n  override whenStable(): Promise<boolean> {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    } else if (this._promise !== null) {\n      return this._promise;\n    } else {\n      this._promise = new Promise((res) => {\n        this._resolve = res;\n      });\n      return this._promise;\n    }\n  }\n\n  override autoDetectChanges(autoDetect = true): void {\n    if (this._noZoneOptionIsSet) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n    }\n\n    this._autoDetect = autoDetect;\n    this.detectChanges();\n  }\n\n  override destroy(): void {\n    this._subscriptions.unsubscribe();\n    super.destroy();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst _Zone: any = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\n\nconst fakeAsyncTestModuleNotLoadedErrorMessage =\n    `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nexport function resetFakeAsyncZone(): void {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\nexport function resetFakeAsyncZoneIfExists(): void {\n  if (fakeAsyncTestModule) {\n    fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n}\n\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * If there are any pending timers at the end of the function, an exception is thrown.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nexport function fakeAsync(fn: Function): (...args: any[]) => any {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.fakeAsync(fn);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nexport function tick(\n    millis: number = 0, tickOptions: {processNewMacroTasksSynchronously: boolean} = {\n      processNewMacroTasksSynchronously: true\n    }): void {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.tick(millis, tickOptions);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nexport function flush(maxTurns?: number): number {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flush(maxTurns);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nexport function discardPeriodicTasks(): void {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.discardPeriodicTasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nexport function flushMicrotasks(): void {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flushMicrotasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵstringify as stringify} from '@angular/core';\n\nimport {MetadataOverride} from './metadata_override';\n\ntype StringMap = {\n  [key: string]: any\n};\n\nlet _nextReferenceId = 0;\n\nexport class MetadataOverrider {\n  private _references = new Map<any, string>();\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata<C extends T, T>(\n      metadataClass: {new(options: T): C;}, oldMetadata: C, override: MetadataOverride<T>): C {\n    const props: StringMap = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach((prop) => props[prop] = (<any>oldMetadata)[prop]);\n    }\n\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${stringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(<any>props);\n  }\n}\n\nfunction removeMetadata(metadata: StringMap, remove: any, references: Map<any, string>) {\n  const removeObjects = new Set<string>();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (Array.isArray(removeValue)) {\n      removeValue.forEach((value: any) => {\n        removeObjects.add(_propHashKey(prop, value, references));\n      });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (Array.isArray(propValue)) {\n      metadata[prop] = propValue.filter(\n          (value: any) => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\n\nfunction addMetadata(metadata: StringMap, add: any) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && Array.isArray(propValue)) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\n\nfunction setMetadata(metadata: StringMap, set: any) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\n\nfunction _propHashKey(propName: any, propValue: any, references: Map<any, string>): string {\n  let nextObjectId = 0;\n  const objectIds = new Map<object, string>();\n  const replacer = (key: any, value: any) => {\n    if (value !== null && typeof value === 'object') {\n      if (objectIds.has(value)) {\n        return objectIds.get(value);\n      }\n      // Record an id for this object such that any later references use the object's id instead\n      // of the object itself, in order to break cyclic pointers in objects.\n      objectIds.set(value, `ɵobj#${nextObjectId++}`);\n\n      // The first time an object is seen the object itself is serialized.\n      return value;\n    } else if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\n\nfunction _serializeReference(ref: any, references: Map<any, string>): string {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${stringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\n\n\nfunction _valueProps(obj: any): string[] {\n  const props: string[] = [];\n  // regular public props\n  Object.keys(obj).forEach((prop) => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach((protoProp) => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Component, Directive, NgModule, Pipe, Type, ɵReflectionCapabilities as ReflectionCapabilities} from '@angular/core';\n\nimport {MetadataOverride} from './metadata_override';\nimport {MetadataOverrider} from './metadata_overrider';\n\nconst reflection = new ReflectionCapabilities();\n\n/**\n * Base interface to resolve `@Component`, `@Directive`, `@Pipe` and `@NgModule`.\n */\nexport interface Resolver<T> {\n  addOverride(type: Type<any>, override: MetadataOverride<T>): void;\n  setOverrides(overrides: Array<[Type<any>, MetadataOverride<T>]>): void;\n  resolve(type: Type<any>): T|null;\n}\n\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nabstract class OverrideResolver<T> implements Resolver<T> {\n  private overrides = new Map<Type<any>, MetadataOverride<T>[]>();\n  private resolved = new Map<Type<any>, T|null>();\n\n  abstract get type(): any;\n\n  addOverride(type: Type<any>, override: MetadataOverride<T>) {\n    const overrides = this.overrides.get(type) || [];\n    overrides.push(override);\n    this.overrides.set(type, overrides);\n    this.resolved.delete(type);\n  }\n\n  setOverrides(overrides: Array<[Type<any>, MetadataOverride<T>]>) {\n    this.overrides.clear();\n    overrides.forEach(([type, override]) => {\n      this.addOverride(type, override);\n    });\n  }\n\n  getAnnotation(type: Type<any>): T|null {\n    const annotations = reflection.annotations(type);\n    // Try to find the nearest known Type annotation and make sure that this annotation is an\n    // instance of the type we are looking for, so we can use it for resolution. Note: there might\n    // be multiple known annotations found due to the fact that Components can extend Directives (so\n    // both Directive and Component annotations would be present), so we always check if the known\n    // annotation has the right type.\n    for (let i = annotations.length - 1; i >= 0; i--) {\n      const annotation = annotations[i];\n      const isKnownType = annotation instanceof Directive || annotation instanceof Component ||\n          annotation instanceof Pipe || annotation instanceof NgModule;\n      if (isKnownType) {\n        return annotation instanceof this.type ? annotation as unknown as T : null;\n      }\n    }\n    return null;\n  }\n\n  resolve(type: Type<any>): T|null {\n    let resolved: T|null = this.resolved.get(type) || null;\n\n    if (!resolved) {\n      resolved = this.getAnnotation(type);\n      if (resolved) {\n        const overrides = this.overrides.get(type);\n        if (overrides) {\n          const overrider = new MetadataOverrider();\n          overrides.forEach(override => {\n            resolved = overrider.overrideMetadata(this.type, resolved!, override);\n          });\n        }\n      }\n      this.resolved.set(type, resolved);\n    }\n\n    return resolved;\n  }\n}\n\n\nexport class DirectiveResolver extends OverrideResolver<Directive> {\n  override get type() {\n    return Directive;\n  }\n}\n\nexport class ComponentResolver extends OverrideResolver<Component> {\n  override get type() {\n    return Component;\n  }\n}\n\nexport class PipeResolver extends OverrideResolver<Pipe> {\n  override get type() {\n    return Pipe;\n  }\n}\n\nexport class NgModuleResolver extends OverrideResolver<NgModule> {\n  override get type() {\n    return NgModule;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ResourceLoader} from '@angular/compiler';\nimport {ApplicationInitStatus, Compiler, COMPILER_OPTIONS, Component, Directive, Injector, InjectorType, LOCALE_ID, ModuleWithComponentFactories, ModuleWithProviders, NgModule, NgModuleFactory, NgZone, Pipe, PlatformRef, Provider, provideZoneChangeDetection, resolveForwardRef, StaticProvider, Type, ɵclearResolutionOfComponentResourcesQueue, ɵcompileComponent as compileComponent, ɵcompileDirective as compileDirective, ɵcompileNgModuleDefs as compileNgModuleDefs, ɵcompilePipe as compilePipe, ɵDEFAULT_LOCALE_ID as DEFAULT_LOCALE_ID, ɵDEFER_BLOCK_CONFIG as DEFER_BLOCK_CONFIG, ɵDeferBlockBehavior as DeferBlockBehavior, ɵdepsTracker as depsTracker, ɵDirectiveDef as DirectiveDef, ɵgenerateStandaloneInDeclarationsError, ɵgetAsyncClassMetadataFn as getAsyncClassMetadataFn, ɵgetInjectableDef as getInjectableDef, ɵInternalEnvironmentProviders as InternalEnvironmentProviders, ɵisComponentDefPendingResolution, ɵisEnvironmentProviders as isEnvironmentProviders, ɵNG_COMP_DEF as NG_COMP_DEF, ɵNG_DIR_DEF as NG_DIR_DEF, ɵNG_INJ_DEF as NG_INJ_DEF, ɵNG_MOD_DEF as NG_MOD_DEF, ɵNG_PIPE_DEF as NG_PIPE_DEF, ɵNgModuleFactory as R3NgModuleFactory, ɵNgModuleTransitiveScopes as NgModuleTransitiveScopes, ɵNgModuleType as NgModuleType, ɵpatchComponentDefWithScope as patchComponentDefWithScope, ɵRender3ComponentFactory as ComponentFactory, ɵRender3NgModuleRef as NgModuleRef, ɵresolveComponentResources, ɵrestoreComponentResolutionQueue, ɵsetLocaleId as setLocaleId, ɵtransitiveScopesFor as transitiveScopesFor, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT as USE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵɵInjectableDeclaration as InjectableDeclaration} from '@angular/core';\n\nimport {ComponentDef, ComponentType} from '../../src/render3';\n\nimport {MetadataOverride} from './metadata_override';\nimport {ComponentResolver, DirectiveResolver, NgModuleResolver, PipeResolver, Resolver} from './resolvers';\nimport {DEFER_BLOCK_DEFAULT_BEHAVIOR, TestModuleMetadata} from './test_bed_common';\n\nenum TestingModuleOverride {\n  DECLARATION,\n  OVERRIDE_TEMPLATE,\n}\n\nfunction isTestingModuleOverride(value: unknown): value is TestingModuleOverride {\n  return value === TestingModuleOverride.DECLARATION ||\n      value === TestingModuleOverride.OVERRIDE_TEMPLATE;\n}\n\nfunction assertNoStandaloneComponents(\n    types: Type<any>[], resolver: Resolver<any>, location: string) {\n  types.forEach(type => {\n    if (!getAsyncClassMetadataFn(type)) {\n      const component = resolver.resolve(type);\n      if (component && component.standalone) {\n        throw new Error(ɵgenerateStandaloneInDeclarationsError(type, location));\n      }\n    }\n  });\n}\n\n// Resolvers for Angular decorators\ntype Resolvers = {\n  module: Resolver<NgModule>,\n  component: Resolver<Directive>,\n  directive: Resolver<Component>,\n  pipe: Resolver<Pipe>,\n};\n\ninterface CleanupOperation {\n  fieldName: string;\n  object: any;\n  originalValue: unknown;\n}\n\nexport class TestBedCompiler {\n  private originalComponentResolutionQueue: Map<Type<any>, Component>|null = null;\n\n  // Testing module configuration\n  private declarations: Type<any>[] = [];\n  private imports: Type<any>[] = [];\n  private providers: Provider[] = [];\n  private schemas: any[] = [];\n\n  // Queues of components/directives/pipes that should be recompiled.\n  private pendingComponents = new Set<Type<any>>();\n  private pendingDirectives = new Set<Type<any>>();\n  private pendingPipes = new Set<Type<any>>();\n\n  // Set of components with async metadata, i.e. components with `@defer` blocks\n  // in their templates.\n  private componentsWithAsyncMetadata = new Set<Type<unknown>>();\n\n  // Keep track of all components and directives, so we can patch Providers onto defs later.\n  private seenComponents = new Set<Type<any>>();\n  private seenDirectives = new Set<Type<any>>();\n\n  // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n  private overriddenModules = new Set<NgModuleType<any>>();\n\n  // Store resolved styles for Components that have template overrides present and `styleUrls`\n  // defined at the same time.\n  private existingComponentStyles = new Map<Type<any>, string[]>();\n\n  private resolvers: Resolvers = initResolvers();\n\n  // Map of component type to an NgModule that declares it.\n  //\n  // There are a couple special cases:\n  // - for standalone components, the module scope value is `null`\n  // - when a component is declared in `TestBed.configureTestingModule()` call or\n  //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n  //   we use a special value from the `TestingModuleOverride` enum.\n  private componentToModuleScope = new Map<Type<any>, Type<any>|TestingModuleOverride|null>();\n\n  // Map that keeps initial version of component/directive/pipe defs in case\n  // we compile a Type again, thus overriding respective static fields. This is\n  // required to make sure we restore defs to their initial states between test runs.\n  // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n  // NgModule), store all of them in a map.\n  private initialNgDefs = new Map<Type<any>, Map<string, PropertyDescriptor|undefined>>();\n\n  // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n  // defs in case TestBed makes changes to the originals.\n  private defCleanupOps: CleanupOperation[] = [];\n\n  private _injector: Injector|null = null;\n  private compilerProviders: Provider[]|null = null;\n\n  private providerOverrides: Provider[] = [];\n  private rootProviderOverrides: Provider[] = [];\n  // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n  // module's provider list.\n  private providerOverridesByModule = new Map<InjectorType<any>, Provider[]>();\n  private providerOverridesByToken = new Map<any, Provider>();\n  private scopesWithOverriddenProviders = new Set<Type<any>>();\n\n  private testModuleType: NgModuleType<any>;\n  private testModuleRef: NgModuleRef<any>|null = null;\n\n  private deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n\n  constructor(private platform: PlatformRef, private additionalModuleTypes: Type<any>|Type<any>[]) {\n    class DynamicTestModule {}\n    this.testModuleType = DynamicTestModule as any;\n  }\n\n  setCompilerProviders(providers: Provider[]|null): void {\n    this.compilerProviders = providers;\n    this._injector = null;\n  }\n\n  configureTestingModule(moduleDef: TestModuleMetadata): void {\n    // Enqueue any compilation tasks for the directly declared component.\n    if (moduleDef.declarations !== undefined) {\n      // Verify that there are no standalone components\n      assertNoStandaloneComponents(\n          moduleDef.declarations, this.resolvers.component,\n          '\"TestBed.configureTestingModule\" call');\n      this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n      this.declarations.push(...moduleDef.declarations);\n    }\n\n    // Enqueue any compilation tasks for imported modules.\n    if (moduleDef.imports !== undefined) {\n      this.queueTypesFromModulesArray(moduleDef.imports);\n      this.imports.push(...moduleDef.imports);\n    }\n\n    if (moduleDef.providers !== undefined) {\n      this.providers.push(...moduleDef.providers);\n    }\n\n    if (moduleDef.schemas !== undefined) {\n      this.schemas.push(...moduleDef.schemas);\n    }\n\n    this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    if (USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n      depsTracker.clearScopeCacheFor(ngModule);\n    }\n    this.overriddenModules.add(ngModule as NgModuleType<any>);\n\n    // Compile the module right away.\n    this.resolvers.module.addOverride(ngModule, override);\n    const metadata = this.resolvers.module.resolve(ngModule);\n    if (metadata === null) {\n      throw invalidTypeError(ngModule.name, 'NgModule');\n    }\n\n    this.recompileNgModule(ngModule, metadata);\n\n    // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n    // new declarations or imported modules. Ingest any possible new types and add them to the\n    // current queue.\n    this.queueTypesFromModulesArray([ngModule]);\n  }\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this.verifyNoStandaloneFlagOverrides(component, override);\n    this.resolvers.component.addOverride(component, override);\n    this.pendingComponents.add(component);\n\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(component);\n  }\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this.verifyNoStandaloneFlagOverrides(directive, override);\n    this.resolvers.directive.addOverride(directive, override);\n    this.pendingDirectives.add(directive);\n  }\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this.verifyNoStandaloneFlagOverrides(pipe, override);\n    this.resolvers.pipe.addOverride(pipe, override);\n    this.pendingPipes.add(pipe);\n  }\n\n  private verifyNoStandaloneFlagOverrides(\n      type: Type<any>, override: MetadataOverride<Component|Directive|Pipe>) {\n    if (override.add?.hasOwnProperty('standalone') || override.set?.hasOwnProperty('standalone') ||\n        override.remove?.hasOwnProperty('standalone')) {\n      throw new Error(\n          `An override for the ${type.name} class has the \\`standalone\\` flag. ` +\n          `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n    }\n  }\n\n  overrideProvider(\n      token: any,\n      provider: {useFactory?: Function, useValue?: any, deps?: any[], multi?: boolean}): void {\n    let providerDef: Provider;\n    if (provider.useFactory !== undefined) {\n      providerDef = {\n        provide: token,\n        useFactory: provider.useFactory,\n        deps: provider.deps || [],\n        multi: provider.multi\n      };\n    } else if (provider.useValue !== undefined) {\n      providerDef = {provide: token, useValue: provider.useValue, multi: provider.multi};\n    } else {\n      providerDef = {provide: token};\n    }\n\n    const injectableDef: InjectableDeclaration<any>|null =\n        typeof token !== 'string' ? getInjectableDef(token) : null;\n    const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n    const overridesBucket =\n        providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n    overridesBucket.push(providerDef);\n\n    // Keep overrides grouped by token as well for fast lookups using token\n    this.providerOverridesByToken.set(token, providerDef);\n    if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n      const existingOverrides = this.providerOverridesByModule.get(providedIn);\n      if (existingOverrides !== undefined) {\n        existingOverrides.push(providerDef);\n      } else {\n        this.providerOverridesByModule.set(providedIn, [providerDef]);\n      }\n    }\n  }\n\n  overrideTemplateUsingTestingModule(type: Type<any>, template: string): void {\n    const def = (type as any)[NG_COMP_DEF];\n    const hasStyleUrls = (): boolean => {\n      const metadata = this.resolvers.component.resolve(type)! as Component;\n      return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n    };\n    const overrideStyleUrls = !!def && !ɵisComponentDefPendingResolution(type) && hasStyleUrls();\n\n    // In Ivy, compiling a component does not require knowing the module providing the\n    // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n    // overrideComponent. Important: overriding template requires full Component re-compilation,\n    // which may fail in case styleUrls are also present (thus Component is considered as required\n    // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n    // preserve current styles available on Component def and restore styles back once compilation\n    // is complete.\n    const override =\n        overrideStyleUrls ? {template, styles: [], styleUrls: [], styleUrl: undefined} : {template};\n    this.overrideComponent(type, {set: override});\n\n    if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n      this.existingComponentStyles.set(type, def.styles);\n    }\n\n    // Set the component's scope to be the testing module.\n    this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n  }\n\n  private async resolvePendingComponentsWithAsyncMetadata() {\n    if (this.componentsWithAsyncMetadata.size === 0) return;\n\n    const promises = [];\n    for (const component of this.componentsWithAsyncMetadata) {\n      const asyncMetadataFn = getAsyncClassMetadataFn(component);\n      if (asyncMetadataFn) {\n        promises.push(asyncMetadataFn());\n      }\n    }\n    this.componentsWithAsyncMetadata.clear();\n\n    const resolvedDeps = await Promise.all(promises);\n    const flatResolvedDeps = resolvedDeps.flat(2);\n    this.queueTypesFromModulesArray(flatResolvedDeps);\n\n    // Loaded standalone components might contain imports of NgModules\n    // with providers, make sure we override providers there too.\n    for (const component of flatResolvedDeps) {\n      this.applyProviderOverridesInScope(component);\n    }\n  }\n\n  async compileComponents(): Promise<void> {\n    this.clearComponentResolutionQueue();\n\n    // Wait for all async metadata for components that were\n    // overridden, we need resolved metadata to perform an override\n    // and re-compile a component.\n    await this.resolvePendingComponentsWithAsyncMetadata();\n\n    // Verify that there were no standalone components present in the `declarations` field\n    // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n    // to the logic in the `configureTestingModule` function, since at this point we have\n    // all async metadata resolved.\n    assertNoStandaloneComponents(\n        this.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n\n    // Run compilers for all queued types.\n    let needsAsyncResources = this.compileTypesSync();\n\n    // compileComponents() should not be async unless it needs to be.\n    if (needsAsyncResources) {\n      let resourceLoader: ResourceLoader;\n      let resolver = (url: string): Promise<string> => {\n        if (!resourceLoader) {\n          resourceLoader = this.injector.get(ResourceLoader);\n        }\n        return Promise.resolve(resourceLoader.get(url));\n      };\n      await ɵresolveComponentResources(resolver);\n    }\n  }\n\n  finalize(): NgModuleRef<any> {\n    // One last compile\n    this.compileTypesSync();\n\n    // Create the testing module itself.\n    this.compileTestModule();\n\n    this.applyTransitiveScopes();\n\n    this.applyProviderOverrides();\n\n    // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n    // Components have `styleUrls` fields defined and template override was requested.\n    this.patchComponentsWithExistingStyles();\n\n    // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n    // every component.\n    this.componentToModuleScope.clear();\n\n    const parentInjector = this.platform.injector;\n    this.testModuleRef = new NgModuleRef(this.testModuleType, parentInjector, []);\n\n    // ApplicationInitStatus.runInitializers() is marked @internal to core.\n    // Cast it to any before accessing it.\n    (this.testModuleRef.injector.get(ApplicationInitStatus) as any).runInitializers();\n\n    // Set locale ID after running app initializers, since locale information might be updated while\n    // running initializers. This is also consistent with the execution order while bootstrapping an\n    // app (see `packages/core/src/application_ref.ts` file).\n    const localeId = this.testModuleRef.injector.get(LOCALE_ID, DEFAULT_LOCALE_ID);\n    setLocaleId(localeId);\n\n    return this.testModuleRef;\n  }\n\n  /**\n   * @internal\n   */\n  _compileNgModuleSync(moduleType: Type<any>): void {\n    this.queueTypesFromModulesArray([moduleType]);\n    this.compileTypesSync();\n    this.applyProviderOverrides();\n    this.applyProviderOverridesInScope(moduleType);\n    this.applyTransitiveScopes();\n  }\n\n  /**\n   * @internal\n   */\n  async _compileNgModuleAsync(moduleType: Type<any>): Promise<void> {\n    this.queueTypesFromModulesArray([moduleType]);\n    await this.compileComponents();\n    this.applyProviderOverrides();\n    this.applyProviderOverridesInScope(moduleType);\n    this.applyTransitiveScopes();\n  }\n\n  /**\n   * @internal\n   */\n  _getModuleResolver(): Resolver<NgModule> {\n    return this.resolvers.module;\n  }\n\n  /**\n   * @internal\n   */\n  _getComponentFactories(moduleType: NgModuleType): ComponentFactory<any>[] {\n    return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n      const componentDef = (declaration as any).ɵcmp;\n      componentDef && factories.push(new ComponentFactory(componentDef, this.testModuleRef!));\n      return factories;\n    }, [] as ComponentFactory<any>[]);\n  }\n\n  private compileTypesSync(): boolean {\n    // Compile all queued components, directives, pipes.\n    let needsAsyncResources = false;\n    this.pendingComponents.forEach(declaration => {\n      if (getAsyncClassMetadataFn(declaration)) {\n        throw new Error(\n            `Component '${declaration.name}' has unresolved metadata. ` +\n            `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n      }\n\n      needsAsyncResources = needsAsyncResources || ɵisComponentDefPendingResolution(declaration);\n\n      const metadata = this.resolvers.component.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Component');\n      }\n\n      this.maybeStoreNgDef(NG_COMP_DEF, declaration);\n      if (USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        depsTracker.clearScopeCacheFor(declaration);\n      }\n      compileComponent(declaration, metadata);\n    });\n    this.pendingComponents.clear();\n\n    this.pendingDirectives.forEach(declaration => {\n      const metadata = this.resolvers.directive.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Directive');\n      }\n      this.maybeStoreNgDef(NG_DIR_DEF, declaration);\n      compileDirective(declaration, metadata);\n    });\n    this.pendingDirectives.clear();\n\n    this.pendingPipes.forEach(declaration => {\n      const metadata = this.resolvers.pipe.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Pipe');\n      }\n      this.maybeStoreNgDef(NG_PIPE_DEF, declaration);\n      compilePipe(declaration, metadata);\n    });\n    this.pendingPipes.clear();\n\n    return needsAsyncResources;\n  }\n\n  private applyTransitiveScopes(): void {\n    if (this.overriddenModules.size > 0) {\n      // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n      // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n      // collect all affected modules and reset scopes to force their re-calculation.\n      const testingModuleDef = (this.testModuleType as any)[NG_MOD_DEF];\n      const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n      if (affectedModules.size > 0) {\n        affectedModules.forEach(moduleType => {\n          if (!USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            this.storeFieldOfDefOnType(moduleType as any, NG_MOD_DEF, 'transitiveCompileScopes');\n            (moduleType as any)[NG_MOD_DEF].transitiveCompileScopes = null;\n          } else {\n            depsTracker.clearScopeCacheFor(moduleType);\n          }\n        });\n      }\n    }\n\n    const moduleToScope = new Map<Type<any>|TestingModuleOverride, NgModuleTransitiveScopes>();\n    const getScopeOfModule =\n        (moduleType: Type<any>|TestingModuleOverride): NgModuleTransitiveScopes => {\n          if (!moduleToScope.has(moduleType)) {\n            const isTestingModule = isTestingModuleOverride(moduleType);\n            const realType = isTestingModule ? this.testModuleType : moduleType as Type<any>;\n            moduleToScope.set(moduleType, transitiveScopesFor(realType));\n          }\n          return moduleToScope.get(moduleType)!;\n        };\n\n    this.componentToModuleScope.forEach((moduleType, componentType) => {\n      if (moduleType !== null) {\n        const moduleScope = getScopeOfModule(moduleType);\n        this.storeFieldOfDefOnType(componentType, NG_COMP_DEF, 'directiveDefs');\n        this.storeFieldOfDefOnType(componentType, NG_COMP_DEF, 'pipeDefs');\n        patchComponentDefWithScope(getComponentDef(componentType)!, moduleScope);\n      }\n      // `tView` that is stored on component def contains information about directives and pipes\n      // that are in the scope of this component. Patching component scope will cause `tView` to be\n      // changed. Store original `tView` before patching scope, so the `tView` (including scope\n      // information) is restored back to its previous/original state before running next test.\n      // Resetting `tView` is also needed for cases when we apply provider overrides and those\n      // providers are defined on component's level, in which case they may end up included into\n      // `tView.blueprint`.\n      this.storeFieldOfDefOnType(componentType, NG_COMP_DEF, 'tView');\n    });\n\n    this.componentToModuleScope.clear();\n  }\n\n  private applyProviderOverrides(): void {\n    const maybeApplyOverrides = (field: string) => (type: Type<any>) => {\n      const resolver = field === NG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n      const metadata = resolver.resolve(type)!;\n      if (this.hasProviderOverrides(metadata.providers)) {\n        this.patchDefWithProviderOverrides(type, field);\n      }\n    };\n    this.seenComponents.forEach(maybeApplyOverrides(NG_COMP_DEF));\n    this.seenDirectives.forEach(maybeApplyOverrides(NG_DIR_DEF));\n\n    this.seenComponents.clear();\n    this.seenDirectives.clear();\n  }\n\n\n  /**\n   * Applies provider overrides to a given type (either an NgModule or a standalone component)\n   * and all imported NgModules and standalone components recursively.\n   */\n  private applyProviderOverridesInScope(type: Type<any>): void {\n    const hasScope = isStandaloneComponent(type) || isNgModule(type);\n\n    // The function can be re-entered recursively while inspecting dependencies\n    // of an NgModule or a standalone component. Exit early if we come across a\n    // type that can not have a scope (directive or pipe) or the type is already\n    // processed earlier.\n    if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n      return;\n    }\n    this.scopesWithOverriddenProviders.add(type);\n\n    // NOTE: the line below triggers JIT compilation of the module injector,\n    // which also invokes verification of the NgModule semantics, which produces\n    // detailed error messages. The fact that the code relies on this line being\n    // present here is suspicious and should be refactored in a way that the line\n    // below can be moved (for ex. after an early exit check below).\n    const injectorDef: any = (type as any)[NG_INJ_DEF];\n\n    // No provider overrides, exit early.\n    if (this.providerOverridesByToken.size === 0) return;\n\n    if (isStandaloneComponent(type)) {\n      // Visit all component dependencies and override providers there.\n      const def = getComponentDef(type);\n      const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n      for (const dependency of dependencies) {\n        this.applyProviderOverridesInScope(dependency);\n      }\n    } else {\n      const providers: Array<Provider|InternalEnvironmentProviders> = [\n        ...injectorDef.providers,\n        ...(this.providerOverridesByModule.get(type as InjectorType<any>) || [])\n      ];\n      if (this.hasProviderOverrides(providers)) {\n        this.maybeStoreNgDef(NG_INJ_DEF, type);\n\n        this.storeFieldOfDefOnType(type, NG_INJ_DEF, 'providers');\n        injectorDef.providers = this.getOverriddenProviders(providers);\n      }\n\n      // Apply provider overrides to imported modules recursively\n      const moduleDef = (type as any)[NG_MOD_DEF];\n      const imports = maybeUnwrapFn(moduleDef.imports);\n      for (const importedModule of imports) {\n        this.applyProviderOverridesInScope(importedModule);\n      }\n      // Also override the providers on any ModuleWithProviders imports since those don't appear in\n      // the moduleDef.\n      for (const importedModule of flatten(injectorDef.imports)) {\n        if (isModuleWithProviders(importedModule)) {\n          this.defCleanupOps.push({\n            object: importedModule,\n            fieldName: 'providers',\n            originalValue: importedModule.providers\n          });\n          importedModule.providers = this.getOverriddenProviders(\n              importedModule.providers as Array<Provider|InternalEnvironmentProviders>);\n        }\n      }\n    }\n  }\n\n  private patchComponentsWithExistingStyles(): void {\n    this.existingComponentStyles.forEach(\n        (styles, type) => (type as any)[NG_COMP_DEF].styles = styles);\n    this.existingComponentStyles.clear();\n  }\n\n  private queueTypeArray(arr: any[], moduleType: Type<any>|TestingModuleOverride): void {\n    for (const value of arr) {\n      if (Array.isArray(value)) {\n        this.queueTypeArray(value, moduleType);\n      } else {\n        this.queueType(value, moduleType);\n      }\n    }\n  }\n\n  private recompileNgModule(ngModule: Type<any>, metadata: NgModule): void {\n    // Cache the initial ngModuleDef as it will be overwritten.\n    this.maybeStoreNgDef(NG_MOD_DEF, ngModule);\n    this.maybeStoreNgDef(NG_INJ_DEF, ngModule);\n\n    compileNgModuleDefs(ngModule as NgModuleType<any>, metadata);\n  }\n\n  private maybeRegisterComponentWithAsyncMetadata(type: Type<unknown>) {\n    const asyncMetadataFn = getAsyncClassMetadataFn(type);\n    if (asyncMetadataFn) {\n      this.componentsWithAsyncMetadata.add(type);\n    }\n  }\n\n  private queueType(type: Type<any>, moduleType: Type<any>|TestingModuleOverride|null): void {\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(type);\n\n    const component = this.resolvers.component.resolve(type);\n    if (component) {\n      // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n      // missing. That might happen in case a class without any Angular decorators extends another\n      // class where Component/Directive/Pipe decorator is defined.\n      if (ɵisComponentDefPendingResolution(type) || !type.hasOwnProperty(NG_COMP_DEF)) {\n        this.pendingComponents.add(type);\n      }\n      this.seenComponents.add(type);\n\n      // Keep track of the module which declares this component, so later the component's scope\n      // can be set correctly. If the component has already been recorded here, then one of several\n      // cases is true:\n      // * the module containing the component was imported multiple times (common).\n      // * the component is declared in multiple modules (which is an error).\n      // * the component was in 'declarations' of the testing module, and also in an imported module\n      //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n      // * overrideTemplateUsingTestingModule was called for the component in which case the module\n      //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n      //\n      // If the component was previously in the testing module's 'declarations' (meaning the\n      // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n      // real module, which was imported. This pattern is understood to mean that the component\n      // should use its original scope, but that the testing module should also contain the\n      // component in its scope.\n      if ((!this.componentToModuleScope.has(type) ||\n           this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION)) {\n        this.componentToModuleScope.set(type, moduleType);\n      }\n      return;\n    }\n\n    const directive = this.resolvers.directive.resolve(type);\n    if (directive) {\n      if (!type.hasOwnProperty(NG_DIR_DEF)) {\n        this.pendingDirectives.add(type);\n      }\n      this.seenDirectives.add(type);\n      return;\n    }\n\n    const pipe = this.resolvers.pipe.resolve(type);\n    if (pipe && !type.hasOwnProperty(NG_PIPE_DEF)) {\n      this.pendingPipes.add(type);\n      return;\n    }\n  }\n\n  private queueTypesFromModulesArray(arr: any[]): void {\n    // Because we may encounter the same NgModule or a standalone Component while processing\n    // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n    // can skip ones that have already been seen encountered. In some test setups, this caching\n    // resulted in 10X runtime improvement.\n    const processedDefs = new Set();\n    const queueTypesFromModulesArrayRecur = (arr: any[]): void => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          queueTypesFromModulesArrayRecur(value);\n        } else if (hasNgModuleDef(value)) {\n          const def = value.ɵmod;\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          // Look through declarations, imports, and exports, and queue\n          // everything found there.\n          this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n        } else if (isModuleWithProviders(value)) {\n          queueTypesFromModulesArrayRecur([value.ngModule]);\n        } else if (isStandaloneComponent(value)) {\n          this.queueType(value, null);\n          const def = getComponentDef(value);\n\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n\n          const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n          dependencies.forEach((dependency) => {\n            // Note: in AOT, the `dependencies` might also contain regular\n            // (NgModule-based) Component, Directive and Pipes, so we handle\n            // them separately and proceed with recursive process for standalone\n            // Components and NgModules only.\n            if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n              queueTypesFromModulesArrayRecur([dependency]);\n            } else {\n              this.queueType(dependency, null);\n            }\n          });\n        }\n      }\n    };\n    queueTypesFromModulesArrayRecur(arr);\n  }\n\n  // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n  // that import (even transitively) an overridden one. For all affected modules we need to\n  // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n  // of this function is to collect all affected modules in a set for further processing. Example:\n  // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n  // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n  // invalidated with the override.\n  private collectModulesAffectedByOverrides(arr: any[]): Set<NgModuleType<any>> {\n    const seenModules = new Set<NgModuleType<any>>();\n    const affectedModules = new Set<NgModuleType<any>>();\n    const calcAffectedModulesRecur = (arr: any[], path: NgModuleType<any>[]): void => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          // If the value is an array, just flatten it (by invoking this function recursively),\n          // keeping \"path\" the same.\n          calcAffectedModulesRecur(value, path);\n        } else if (hasNgModuleDef(value)) {\n          if (seenModules.has(value)) {\n            // If we've seen this module before and it's included into \"affected modules\" list, mark\n            // the whole path that leads to that module as affected, but do not descend into its\n            // imports, since we already examined them before.\n            if (affectedModules.has(value)) {\n              path.forEach(item => affectedModules.add(item));\n            }\n            continue;\n          }\n          seenModules.add(value);\n          if (this.overriddenModules.has(value)) {\n            path.forEach(item => affectedModules.add(item));\n          }\n          // Examine module imports recursively to look for overridden modules.\n          const moduleDef = (value as any)[NG_MOD_DEF];\n          calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n        }\n      }\n    };\n    calcAffectedModulesRecur(arr, []);\n    return affectedModules;\n  }\n\n  /**\n   * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n   * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n   * an NgModule). If there is a def in a set already, don't override it, since\n   * an original one should be restored at the end of a test.\n   */\n  private maybeStoreNgDef(prop: string, type: Type<any>) {\n    if (!this.initialNgDefs.has(type)) {\n      this.initialNgDefs.set(type, new Map());\n    }\n    const currentDefs = this.initialNgDefs.get(type)!;\n    if (!currentDefs.has(prop)) {\n      const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n      currentDefs.set(prop, currentDef);\n    }\n  }\n\n  private storeFieldOfDefOnType(type: Type<any>, defField: string, fieldName: string): void {\n    const def: any = (type as any)[defField];\n    const originalValue: any = def[fieldName];\n    this.defCleanupOps.push({object: def, fieldName, originalValue});\n  }\n\n  /**\n   * Clears current components resolution queue, but stores the state of the queue, so we can\n   * restore it later. Clearing the queue is required before we try to compile components (via\n   * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n   */\n  private clearComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue === null) {\n      this.originalComponentResolutionQueue = new Map();\n    }\n    ɵclearResolutionOfComponentResourcesQueue().forEach(\n        (value, key) => this.originalComponentResolutionQueue!.set(key, value));\n  }\n\n  /*\n   * Restores component resolution queue to the previously saved state. This operation is performed\n   * as a part of restoring the state after completion of the current set of tests (that might\n   * potentially mutate the state).\n   */\n  private restoreComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue !== null) {\n      ɵrestoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n      this.originalComponentResolutionQueue = null;\n    }\n  }\n\n  restoreOriginalState(): void {\n    // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n    // case there were multiple overrides for the same field).\n    forEachRight(this.defCleanupOps, (op: CleanupOperation) => {\n      op.object[op.fieldName] = op.originalValue;\n    });\n    // Restore initial component/directive/pipe defs\n    this.initialNgDefs.forEach(\n        (defs: Map<string, PropertyDescriptor|undefined>, type: Type<any>) => {\n          if (USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            depsTracker.clearScopeCacheFor(type);\n          }\n          defs.forEach((descriptor, prop) => {\n            if (!descriptor) {\n              // Delete operations are generally undesirable since they have performance\n              // implications on objects they were applied to. In this particular case, situations\n              // where this code is invoked should be quite rare to cause any noticeable impact,\n              // since it's applied only to some test cases (for example when class with no\n              // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n              // class to restore its original state (before applying overrides and running tests).\n              delete (type as any)[prop];\n            } else {\n              Object.defineProperty(type, prop, descriptor);\n            }\n          });\n        });\n    this.initialNgDefs.clear();\n    this.scopesWithOverriddenProviders.clear();\n    this.restoreComponentResolutionQueue();\n    // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n    setLocaleId(DEFAULT_LOCALE_ID);\n  }\n\n  private compileTestModule(): void {\n    class RootScopeModule {}\n    compileNgModuleDefs(RootScopeModule as NgModuleType<any>, {\n      providers: [...this.rootProviderOverrides],\n    });\n\n    const providers = [\n      provideZoneChangeDetection(),\n      {provide: Compiler, useFactory: () => new R3TestCompiler(this)},\n      {provide: DEFER_BLOCK_CONFIG, useValue: {behavior: this.deferBlockBehavior}},\n      ...this.providers,\n      ...this.providerOverrides,\n    ];\n    const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n\n    // clang-format off\n    compileNgModuleDefs(this.testModuleType, {\n      declarations: this.declarations,\n      imports,\n      schemas: this.schemas,\n      providers,\n    }, /* allowDuplicateDeclarationsInRoot */ true);\n    // clang-format on\n\n    this.applyProviderOverridesInScope(this.testModuleType);\n  }\n\n  get injector(): Injector {\n    if (this._injector !== null) {\n      return this._injector;\n    }\n\n    const providers: StaticProvider[] = [];\n    const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS);\n    compilerOptions.forEach(opts => {\n      if (opts.providers) {\n        providers.push(opts.providers);\n      }\n    });\n    if (this.compilerProviders !== null) {\n      providers.push(...this.compilerProviders as StaticProvider[]);\n    }\n\n    this._injector = Injector.create({providers, parent: this.platform.injector});\n    return this._injector;\n  }\n\n  // get overrides for a specific provider (if any)\n  private getSingleProviderOverrides(provider: Provider): Provider|null {\n    const token = getProviderToken(provider);\n    return this.providerOverridesByToken.get(token) || null;\n  }\n\n  private getProviderOverrides(providers?: Array<Provider|InternalEnvironmentProviders>):\n      Provider[] {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    // There are two flattening operations here. The inner flattenProviders() operates on the\n    // metadata's providers and applies a mapping function which retrieves overrides for each\n    // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n    // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n    // providers array and contaminate any error messages that might be generated.\n    return flatten(flattenProviders(\n        providers, (provider: Provider) => this.getSingleProviderOverrides(provider) || []));\n  }\n\n  private getOverriddenProviders(providers?: Array<Provider|InternalEnvironmentProviders>):\n      Provider[] {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n\n    const flattenedProviders = flattenProviders(providers);\n    const overrides = this.getProviderOverrides(flattenedProviders);\n    const overriddenProviders = [...flattenedProviders, ...overrides];\n    const final: Provider[] = [];\n    const seenOverriddenProviders = new Set<Provider>();\n\n    // We iterate through the list of providers in reverse order to make sure provider overrides\n    // take precedence over the values defined in provider list. We also filter out all providers\n    // that have overrides, keeping overridden values only. This is needed, since presence of a\n    // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n    forEachRight(overriddenProviders, (provider: any) => {\n      const token: any = getProviderToken(provider);\n      if (this.providerOverridesByToken.has(token)) {\n        if (!seenOverriddenProviders.has(token)) {\n          seenOverriddenProviders.add(token);\n          // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n          // make sure that provided override takes highest precedence and is not combined with\n          // other instances of the same multi provider.\n          final.unshift({...provider, multi: false});\n        }\n      } else {\n        final.unshift(provider);\n      }\n    });\n    return final;\n  }\n\n  private hasProviderOverrides(providers?: Array<Provider|InternalEnvironmentProviders>): boolean {\n    return this.getProviderOverrides(providers).length > 0;\n  }\n\n  private patchDefWithProviderOverrides(declaration: Type<any>, field: string): void {\n    const def = (declaration as any)[field];\n    if (def && def.providersResolver) {\n      this.maybeStoreNgDef(field, declaration);\n\n      const resolver = def.providersResolver;\n      const processProvidersFn = (providers: Provider[]) => this.getOverriddenProviders(providers);\n      this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n      def.providersResolver = (ngDef: DirectiveDef<any>) => resolver(ngDef, processProvidersFn);\n    }\n  }\n}\n\nfunction initResolvers(): Resolvers {\n  return {\n    module: new NgModuleResolver(),\n    component: new ComponentResolver(),\n    directive: new DirectiveResolver(),\n    pipe: new PipeResolver()\n  };\n}\n\nfunction isStandaloneComponent<T>(value: Type<T>): value is ComponentType<T> {\n  const def = getComponentDef(value);\n  return !!def?.standalone;\n}\n\nfunction getComponentDef(value: ComponentType<unknown>): ComponentDef<unknown>;\nfunction getComponentDef(value: Type<unknown>): ComponentDef<unknown>|null;\nfunction getComponentDef(value: Type<unknown>): ComponentDef<unknown>|null {\n  return (value as any).ɵcmp ?? null;\n}\n\nfunction hasNgModuleDef<T>(value: Type<T>): value is NgModuleType<T> {\n  return value.hasOwnProperty('ɵmod');\n}\n\nfunction isNgModule<T>(value: Type<T>): boolean {\n  return hasNgModuleDef(value);\n}\n\nfunction maybeUnwrapFn<T>(maybeFn: (() => T)|T): T {\n  return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\n\nfunction flatten<T>(values: any[]): T[] {\n  const out: T[] = [];\n  values.forEach(value => {\n    if (Array.isArray(value)) {\n      out.push(...flatten<T>(value));\n    } else {\n      out.push(value);\n    }\n  });\n  return out;\n}\n\nfunction identityFn<T>(value: T): T {\n  return value;\n}\n\nfunction flattenProviders<T>(\n    providers: Array<Provider|InternalEnvironmentProviders>, mapFn: (provider: Provider) => T): T[];\nfunction flattenProviders(providers: Array<Provider|InternalEnvironmentProviders>): Provider[];\nfunction flattenProviders(\n    providers: Array<Provider|InternalEnvironmentProviders>,\n    mapFn: (provider: Provider) => any = identityFn): any[] {\n  const out: any[] = [];\n  for (let provider of providers) {\n    if (isEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      out.push(...flattenProviders(provider, mapFn));\n    } else {\n      out.push(mapFn(provider));\n    }\n  }\n  return out;\n}\n\nfunction getProviderField(provider: Provider, field: string) {\n  return provider && typeof provider === 'object' && (provider as any)[field];\n}\n\nfunction getProviderToken(provider: Provider) {\n  return getProviderField(provider, 'provide') || provider;\n}\n\nfunction isModuleWithProviders(value: any): value is ModuleWithProviders<any> {\n  return value.hasOwnProperty('ngModule');\n}\n\nfunction forEachRight<T>(values: T[], fn: (value: T, idx: number) => void): void {\n  for (let idx = values.length - 1; idx >= 0; idx--) {\n    fn(values[idx], idx);\n  }\n}\n\nfunction invalidTypeError(name: string, expectedType: string): Error {\n  return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\n\nclass R3TestCompiler implements Compiler {\n  constructor(private testBed: TestBedCompiler) {}\n\n  compileModuleSync<T>(moduleType: Type<T>): NgModuleFactory<T> {\n    this.testBed._compileNgModuleSync(moduleType);\n    return new R3NgModuleFactory(moduleType);\n  }\n\n  async compileModuleAsync<T>(moduleType: Type<T>): Promise<NgModuleFactory<T>> {\n    await this.testBed._compileNgModuleAsync(moduleType);\n    return new R3NgModuleFactory(moduleType);\n  }\n\n  compileModuleAndAllComponentsSync<T>(moduleType: Type<T>): ModuleWithComponentFactories<T> {\n    const ngModuleFactory = this.compileModuleSync(moduleType);\n    const componentFactories = this.testBed._getComponentFactories(moduleType as NgModuleType<T>);\n    return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n  }\n\n  async compileModuleAndAllComponentsAsync<T>(moduleType: Type<T>):\n      Promise<ModuleWithComponentFactories<T>> {\n    const ngModuleFactory = await this.compileModuleAsync(moduleType);\n    const componentFactories = this.testBed._getComponentFactories(moduleType as NgModuleType<T>);\n    return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n  }\n\n  clearCache(): void {}\n\n  clearCacheFor(type: Type<any>): void {}\n\n  getModuleId(moduleType: Type<any>): string|undefined {\n    const meta = this.testBed._getModuleResolver().resolve(moduleType);\n    return meta && meta.id || undefined;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\n// it on one line, too, which has gotten very hard to read & manage. So disable the formatter for\n// this statement only.\n\n/* clang-format off */\nimport {\n  Component,\n  ComponentRef,\n  Directive,\n  EnvironmentInjector,\n  InjectFlags,\n  InjectOptions,\n  Injector,\n  NgModule,\n  NgZone,\n  Pipe,\n  PlatformRef,\n  ProviderToken,\n  runInInjectionContext,\n  Type,\n  ɵChangeDetectionScheduler as ChangeDetectionScheduler,\n  ɵconvertToBitFlags as convertToBitFlags,\n  ɵDeferBlockBehavior as DeferBlockBehavior,\n  ɵEffectScheduler as EffectScheduler,\n  ɵflushModuleScopingQueueAsMuchAsPossible as flushModuleScopingQueueAsMuchAsPossible,\n  ɵgetAsyncClassMetadataFn as getAsyncClassMetadataFn,\n  ɵgetUnknownElementStrictMode as getUnknownElementStrictMode,\n  ɵgetUnknownPropertyStrictMode as getUnknownPropertyStrictMode,\n  ɵRender3ComponentFactory as ComponentFactory,\n  ɵRender3NgModuleRef as NgModuleRef,\n  ɵresetCompiledComponents as resetCompiledComponents,\n  ɵsetAllowDuplicateNgModuleIdsForTest as setAllowDuplicateNgModuleIdsForTest,\n  ɵsetUnknownElementStrictMode as setUnknownElementStrictMode,\n  ɵsetUnknownPropertyStrictMode as setUnknownPropertyStrictMode,\n  ɵstringify as stringify,\n} from '@angular/core';\n\n/* clang-format on */\n\n\n\nimport {ComponentFixture, PseudoApplicationComponentFixture, ScheduledComponentFixture} from './component_fixture';\nimport {MetadataOverride} from './metadata_override';\nimport {ComponentFixtureNoNgZone, DEFER_BLOCK_DEFAULT_BEHAVIOR, ModuleTeardownOptions, TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT, TestComponentRenderer, TestEnvironmentOptions, TestModuleMetadata, THROW_ON_UNKNOWN_ELEMENTS_DEFAULT, THROW_ON_UNKNOWN_PROPERTIES_DEFAULT} from './test_bed_common';\nimport {TestBedCompiler} from './test_bed_compiler';\n\n/**\n * Static methods implemented by the `TestBed`.\n *\n * @publicApi\n */\nexport interface TestBedStatic extends TestBed {\n  new(...args: any[]): TestBed;\n}\n\n/**\n * @publicApi\n */\nexport interface TestBed {\n  get platform(): PlatformRef;\n\n  get ngModule(): Type<any>|Type<any>[];\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   */\n  initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef,\n      options?: TestEnvironmentOptions): void;\n\n  /**\n   * Reset the providers for the test injector.\n   */\n  resetTestEnvironment(): void;\n\n  resetTestingModule(): TestBed;\n\n  configureCompiler(config: {providers?: any[], useJit?: boolean}): void;\n\n  configureTestingModule(moduleDef: TestModuleMetadata): TestBed;\n\n  compileComponents(): Promise<any>;\n\n  inject<T>(token: ProviderToken<T>, notFoundValue: undefined, options: InjectOptions&{\n    optional?: false\n  }): T;\n  inject<T>(token: ProviderToken<T>, notFoundValue: null|undefined, options: InjectOptions): T|null;\n  inject<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions): T;\n  /** @deprecated use object-based flags (`InjectOptions`) instead. */\n  inject<T>(token: ProviderToken<T>, notFoundValue?: T, flags?: InjectFlags): T;\n  /** @deprecated use object-based flags (`InjectOptions`) instead. */\n  inject<T>(token: ProviderToken<T>, notFoundValue: null, flags?: InjectFlags): T|null;\n\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get<T>(token: ProviderToken<T>, notFoundValue?: T, flags?: InjectFlags): any;\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get(token: any, notFoundValue?: any): any;\n\n  /**\n   * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n   *\n   * @see {@link EnvironmentInjector#runInContext}\n   */\n  runInInjectionContext<T>(fn: () => T): T;\n\n  execute(tokens: any[], fn: Function, context?: any): any;\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): TestBed;\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): TestBed;\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): TestBed;\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): TestBed;\n\n  overrideTemplate(component: Type<any>, template: string): TestBed;\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token: any, provider: {useFactory: Function, deps: any[], multi?: boolean}):\n      TestBed;\n  overrideProvider(token: any, provider: {useValue: any, multi?: boolean}): TestBed;\n  overrideProvider(\n      token: any,\n      provider: {useFactory?: Function, useValue?: any, deps?: any[], multi?: boolean}): TestBed;\n\n  overrideTemplateUsingTestingModule(component: Type<any>, template: string): TestBed;\n\n  createComponent<T>(component: Type<T>): ComponentFixture<T>;\n\n\n  /**\n   * Execute any pending effects.\n   *\n   * @developerPreview\n   */\n  flushEffects(): void;\n}\n\nlet _nextRootElementId = 0;\n\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nexport function getTestBed(): TestBed {\n  return TestBedImpl.INSTANCE;\n}\n\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nexport class TestBedImpl implements TestBed {\n  private static _INSTANCE: TestBedImpl|null = null;\n\n  static get INSTANCE(): TestBedImpl {\n    return TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl();\n  }\n\n  /**\n   * Teardown options that have been configured at the environment level.\n   * Used as a fallback if no instance-level options have been provided.\n   */\n  private static _environmentTeardownOptions: ModuleTeardownOptions|undefined;\n\n  /**\n   * \"Error on unknown elements\" option that has been configured at the environment level.\n   * Used as a fallback if no instance-level option has been provided.\n   */\n  private static _environmentErrorOnUnknownElementsOption: boolean|undefined;\n\n  /**\n   * \"Error on unknown properties\" option that has been configured at the environment level.\n   * Used as a fallback if no instance-level option has been provided.\n   */\n  private static _environmentErrorOnUnknownPropertiesOption: boolean|undefined;\n\n  /**\n   * Teardown options that have been configured at the `TestBed` instance level.\n   * These options take precedence over the environment-level ones.\n   */\n  private _instanceTeardownOptions: ModuleTeardownOptions|undefined;\n\n  /**\n   * Defer block behavior option that specifies whether defer blocks will be triggered manually\n   * or set to play through.\n   */\n  private _instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n\n  /**\n   * \"Error on unknown elements\" option that has been configured at the `TestBed` instance level.\n   * This option takes precedence over the environment-level one.\n   */\n  private _instanceErrorOnUnknownElementsOption: boolean|undefined;\n\n  /**\n   * \"Error on unknown properties\" option that has been configured at the `TestBed` instance level.\n   * This option takes precedence over the environment-level one.\n   */\n  private _instanceErrorOnUnknownPropertiesOption: boolean|undefined;\n\n  /**\n   * Stores the previous \"Error on unknown elements\" option value,\n   * allowing to restore it in the reset testing module logic.\n   */\n  private _previousErrorOnUnknownElementsOption: boolean|undefined;\n\n  /**\n   * Stores the previous \"Error on unknown properties\" option value,\n   * allowing to restore it in the reset testing module logic.\n   */\n  private _previousErrorOnUnknownPropertiesOption: boolean|undefined;\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  static initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef,\n      options?: TestEnvironmentOptions): TestBed {\n    const testBed = TestBedImpl.INSTANCE;\n    testBed.initTestEnvironment(ngModule, platform, options);\n    return testBed;\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  static resetTestEnvironment(): void {\n    TestBedImpl.INSTANCE.resetTestEnvironment();\n  }\n\n  static configureCompiler(config: {providers?: any[]; useJit?: boolean;}): TestBed {\n    return TestBedImpl.INSTANCE.configureCompiler(config);\n  }\n\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef: TestModuleMetadata): TestBed {\n    return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n  }\n\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents(): Promise<any> {\n    return TestBedImpl.INSTANCE.compileComponents();\n  }\n\n  static overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): TestBed {\n    return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n  }\n\n  static overrideComponent(component: Type<any>, override: MetadataOverride<Component>): TestBed {\n    return TestBedImpl.INSTANCE.overrideComponent(component, override);\n  }\n\n  static overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): TestBed {\n    return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n  }\n\n  static overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): TestBed {\n    return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n  }\n\n  static overrideTemplate(component: Type<any>, template: string): TestBed {\n    return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n  }\n\n  /**\n   * Overrides the template of the given component, compiling the template\n   * in the context of the TestingModule.\n   *\n   * Note: This works for JIT and AOTed components as well.\n   */\n  static overrideTemplateUsingTestingModule(component: Type<any>, template: string): TestBed {\n    return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n  }\n\n  static overrideProvider(token: any, provider: {\n    useFactory: Function,\n    deps: any[],\n  }): TestBed;\n  static overrideProvider(token: any, provider: {useValue: any;}): TestBed;\n  static overrideProvider(token: any, provider: {\n    useFactory?: Function,\n    useValue?: any,\n    deps?: any[],\n  }): TestBed {\n    return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n  }\n\n  static inject<T>(token: ProviderToken<T>, notFoundValue: undefined, options: InjectOptions&{\n    optional?: false\n  }): T;\n  static inject<T>(token: ProviderToken<T>, notFoundValue: null|undefined, options: InjectOptions):\n      T|null;\n  static inject<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions): T;\n  /** @deprecated use object-based flags (`InjectOptions`) instead. */\n  static inject<T>(token: ProviderToken<T>, notFoundValue?: T, flags?: InjectFlags): T;\n  /** @deprecated use object-based flags (`InjectOptions`) instead. */\n  static inject<T>(token: ProviderToken<T>, notFoundValue: null, flags?: InjectFlags): T|null;\n  static inject<T>(\n      token: ProviderToken<T>, notFoundValue?: T|null, flags?: InjectFlags|InjectOptions): T|null {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, convertToBitFlags(flags));\n  }\n\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  static get<T>(token: ProviderToken<T>, notFoundValue?: T, flags?: InjectFlags): any;\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  static get(token: any, notFoundValue?: any): any;\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  static get(\n      token: any, notFoundValue: any = Injector.THROW_IF_NOT_FOUND,\n      flags: InjectFlags = InjectFlags.Default): any {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n  }\n\n  /**\n   * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n   *\n   * @see {@link EnvironmentInjector#runInContext}\n   */\n  static runInInjectionContext<T>(fn: () => T): T {\n    return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n  }\n\n  static createComponent<T>(component: Type<T>): ComponentFixture<T> {\n    return TestBedImpl.INSTANCE.createComponent(component);\n  }\n\n  static resetTestingModule(): TestBed {\n    return TestBedImpl.INSTANCE.resetTestingModule();\n  }\n\n  static execute(tokens: any[], fn: Function, context?: any): any {\n    return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n  }\n\n  static get platform(): PlatformRef {\n    return TestBedImpl.INSTANCE.platform;\n  }\n\n  static get ngModule(): Type<any>|Type<any>[] {\n    return TestBedImpl.INSTANCE.ngModule;\n  }\n\n  static flushEffects(): void {\n    return TestBedImpl.INSTANCE.flushEffects();\n  }\n\n  // Properties\n\n  platform: PlatformRef = null!;\n  ngModule: Type<any>|Type<any>[] = null!;\n\n  private _compiler: TestBedCompiler|null = null;\n  private _testModuleRef: NgModuleRef<any>|null = null;\n\n  private _activeFixtures: ComponentFixture<any>[] = [];\n\n  /**\n   * Internal-only flag to indicate whether a module\n   * scoping queue has been checked and flushed already.\n   * @nodoc\n   */\n  globalCompilationChecked = false;\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  initTestEnvironment(\n      ngModule: Type<any>|Type<any>[], platform: PlatformRef,\n      options?: TestEnvironmentOptions): void {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n\n    TestBedImpl._environmentTeardownOptions = options?.teardown;\n\n    TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n\n    TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n\n    this.platform = platform;\n    this.ngModule = ngModule;\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n\n    // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n    // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n    // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n    // completely.\n    setAllowDuplicateNgModuleIdsForTest(true);\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  resetTestEnvironment(): void {\n    this.resetTestingModule();\n    this._compiler = null;\n    this.platform = null!;\n    this.ngModule = null!;\n    TestBedImpl._environmentTeardownOptions = undefined;\n    setAllowDuplicateNgModuleIdsForTest(false);\n  }\n\n  resetTestingModule(): this {\n    this.checkGlobalCompilationFinished();\n    resetCompiledComponents();\n    if (this._compiler !== null) {\n      this.compiler.restoreOriginalState();\n    }\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // Restore the previous value of the \"error on unknown elements\" option\n    setUnknownElementStrictMode(\n        this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n    // Restore the previous value of the \"error on unknown properties\" option\n    setUnknownPropertyStrictMode(\n        this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n\n    // We have to chain a couple of try/finally blocks, because each step can\n    // throw errors and we don't want it to interrupt the next step and we also\n    // want an error to be thrown at the end.\n    try {\n      this.destroyActiveFixtures();\n    } finally {\n      try {\n        if (this.shouldTearDownTestingModule()) {\n          this.tearDownTestingModule();\n        }\n      } finally {\n        this._testModuleRef = null;\n        this._instanceTeardownOptions = undefined;\n        this._instanceErrorOnUnknownElementsOption = undefined;\n        this._instanceErrorOnUnknownPropertiesOption = undefined;\n        this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n      }\n    }\n    return this;\n  }\n\n  configureCompiler(config: {providers?: any[]; useJit?: boolean;}): this {\n    if (config.useJit != null) {\n      throw new Error('JIT compiler is not configurable via TestBed APIs.');\n    }\n\n    if (config.providers !== undefined) {\n      this.compiler.setCompilerProviders(config.providers);\n    }\n    return this;\n  }\n\n  configureTestingModule(moduleDef: TestModuleMetadata): this {\n    this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n\n    // Trigger module scoping queue flush before executing other TestBed operations in a test.\n    // This is needed for the first test invocation to ensure that globally declared modules have\n    // their components scoped properly. See the `checkGlobalCompilationFinished` function\n    // description for additional info.\n    this.checkGlobalCompilationFinished();\n\n    // Always re-assign the options, even if they're undefined.\n    // This ensures that we don't carry them between tests.\n    this._instanceTeardownOptions = moduleDef.teardown;\n    this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n    this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n    this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    // Store the current value of the strict mode option,\n    // so we can restore it later\n    this._previousErrorOnUnknownElementsOption = getUnknownElementStrictMode();\n    setUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n    this._previousErrorOnUnknownPropertiesOption = getUnknownPropertyStrictMode();\n    setUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n    this.compiler.configureTestingModule(moduleDef);\n    return this;\n  }\n\n  compileComponents(): Promise<any> {\n    return this.compiler.compileComponents();\n  }\n\n  inject<T>(token: ProviderToken<T>, notFoundValue: undefined, options: InjectOptions&{\n    optional: true\n  }): T|null;\n  inject<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions): T;\n  inject<T>(token: ProviderToken<T>, notFoundValue: null, options?: InjectOptions): T|null;\n  /** @deprecated use object-based flags (`InjectOptions`) instead. */\n  inject<T>(token: ProviderToken<T>, notFoundValue?: T, flags?: InjectFlags): T;\n  /** @deprecated use object-based flags (`InjectOptions`) instead. */\n  inject<T>(token: ProviderToken<T>, notFoundValue: null, flags?: InjectFlags): T|null;\n  inject<T>(token: ProviderToken<T>, notFoundValue?: T|null, flags?: InjectFlags|InjectOptions): T\n      |null {\n    if (token as unknown === TestBed) {\n      return this as any;\n    }\n    const UNDEFINED = {} as unknown as T;\n    const result = this.testModuleRef.injector.get(token, UNDEFINED, convertToBitFlags(flags));\n    return result === UNDEFINED ? this.compiler.injector.get(token, notFoundValue, flags) as any :\n                                  result;\n  }\n\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get<T>(token: ProviderToken<T>, notFoundValue?: T, flags?: InjectFlags): any;\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get(token: any, notFoundValue?: any): any;\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get(token: any, notFoundValue: any = Injector.THROW_IF_NOT_FOUND,\n      flags: InjectFlags = InjectFlags.Default): any {\n    return this.inject(token, notFoundValue, flags);\n  }\n\n  runInInjectionContext<T>(fn: () => T): T {\n    return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n  }\n\n  execute(tokens: any[], fn: Function, context?: any): any {\n    const params = tokens.map(t => this.inject(t));\n    return fn.apply(context, params);\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): this {\n    this.assertNotInstantiated('overrideModule', 'override module metadata');\n    this.compiler.overrideModule(ngModule, override);\n    return this;\n  }\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): this {\n    this.assertNotInstantiated('overrideComponent', 'override component metadata');\n    this.compiler.overrideComponent(component, override);\n    return this;\n  }\n\n  overrideTemplateUsingTestingModule(component: Type<any>, template: string): this {\n    this.assertNotInstantiated(\n        'TestBed.overrideTemplateUsingTestingModule',\n        'Cannot override template when the test module has already been instantiated');\n    this.compiler.overrideTemplateUsingTestingModule(component, template);\n    return this;\n  }\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): this {\n    this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this.compiler.overrideDirective(directive, override);\n    return this;\n  }\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): this {\n    this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this.compiler.overridePipe(pipe, override);\n    return this;\n  }\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token: any, provider: {useFactory?: Function, useValue?: any, deps?: any[]}):\n      this {\n    this.assertNotInstantiated('overrideProvider', 'override provider');\n    this.compiler.overrideProvider(token, provider);\n    return this;\n  }\n\n  overrideTemplate(component: Type<any>, template: string): TestBed {\n    return this.overrideComponent(component, {set: {template, templateUrl: null!}});\n  }\n\n  createComponent<T>(type: Type<T>): ComponentFixture<T> {\n    const testComponentRenderer = this.inject(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n\n    if (getAsyncClassMetadataFn(type)) {\n      throw new Error(\n          `Component '${type.name}' has unresolved metadata. ` +\n          `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n    }\n\n    const componentDef = (type as any).ɵcmp;\n\n    if (!componentDef) {\n      throw new Error(`It looks like '${stringify(type)}' has not been compiled.`);\n    }\n\n    const componentFactory = new ComponentFactory(componentDef);\n    const initComponent = () => {\n      const componentRef =\n          componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef) as\n          ComponentRef<T>;\n      return this.runInInjectionContext(() => {\n        const hasScheduler = this.inject(ChangeDetectionScheduler, null) !== null;\n        const fixture = hasScheduler ? new ScheduledComponentFixture(componentRef) :\n                                       new PseudoApplicationComponentFixture(componentRef);\n        fixture.initialize();\n        return fixture;\n      });\n    };\n    const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n    const ngZone = noNgZone ? null : this.inject(NgZone, null);\n    const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  private get compiler(): TestBedCompiler {\n    if (this._compiler === null) {\n      throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n    }\n    return this._compiler;\n  }\n\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  private get testModuleRef(): NgModuleRef<any> {\n    if (this._testModuleRef === null) {\n      this._testModuleRef = this.compiler.finalize();\n    }\n    return this._testModuleRef;\n  }\n\n  private assertNotInstantiated(methodName: string, methodDescription: string) {\n    if (this._testModuleRef !== null) {\n      throw new Error(\n          `Cannot ${methodDescription} when the test module has already been instantiated. ` +\n          `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n    }\n  }\n\n  /**\n   * Check whether the module scoping queue should be flushed, and flush it if needed.\n   *\n   * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n   * in-progress module compilation. This creates a potential hazard - the very first time the\n   * TestBed is initialized (or if it's reset without being initialized), there may be pending\n   * compilations of modules declared in global scope. These compilations should be finished.\n   *\n   * To ensure that globally declared modules have their components scoped properly, this function\n   * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n   * to any other operations, the scoping queue is flushed.\n   */\n  private checkGlobalCompilationFinished(): void {\n    // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n    // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n    if (!this.globalCompilationChecked && this._testModuleRef === null) {\n      flushModuleScopingQueueAsMuchAsPossible();\n    }\n    this.globalCompilationChecked = true;\n  }\n\n  private destroyActiveFixtures(): void {\n    let errorCount = 0;\n    this._activeFixtures.forEach((fixture) => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        errorCount++;\n        console.error('Error during cleanup of component', {\n          component: fixture.componentInstance,\n          stacktrace: e,\n        });\n      }\n    });\n    this._activeFixtures = [];\n\n    if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n      throw Error(\n          `${errorCount} ${(errorCount === 1 ? 'component' : 'components')} ` +\n          `threw errors during cleanup`);\n    }\n  }\n\n  shouldRethrowTeardownErrors(): boolean {\n    const instanceOptions = this._instanceTeardownOptions;\n    const environmentOptions = TestBedImpl._environmentTeardownOptions;\n\n    // If the new teardown behavior hasn't been configured, preserve the old behavior.\n    if (!instanceOptions && !environmentOptions) {\n      return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n    }\n\n    // Otherwise use the configured behavior or default to rethrowing.\n    return instanceOptions?.rethrowErrors ?? environmentOptions?.rethrowErrors ??\n        this.shouldTearDownTestingModule();\n  }\n\n  shouldThrowErrorOnUnknownElements(): boolean {\n    // Check if a configuration has been provided to throw when an unknown element is found\n    return this._instanceErrorOnUnknownElementsOption ??\n        TestBedImpl._environmentErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT;\n  }\n\n  shouldThrowErrorOnUnknownProperties(): boolean {\n    // Check if a configuration has been provided to throw when an unknown property is found\n    return this._instanceErrorOnUnknownPropertiesOption ??\n        TestBedImpl._environmentErrorOnUnknownPropertiesOption ??\n        THROW_ON_UNKNOWN_PROPERTIES_DEFAULT;\n  }\n\n  shouldTearDownTestingModule(): boolean {\n    return this._instanceTeardownOptions?.destroyAfterEach ??\n        TestBedImpl._environmentTeardownOptions?.destroyAfterEach ??\n        TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n  }\n\n  getDeferBlockBehavior(): DeferBlockBehavior {\n    return this._instanceDeferBlockBehavior;\n  }\n\n  tearDownTestingModule() {\n    // If the module ref has already been destroyed, we won't be able to get a test renderer.\n    if (this._testModuleRef === null) {\n      return;\n    }\n    // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n    // last step, but the injector will be destroyed as a part of the module ref destruction.\n    const testRenderer = this.inject(TestComponentRenderer);\n    try {\n      this._testModuleRef.destroy();\n    } catch (e) {\n      if (this.shouldRethrowTeardownErrors()) {\n        throw e;\n      } else {\n        console.error('Error during cleanup of a testing module', {\n          component: this._testModuleRef.instance,\n          stacktrace: e,\n        });\n      }\n    } finally {\n      testRenderer.removeAllRootElements?.();\n    }\n  }\n\n  /**\n   * Execute any pending effects.\n   *\n   * @developerPreview\n   */\n  flushEffects(): void {\n    this.inject(EffectScheduler).flush();\n  }\n}\n\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nexport const TestBed: TestBedStatic = TestBedImpl;\n\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nexport function inject(tokens: any[], fn: Function): () => any {\n  const testBed = TestBedImpl.INSTANCE;\n  // Not using an arrow function to preserve context passed from call site\n  return function(this: unknown) {\n    return testBed.execute(tokens, fn, this);\n  };\n}\n\n/**\n * @publicApi\n */\nexport class InjectSetupWrapper {\n  constructor(private _moduleDef: () => TestModuleMetadata) {}\n\n  private _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      TestBedImpl.configureTestingModule(moduleDef);\n    }\n  }\n\n  inject(tokens: any[], fn: Function): () => any {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function(this: unknown) {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\n\n/**\n * @publicApi\n */\nexport function withModule(moduleDef: TestModuleMetadata): InjectSetupWrapper;\nexport function withModule(moduleDef: TestModuleMetadata, fn: Function): () => any;\nexport function withModule(moduleDef: TestModuleMetadata, fn?: Function|null): (() => any)|\n    InjectSetupWrapper {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function(this: unknown) {\n      const testBed = TestBedImpl.INSTANCE;\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with <PERSON>, <PERSON><PERSON>, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n\nimport {resetFakeAsyncZoneIfExists} from './fake_async';\nimport {TestBedImpl} from './test_bed';\n\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\n\nfunction getCleanupHook(expectedTeardownValue: boolean) {\n  return () => {\n    const testBed = TestBedImpl.INSTANCE;\n    if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n      testBed.resetTestingModule();\n      resetFakeAsyncZoneIfExists();\n    }\n  };\n}\n\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nexport const __core_private_testing_placeholder__ = '';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\nexport * from './async';\nexport {ComponentFixture} from './component_fixture';\nexport {resetFakeAsyncZone, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, tick} from './fake_async';\nexport {TestBed, getTestBed, TestBedStatic, inject, InjectSetupWrapper, withModule} from './test_bed';\nexport {TestComponentRenderer, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, TestModuleMetadata, TestEnvironmentOptions, ModuleTeardownOptions} from './test_bed_common';\nexport * from './test_hooks';\nexport * from './metadata_override';\nexport {MetadataOverrider as ɵMetadataOverrider} from './metadata_overrider';\nexport {ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState} from '@angular/core';\nexport {DeferBlockFixture} from './defer';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/// <reference types=\"jasmine\" />\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["DeferBlockState", "triggerResourceLoading", "renderDeferBlockState", "CONTAINER_HEADER_OFFSET", "getDeferBlocks", "DeferBlockBehavior", "inject", "NoopNgZone", "EffectScheduler", "PendingTasks", "stringify", "ReflectionCapabilities", "getAsyncClassMetadataFn", "USE_RUNTIME_DEPS_TRACKER_FOR_JIT", "depsTracker", "getInjectableDef", "NG_COMP_DEF", "NgModuleRef", "DEFAULT_LOCALE_ID", "setLocaleId", "ComponentFactory", "compileComponent", "NG_DIR_DEF", "compileDirective", "NG_PIPE_DEF", "compilePipe", "NG_MOD_DEF", "transitiveScopesFor", "patchComponentDefWithScope", "NG_INJ_DEF", "compileNgModuleDefs", "DEFER_BLOCK_CONFIG", "isEnvironmentProviders", "R3NgModuleFactory", "convertToBitFlags", "setAllowDuplicateNgModuleIdsForTest", "resetCompiledComponents", "setUnknownElementStrictMode", "setUnknownPropertyStrictMode", "getUnknownElementStrictMode", "getUnknownPropertyStrictMode", "ChangeDetectionScheduler", "flushModuleScopingQueueAsMuchAsPossible"], "mappings": ";;;;;;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBG;AACG,SAAU,YAAY,CAAC,EAAY,EAAA;AACvC,IAAA,MAAM,KAAK,GAAQ,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;IAC7D,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,YAAA;AACL,YAAA,OAAO,OAAO,CAAC,MAAM,CACjB,4EAA4E;AAC5E,gBAAA,yDAAyD,CAAC,CAAC;AACjE,SAAC,CAAC;KACH;AACD,IAAA,MAAM,SAAS,GAAG,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;AAChE,IAAA,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;AACnC,QAAA,OAAO,SAAS,CAAC,EAAE,CAAC,CAAC;KACtB;IACD,OAAO,YAAA;AACL,QAAA,OAAO,OAAO,CAAC,MAAM,CACjB,gFAAgF;AAChF,YAAA,iEAAiE,CAAC,CAAC;AACzE,KAAC,CAAC;AACJ,CAAC;AAED;;;;AAIK;AACC,SAAU,KAAK,CAAC,EAAY,EAAA;AAChC,IAAA,OAAO,YAAY,CAAC,EAAE,CAAC,CAAC;AAC1B;;ACvCA;;;;;AAKG;MACU,iBAAiB,CAAA;;IAE5B,WACY,CAAA,KAAwB,EAAU,gBAA2C,EAAA;QAA7E,IAAK,CAAA,KAAA,GAAL,KAAK,CAAmB;QAAU,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA2B;KAAI;AAE7F;;;AAGG;IACH,MAAM,MAAM,CAAC,KAAsB,EAAA;QACjC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACxC,YAAA,MAAM,aAAa,GAAG,8BAA8B,CAAC,KAAK,CAAC,CAAC;AAC5D,YAAA,MAAM,IAAI,KAAK,CACX,CAAA,0CAAA,EAA6C,aAAa,CAAY,UAAA,CAAA;AACtE,gBAAA,CAAA,kBAAA,EAAqB,aAAa,CAAC,WAAW,EAAE,CAAA,6BAAA,CAA+B,CAAC,CAAC;SACtF;AACD,QAAA,IAAI,KAAK,KAAKA,gBAAe,CAAC,QAAQ,EAAE;YACtC,MAAMC,uBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACvF;;;QAGD,MAAM,mBAAmB,GAAG,IAAI,CAAC;AACjC,QAAAC,sBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;AAC3F,QAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;KACvC;AAED;;;AAGG;IACH,cAAc,GAAA;QACZ,MAAM,WAAW,GAAwB,EAAE,CAAC;;;;QAI5C,MAAM,kBAAkB,GAAG,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,IAAIC,wBAAuB,EAAE;YAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAACA,wBAAuB,CAAC,CAAC;AAC7D,YAAAC,eAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACnC,YAAA,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;AAC/B,gBAAA,kBAAkB,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;aAC9E;SACF;AACD,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;KAC5C;AACF,CAAA;AAED,SAAS,gBAAgB,CAAC,KAAsB,EAAE,KAAwB,EAAA;IACxE,QAAQ,KAAK;QACX,KAAKJ,gBAAe,CAAC,WAAW;AAC9B,YAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,oBAAoB,KAAK,IAAI,CAAC;QACtD,KAAKA,gBAAe,CAAC,OAAO;AAC1B,YAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,gBAAgB,KAAK,IAAI,CAAC;QAClD,KAAKA,gBAAe,CAAC,KAAK;AACxB,YAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,cAAc,KAAK,IAAI,CAAC;QAChD,KAAKA,gBAAe,CAAC,QAAQ;AAC3B,YAAA,OAAO,IAAI,CAAC;AACd,QAAA;AACE,YAAA,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAED,SAAS,8BAA8B,CAAC,KAAsB,EAAA;IAC5D,QAAQ,KAAK;QACX,KAAKA,gBAAe,CAAC,WAAW;AAC9B,YAAA,OAAO,aAAa,CAAC;QACvB,KAAKA,gBAAe,CAAC,OAAO;AAC1B,YAAA,OAAO,SAAS,CAAC;QACnB,KAAKA,gBAAe,CAAC,KAAK;AACxB,YAAA,OAAO,OAAO,CAAC;AACjB,QAAA;AACE,YAAA,OAAO,MAAM,CAAC;KACjB;AACH;;AChFA;AACO,MAAM,0CAA0C,GAAG,IAAI,CAAC;AAE/D;AACO,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEvD;AACO,MAAM,mCAAmC,GAAG,KAAK,CAAC;AAEzD;AACO,MAAM,4BAA4B,GAAGK,mBAAkB,CAAC,WAAW,CAAC;AAE3E;;;;AAIG;MACU,qBAAqB,CAAA;IAChC,iBAAiB,CAAC,aAAqB,EAAA,GAAI;AAC3C,IAAA,qBAAqB,MAAM;AAC5B,CAAA;AAED;;AAEG;MACU,0BAA0B,GAAG,IAAI,cAAc,CAAU,4BAA4B,EAAE;AAEpG;;;AAGG;AACI,MAAM,wDAAwD,GACjE,IAAI,cAAc,CAAU,0DAA0D,CAAC,CAAC;AAE5F;;AAEG;MACU,wBAAwB,GAAG,IAAI,cAAc,CAAU,0BAA0B;;ACjC9F;;;;AAIG;MACmB,gBAAgB,CAAA;;AAiDpC,IAAA,WAAA,CAAmB,YAA6B,EAAA;QAA7B,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAiB;QAtBxC,IAAY,CAAA,YAAA,GAAY,KAAK,CAAC;;QAEnB,IAAkB,CAAA,kBAAA,GAAGC,QAAM,CAAC,wBAAwB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;;AAEjF,QAAA,IAAA,CAAA,OAAO,GAAW,IAAI,CAAC,kBAAkB,GAAG,IAAIC,WAAU,EAAE,GAAGD,QAAM,CAAC,MAAM,CAAC,CAAC;;AAE9E,QAAA,IAAA,CAAA,aAAa,GAAGA,QAAM,CAACE,gBAAe,CAAC,CAAC;;;;;;;;AAQ/B,QAAA,IAAA,CAAA,OAAO,GAAGF,QAAM,CAAC,cAAc,CAAC,CAAC;;AAEjC,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,CAAC,OAAgC,CAAC;;AAGvE,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;AAIrD,QAAA,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,CAAC;AACxD,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,YAAY,GAAiB,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AAC9E,QAAA,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;AACnD,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KAClC;AAOD;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC;KACzC;AAuBD;;;;AAIG;IACH,cAAc,GAAA;QACZ,MAAM,WAAW,GAAwB,EAAE,CAAC;QAC5C,MAAM,KAAK,GAAI,IAAI,CAAC,YAAY,CAAC,QAAgB,CAAC,QAAQ,CAAC,CAAC;AAC5D,QAAAF,eAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAEnC,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAC9B,QAAA,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;YAC/B,kBAAkB,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;SAC7D;AAED,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;KAC5C;IAEO,YAAY,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;AAChC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;SACzE;QACD,OAAO,IAAI,CAAC,SAAoC,CAAC;KAClD;AAED;;AAEG;IACH,iBAAiB,GAAA;AACf,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AACrC,QAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,iBAAiB,EAAE;AAC1C,YAAA,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;SACrC;AACD,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;KAC1B;AAED;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;KACF;AACF,CAAA;AAED;;;;;AAKG;AACG,MAAO,yBAA6B,SAAQ,gBAAmB,CAAA;AAArE,IAAA,WAAA,GAAA;;AACmB,QAAA,IAAA,CAAA,yBAAyB,GACtCE,QAAM,CAAC,wDAAwD,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,IAAI,KAAK,CAAC;AAC/E,QAAA,IAAA,CAAA,YAAY,GAAGA,QAAM,CAACG,aAAY,CAAC,CAAC;KAqCtD;IAnCC,UAAU,GAAA;QACR,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;KACrD;IAEQ,aAAa,CAAC,iBAA0B,IAAI,EAAA;AACnD,QAAA,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACnC,MAAM,IAAI,KAAK,CACX,2EAA2E;AACvE,gBAAA,6DAA6D,CACpE,CAAC;SACH;aAAM,IAAI,CAAC,cAAc,EAAE;YAC1B,MAAM,IAAI,KAAK,CACX,yDAAyD;AACrD,gBAAA,gFAAgF,CACvF,CAAC;SACH;AACD,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KAC5B;IAEQ,QAAQ,GAAA;QACf,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,CAAC;KACjD;IAEQ,UAAU,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;AACnB,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/B;AACD,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;KAC3F;AAEQ,IAAA,iBAAiB,CAAC,UAA8B,EAAA;AACvD,QAAA,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;KAC1F;AACF,CAAA;AAQD;;AAEG;AACG,MAAO,iCAAqC,SAAQ,gBAAmB,CAAA;AAA7E,IAAA,WAAA,GAAA;;AACU,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,YAAY,EAAE,CAAC;AACpC,QAAA,IAAA,CAAA,WAAW,GAAGH,QAAM,CAAC,0BAA0B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,IAAI,KAAK,CAAC;QAC5E,IAAS,CAAA,SAAA,GAAY,IAAI,CAAC;QAC1B,IAAQ,CAAA,QAAA,GAA0B,IAAI,CAAC;QACvC,IAAQ,CAAA,QAAA,GAAqC,IAAI,CAAC;QAClD,IAAqB,CAAA,qBAAA,GAA2B,SAAS,CAAC;QAC1D,IAAwB,CAAA,wBAAA,GAA2B,SAAS,CAAC;KAsGtE;IApGC,UAAU,GAAA;;;AAGR,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CACnB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC;gBAChC,IAAI,EAAE,MAAK;AACT,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;iBACxB;AACF,aAAA,CAAC,CACL,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CACnB,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC;gBACtC,IAAI,EAAE,MAAK;AACT,oBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;;;AAGpB,wBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;qBAC1B;iBACF;AACF,aAAA,CAAC,CACL,CAAC;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CACnB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC9B,IAAI,EAAE,MAAK;AACT,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;;AAEtB,oBAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;;;;wBAI1B,cAAc,CAAC,MAAK;AAClB,4BAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;AACtC,gCAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;AAC1B,oCAAA,IAAI,CAAC,QAAS,CAAC,IAAI,CAAC,CAAC;AACrB,oCAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,oCAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;iCACtB;6BACF;AACH,yBAAC,CAAC,CAAC;qBACJ;iBACF;AACF,aAAA,CAAC,CACL,CAAC;AAEF,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CACnB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;AAC7B,gBAAA,IAAI,EAAE,CAAC,KAAU,KAAI;AACnB,oBAAA,MAAM,KAAK,CAAC;iBACb;AACF,aAAA,CAAC,CACL,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;IAEQ,aAAa,CAAC,cAAc,GAAG,IAAI,EAAA;AAC1C,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;;;AAG3B,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,YAAA,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC;YACvC,IAAI,cAAc,EAAE;gBAClB,IAAI,CAAC,cAAc,EAAE,CAAC;aACvB;AACH,SAAC,CAAC,CAAC;;;AAGH,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KAC5B;IAEQ,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;KAC7D;IAEQ,UAAU,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;AACnB,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/B;AAAM,aAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACjC,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,KAAI;AAClC,gBAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACtB,aAAC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;KACF;IAEQ,iBAAiB,CAAC,UAAU,GAAG,IAAI,EAAA;AAC1C,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;SACxF;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;IAEQ,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;QAClC,KAAK,CAAC,OAAO,EAAE,CAAC;KACjB;AACF;;AC7TD,MAAM,KAAK,GAAQ,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;AAC7D,MAAM,mBAAmB,GAAG,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;AAE9E,MAAM,wCAAwC,GAC1C,CAAA;wEACoE,CAAC;AAEzE;;;;;AAKG;SACa,kBAAkB,GAAA;IAChC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;KACjD;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,CAAC;SAEe,0BAA0B,GAAA;IACxC,IAAI,mBAAmB,EAAE;QACvB,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;KAC1C;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACG,SAAU,SAAS,CAAC,EAAY,EAAA;IACpC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KAC1C;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DG;SACa,IAAI,CAChB,MAAiB,GAAA,CAAC,EAAE,WAA4D,GAAA;AAC9E,IAAA,iCAAiC,EAAE,IAAI;AACxC,CAAA,EAAA;IACH,IAAI,mBAAmB,EAAE;QACvB,OAAO,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;KACtD;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;;;;AAUG;AACG,SAAU,KAAK,CAAC,QAAiB,EAAA;IACrC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;KAC5C;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,CAAC;AAED;;;;AAIG;SACa,oBAAoB,GAAA;IAClC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,oBAAoB,EAAE,CAAC;KACnD;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,CAAC;AAED;;;;AAIG;SACa,eAAe,GAAA;IAC7B,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,eAAe,EAAE,CAAC;KAC9C;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D;;ACjKA,IAAI,gBAAgB,GAAG,CAAC,CAAC;MAEZ,iBAAiB,CAAA;AAA9B,IAAA,WAAA,GAAA;AACU,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,GAAG,EAAe,CAAC;KA0B9C;AAzBC;;;AAGG;AACH,IAAA,gBAAgB,CACZ,aAAoC,EAAE,WAAc,EAAE,QAA6B,EAAA;QACrF,MAAM,KAAK,GAAc,EAAE,CAAC;QAC5B,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,GAAS,WAAY,CAAC,IAAI,CAAC,CAAC,CAAC;SACpF;AAED,QAAA,IAAI,QAAQ,CAAC,GAAG,EAAE;YAChB,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,CAA6B,0BAAA,EAAAI,UAAS,CAAC,aAAa,CAAC,CAAoB,kBAAA,CAAA,CAAC,CAAC;aAC5F;AACD,YAAA,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;SAClC;AACD,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SAC1D;AACD,QAAA,IAAI,QAAQ,CAAC,GAAG,EAAE;AAChB,YAAA,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;SAClC;AACD,QAAA,OAAO,IAAI,aAAa,CAAM,KAAK,CAAC,CAAC;KACtC;AACF,CAAA;AAED,SAAS,cAAc,CAAC,QAAmB,EAAE,MAAW,EAAE,UAA4B,EAAA;AACpF,IAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;AACxC,IAAA,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;AACzB,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACjC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAC9B,YAAA,WAAW,CAAC,OAAO,CAAC,CAAC,KAAU,KAAI;AACjC,gBAAA,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;AAC3D,aAAC,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;SAChE;KACF;AAED,IAAA,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;AAC3B,QAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACjC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC5B,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAC7B,CAAC,KAAU,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;SAChF;aAAM;AACL,YAAA,IAAI,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;AAChE,gBAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;aAC5B;SACF;KACF;AACH,CAAC;AAED,SAAS,WAAW,CAAC,QAAmB,EAAE,GAAQ,EAAA;AAChD,IAAA,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AACtB,QAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,QAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,SAAS,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACjD,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SAC7C;aAAM;AACL,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;SAC3B;KACF;AACH,CAAC;AAED,SAAS,WAAW,CAAC,QAAmB,EAAE,GAAQ,EAAA;AAChD,IAAA,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;QACtB,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;KAC5B;AACH,CAAC;AAED,SAAS,YAAY,CAAC,QAAa,EAAE,SAAc,EAAE,UAA4B,EAAA;IAC/E,IAAI,YAAY,GAAG,CAAC,CAAC;AACrB,IAAA,MAAM,SAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;AAC5C,IAAA,MAAM,QAAQ,GAAG,CAAC,GAAQ,EAAE,KAAU,KAAI;QACxC,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC/C,YAAA,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aAC7B;;;YAGD,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,YAAY,EAAE,CAAE,CAAA,CAAC,CAAC;;AAG/C,YAAA,OAAO,KAAK,CAAC;SACd;AAAM,aAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;AACtC,YAAA,KAAK,GAAG,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;SAChD;AACD,QAAA,OAAO,KAAK,CAAC;AACf,KAAC,CAAC;AAEF,IAAA,OAAO,CAAG,EAAA,QAAQ,CAAI,CAAA,EAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA,CAAE,CAAC;AAC9D,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAQ,EAAE,UAA4B,EAAA;IACjE,IAAI,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7B,IAAI,CAAC,EAAE,EAAE;QACP,EAAE,GAAG,CAAG,EAAAA,UAAS,CAAC,GAAG,CAAC,CAAG,EAAA,gBAAgB,EAAE,CAAA,CAAE,CAAC;AAC9C,QAAA,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;KACzB;AACD,IAAA,OAAO,EAAE,CAAC;AACZ,CAAC;AAGD,SAAS,WAAW,CAAC,GAAQ,EAAA;IAC3B,MAAM,KAAK,GAAa,EAAE,CAAC;;IAE3B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;QAChC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACzB,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClB;AACH,KAAC,CAAC,CAAC;;IAGH,IAAI,KAAK,GAAG,GAAG,CAAC;IAChB,OAAO,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;QAC3C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,KAAI;YACvC,MAAM,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC/D,YAAA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AACvD,gBAAA,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACvB;AACH,SAAC,CAAC,CAAC;KACJ;AACD,IAAA,OAAO,KAAK,CAAC;AACf;;ACnIA,MAAM,UAAU,GAAG,IAAIC,uBAAsB,EAAE,CAAC;AAWhD;;AAEG;AACH,MAAe,gBAAgB,CAAA;AAA/B,IAAA,WAAA,GAAA;AACU,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAAoC,CAAC;AACxD,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,GAAG,EAAqB,CAAC;KAuDjD;IAnDC,WAAW,CAAC,IAAe,EAAE,QAA6B,EAAA;AACxD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACjD,QAAA,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC5B;AAED,IAAA,YAAY,CAAC,SAAkD,EAAA;AAC7D,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAI;AACrC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACnC,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,aAAa,CAAC,IAAe,EAAA;QAC3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;;;;;AAMjD,QAAA,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,WAAW,GAAG,UAAU,YAAY,SAAS,IAAI,UAAU,YAAY,SAAS;AAClF,gBAAA,UAAU,YAAY,IAAI,IAAI,UAAU,YAAY,QAAQ,CAAC;YACjE,IAAI,WAAW,EAAE;AACf,gBAAA,OAAO,UAAU,YAAY,IAAI,CAAC,IAAI,GAAG,UAA0B,GAAG,IAAI,CAAC;aAC5E;SACF;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,OAAO,CAAC,IAAe,EAAA;AACrB,QAAA,IAAI,QAAQ,GAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;QAEvD,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,QAAQ,EAAE;gBACZ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC3C,IAAI,SAAS,EAAE;AACb,oBAAA,MAAM,SAAS,GAAG,IAAI,iBAAiB,EAAE,CAAC;AAC1C,oBAAA,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;AAC3B,wBAAA,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAS,EAAE,QAAQ,CAAC,CAAC;AACxE,qBAAC,CAAC,CAAC;iBACJ;aACF;YACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SACnC;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;AACF,CAAA;AAGK,MAAO,iBAAkB,SAAQ,gBAA2B,CAAA;AAChE,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,SAAS,CAAC;KAClB;AACF,CAAA;AAEK,MAAO,iBAAkB,SAAQ,gBAA2B,CAAA;AAChE,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,SAAS,CAAC;KAClB;AACF,CAAA;AAEK,MAAO,YAAa,SAAQ,gBAAsB,CAAA;AACtD,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI,CAAC;KACb;AACF,CAAA;AAEK,MAAO,gBAAiB,SAAQ,gBAA0B,CAAA;AAC9D,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,QAAQ,CAAC;KACjB;AACF;;AC5FD,IAAK,qBAGJ,CAAA;AAHD,CAAA,UAAK,qBAAqB,EAAA;AACxB,IAAA,qBAAA,CAAA,qBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW,CAAA;AACX,IAAA,qBAAA,CAAA,qBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAiB,CAAA;AACnB,CAAC,EAHI,qBAAqB,KAArB,qBAAqB,GAGzB,EAAA,CAAA,CAAA,CAAA;AAED,SAAS,uBAAuB,CAAC,KAAc,EAAA;AAC7C,IAAA,OAAO,KAAK,KAAK,qBAAqB,CAAC,WAAW;AAC9C,QAAA,KAAK,KAAK,qBAAqB,CAAC,iBAAiB,CAAC;AACxD,CAAC;AAED,SAAS,4BAA4B,CACjC,KAAkB,EAAE,QAAuB,EAAE,QAAgB,EAAA;AAC/D,IAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AACnB,QAAA,IAAI,CAACC,wBAAuB,CAAC,IAAI,CAAC,EAAE;YAClC,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzC,YAAA,IAAI,SAAS,IAAI,SAAS,CAAC,UAAU,EAAE;gBACrC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;aACzE;SACF;AACH,KAAC,CAAC,CAAC;AACL,CAAC;MAgBY,eAAe,CAAA;IAmE1B,WAAoB,CAAA,QAAqB,EAAU,qBAA4C,EAAA;QAA3E,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAa;QAAU,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAAuB;QAlEvF,IAAgC,CAAA,gCAAA,GAAmC,IAAI,CAAC;;QAGxE,IAAY,CAAA,YAAA,GAAgB,EAAE,CAAC;QAC/B,IAAO,CAAA,OAAA,GAAgB,EAAE,CAAC;QAC1B,IAAS,CAAA,SAAA,GAAe,EAAE,CAAC;QAC3B,IAAO,CAAA,OAAA,GAAU,EAAE,CAAC;;AAGpB,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,GAAG,EAAa,CAAC;AACzC,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,GAAG,EAAa,CAAC;AACzC,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,GAAG,EAAa,CAAC;;;AAIpC,QAAA,IAAA,CAAA,2BAA2B,GAAG,IAAI,GAAG,EAAiB,CAAC;;AAGvD,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAAa,CAAC;AACtC,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAAa,CAAC;;AAGtC,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,GAAG,EAAqB,CAAC;;;AAIjD,QAAA,IAAA,CAAA,uBAAuB,GAAG,IAAI,GAAG,EAAuB,CAAC;QAEzD,IAAS,CAAA,SAAA,GAAc,aAAa,EAAE,CAAC;;;;;;;;AASvC,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,GAAG,EAAmD,CAAC;;;;;;AAOpF,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAwD,CAAC;;;QAIhF,IAAa,CAAA,aAAA,GAAuB,EAAE,CAAC;QAEvC,IAAS,CAAA,SAAA,GAAkB,IAAI,CAAC;QAChC,IAAiB,CAAA,iBAAA,GAAoB,IAAI,CAAC;QAE1C,IAAiB,CAAA,iBAAA,GAAe,EAAE,CAAC;QACnC,IAAqB,CAAA,qBAAA,GAAe,EAAE,CAAC;;;AAGvC,QAAA,IAAA,CAAA,yBAAyB,GAAG,IAAI,GAAG,EAAiC,CAAC;AACrE,QAAA,IAAA,CAAA,wBAAwB,GAAG,IAAI,GAAG,EAAiB,CAAC;AACpD,QAAA,IAAA,CAAA,6BAA6B,GAAG,IAAI,GAAG,EAAa,CAAC;QAGrD,IAAa,CAAA,aAAA,GAA0B,IAAI,CAAC;QAE5C,IAAkB,CAAA,kBAAA,GAAG,4BAA4B,CAAC;AAGxD,QAAA,MAAM,iBAAiB,CAAA;AAAG,SAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,iBAAwB,CAAC;KAChD;AAED,IAAA,oBAAoB,CAAC,SAA0B,EAAA;AAC7C,QAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;AACnC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACvB;AAED,IAAA,sBAAsB,CAAC,SAA6B,EAAA;;AAElD,QAAA,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,EAAE;;AAExC,YAAA,4BAA4B,CACxB,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAChD,uCAAuC,CAAC,CAAC;YAC7C,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,YAAY,EAAE,qBAAqB,CAAC,WAAW,CAAC,CAAC;YAC/E,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;SACnD;;AAGD,QAAA,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,EAAE;AACnC,YAAA,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACnD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;SACzC;AAED,QAAA,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;SAC7C;AAED,QAAA,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,IAAI,4BAA4B,CAAC;KACxF;IAED,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAAA;QACtE,IAAIC,iCAAgC,EAAE;AACpC,YAAAC,YAAW,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;SAC1C;AACD,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAA6B,CAAC,CAAC;;QAG1D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACtD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACzD,QAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;YACrB,MAAM,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SACnD;AAED,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;;;;AAK3C,QAAA,IAAI,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC7C;IAED,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;AAC3E,QAAA,IAAI,CAAC,+BAA+B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;;;AAItC,QAAA,IAAI,CAAC,uCAAuC,CAAC,SAAS,CAAC,CAAC;KACzD;IAED,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;AAC3E,QAAA,IAAI,CAAC,+BAA+B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KACvC;IAED,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAA;AAC5D,QAAA,IAAI,CAAC,+BAA+B,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC7B;IAEO,+BAA+B,CACnC,IAAe,EAAE,QAAoD,EAAA;AACvE,QAAA,IAAI,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,YAAY,CAAC;YACxF,QAAQ,CAAC,MAAM,EAAE,cAAc,CAAC,YAAY,CAAC,EAAE;AACjD,YAAA,MAAM,IAAI,KAAK,CACX,uBAAuB,IAAI,CAAC,IAAI,CAAsC,oCAAA,CAAA;AACtE,gBAAA,CAAA,wEAAA,CAA0E,CAAC,CAAC;SACjF;KACF;IAED,gBAAgB,CACZ,KAAU,EACV,QAAgF,EAAA;AAClF,QAAA,IAAI,WAAqB,CAAC;AAC1B,QAAA,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;AACrC,YAAA,WAAW,GAAG;AACZ,gBAAA,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,QAAQ,CAAC,UAAU;AAC/B,gBAAA,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE;gBACzB,KAAK,EAAE,QAAQ,CAAC,KAAK;aACtB,CAAC;SACH;AAAM,aAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC1C,YAAA,WAAW,GAAG,EAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAC,CAAC;SACpF;aAAM;AACL,YAAA,WAAW,GAAG,EAAC,OAAO,EAAE,KAAK,EAAC,CAAC;SAChC;AAED,QAAA,MAAM,aAAa,GACf,OAAO,KAAK,KAAK,QAAQ,GAAGC,iBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC/D,QAAA,MAAM,UAAU,GAAG,aAAa,KAAK,IAAI,GAAG,IAAI,GAAG,iBAAiB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AAC/F,QAAA,MAAM,eAAe,GACjB,UAAU,KAAK,MAAM,GAAG,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAChF,QAAA,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;;QAGlC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACtD,QAAA,IAAI,aAAa,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YACnF,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACzE,YAAA,IAAI,iBAAiB,KAAK,SAAS,EAAE;AACnC,gBAAA,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACrC;iBAAM;gBACL,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;aAC/D;SACF;KACF;IAED,kCAAkC,CAAC,IAAe,EAAE,QAAgB,EAAA;AAClE,QAAA,MAAM,GAAG,GAAI,IAAY,CAACC,YAAW,CAAC,CAAC;QACvC,MAAM,YAAY,GAAG,MAAc;AACjC,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAe,CAAC;AACtE,YAAA,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;AAC7D,SAAC,CAAC;AACF,QAAA,MAAM,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,IAAI,YAAY,EAAE,CAAC;;;;;;;;QAS7F,MAAM,QAAQ,GACV,iBAAiB,GAAG,EAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAC,GAAG,EAAC,QAAQ,EAAC,CAAC;QAChG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAC,GAAG,EAAE,QAAQ,EAAC,CAAC,CAAC;AAE9C,QAAA,IAAI,iBAAiB,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5D,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;SACpD;;QAGD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,EAAE,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;KAChF;AAEO,IAAA,MAAM,yCAAyC,GAAA;AACrD,QAAA,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,KAAK,CAAC;YAAE,OAAO;QAExD,MAAM,QAAQ,GAAG,EAAE,CAAC;AACpB,QAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACxD,YAAA,MAAM,eAAe,GAAGJ,wBAAuB,CAAC,SAAS,CAAC,CAAC;YAC3D,IAAI,eAAe,EAAE;AACnB,gBAAA,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;aAClC;SACF;AACD,QAAA,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QAEzC,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,MAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,CAAC;;;AAIlD,QAAA,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE;AACxC,YAAA,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC,CAAC;SAC/C;KACF;AAED,IAAA,MAAM,iBAAiB,GAAA;QACrB,IAAI,CAAC,6BAA6B,EAAE,CAAC;;;;AAKrC,QAAA,MAAM,IAAI,CAAC,yCAAyC,EAAE,CAAC;;;;;AAMvD,QAAA,4BAA4B,CACxB,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,uCAAuC,CAAC,CAAC;;AAG1F,QAAA,IAAI,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;;QAGlD,IAAI,mBAAmB,EAAE;AACvB,YAAA,IAAI,cAA8B,CAAC;AACnC,YAAA,IAAI,QAAQ,GAAG,CAAC,GAAW,KAAqB;gBAC9C,IAAI,CAAC,cAAc,EAAE;oBACnB,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;iBACpD;gBACD,OAAO,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAClD,aAAC,CAAC;AACF,YAAA,MAAM,0BAA0B,CAAC,QAAQ,CAAC,CAAC;SAC5C;KACF;IAED,QAAQ,GAAA;;QAEN,IAAI,CAAC,gBAAgB,EAAE,CAAC;;QAGxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;;;QAI9B,IAAI,CAAC,iCAAiC,EAAE,CAAC;;;AAIzC,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;AAEpC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC9C,QAAA,IAAI,CAAC,aAAa,GAAG,IAAIK,mBAAW,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;;;AAI7E,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,qBAAqB,CAAS,CAAC,eAAe,EAAE,CAAC;;;;AAKlF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAEC,kBAAiB,CAAC,CAAC;QAC/EC,YAAW,CAAC,QAAQ,CAAC,CAAC;QAEtB,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;AAED;;AAEG;AACH,IAAA,oBAAoB,CAAC,UAAqB,EAAA;AACxC,QAAA,IAAI,CAAC,0BAA0B,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;AAED;;AAEG;IACH,MAAM,qBAAqB,CAAC,UAAqB,EAAA;AAC/C,QAAA,IAAI,CAAC,0BAA0B,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9C,QAAA,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;AAED;;AAEG;IACH,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;KAC9B;AAED;;AAEG;AACH,IAAA,sBAAsB,CAAC,UAAwB,EAAA;AAC7C,QAAA,OAAO,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,KAAI;AACnF,YAAA,MAAM,YAAY,GAAI,WAAmB,CAAC,IAAI,CAAC;AAC/C,YAAA,YAAY,IAAI,SAAS,CAAC,IAAI,CAAC,IAAIC,wBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,aAAc,CAAC,CAAC,CAAC;AACxF,YAAA,OAAO,SAAS,CAAC;SAClB,EAAE,EAA6B,CAAC,CAAC;KACnC;IAEO,gBAAgB,GAAA;;QAEtB,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAChC,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,IAAG;AAC3C,YAAA,IAAIR,wBAAuB,CAAC,WAAW,CAAC,EAAE;AACxC,gBAAA,MAAM,IAAI,KAAK,CACX,cAAc,WAAW,CAAC,IAAI,CAA6B,2BAAA,CAAA;AAC3D,oBAAA,CAAA,2EAAA,CAA6E,CAAC,CAAC;aACpF;AAED,YAAA,mBAAmB,GAAG,mBAAmB,IAAI,gCAAgC,CAAC,WAAW,CAAC,CAAC;AAE3F,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC/D,YAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,MAAM,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aACvD;AAED,YAAA,IAAI,CAAC,eAAe,CAACI,YAAW,EAAE,WAAW,CAAC,CAAC;YAC/C,IAAIH,iCAAgC,EAAE;AACpC,gBAAAC,YAAW,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;aAC7C;AACD,YAAAO,iBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC1C,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAE/B,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,IAAG;AAC3C,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC/D,YAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,MAAM,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aACvD;AACD,YAAA,IAAI,CAAC,eAAe,CAACC,WAAU,EAAE,WAAW,CAAC,CAAC;AAC9C,YAAAC,iBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC1C,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAE/B,QAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,IAAG;AACtC,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC1D,YAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,MAAM,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aAClD;AACD,YAAA,IAAI,CAAC,eAAe,CAACC,YAAW,EAAE,WAAW,CAAC,CAAC;AAC/C,YAAAC,YAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACrC,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;AAE1B,QAAA,OAAO,mBAAmB,CAAC;KAC5B;IAEO,qBAAqB,GAAA;QAC3B,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,CAAC,EAAE;;;;YAInC,MAAM,gBAAgB,GAAI,IAAI,CAAC,cAAsB,CAACC,WAAU,CAAC,CAAC;YAClE,MAAM,eAAe,GAAG,IAAI,CAAC,iCAAiC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACzF,YAAA,IAAI,eAAe,CAAC,IAAI,GAAG,CAAC,EAAE;AAC5B,gBAAA,eAAe,CAAC,OAAO,CAAC,UAAU,IAAG;oBACnC,IAAI,CAACb,iCAAgC,EAAE;wBACrC,IAAI,CAAC,qBAAqB,CAAC,UAAiB,EAAEa,WAAU,EAAE,yBAAyB,CAAC,CAAC;AACpF,wBAAA,UAAkB,CAACA,WAAU,CAAC,CAAC,uBAAuB,GAAG,IAAI,CAAC;qBAChE;yBAAM;AACL,wBAAAZ,YAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;qBAC5C;AACH,iBAAC,CAAC,CAAC;aACJ;SACF;AAED,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAA6D,CAAC;AAC3F,QAAA,MAAM,gBAAgB,GAClB,CAAC,UAA2C,KAA8B;YACxE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAClC,gBAAA,MAAM,eAAe,GAAG,uBAAuB,CAAC,UAAU,CAAC,CAAC;AAC5D,gBAAA,MAAM,QAAQ,GAAG,eAAe,GAAG,IAAI,CAAC,cAAc,GAAG,UAAuB,CAAC;gBACjF,aAAa,CAAC,GAAG,CAAC,UAAU,EAAEa,oBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC9D;AACD,YAAA,OAAO,aAAa,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;AACxC,SAAC,CAAC;QAEN,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,aAAa,KAAI;AAChE,YAAA,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,gBAAA,MAAM,WAAW,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBACjD,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAEX,YAAW,EAAE,eAAe,CAAC,CAAC;gBACxE,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAEA,YAAW,EAAE,UAAU,CAAC,CAAC;gBACnEY,2BAA0B,CAAC,eAAe,CAAC,aAAa,CAAE,EAAE,WAAW,CAAC,CAAC;aAC1E;;;;;;;;YAQD,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAEZ,YAAW,EAAE,OAAO,CAAC,CAAC;AAClE,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;KACrC;IAEO,sBAAsB,GAAA;QAC5B,MAAM,mBAAmB,GAAG,CAAC,KAAa,KAAK,CAAC,IAAe,KAAI;YACjE,MAAM,QAAQ,GAAG,KAAK,KAAKA,YAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;YAC7F,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAE,CAAC;YACzC,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AACjD,gBAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aACjD;AACH,SAAC,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAACA,YAAW,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAACM,WAAU,CAAC,CAAC,CAAC;AAE7D,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;KAC7B;AAGD;;;AAGG;AACK,IAAA,6BAA6B,CAAC,IAAe,EAAA;QACnD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;;;;;AAMjE,QAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC7D,OAAO;SACR;AACD,QAAA,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;;;;;AAO7C,QAAA,MAAM,WAAW,GAAS,IAAY,CAACO,WAAU,CAAC,CAAC;;AAGnD,QAAA,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,KAAK,CAAC;YAAE,OAAO;AAErD,QAAA,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;;AAE/B,YAAA,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;AAC3D,YAAA,KAAK,MAAM,UAAU,IAAI,YAAY,EAAE;AACrC,gBAAA,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;aAChD;SACF;aAAM;AACL,YAAA,MAAM,SAAS,GAAiD;gBAC9D,GAAG,WAAW,CAAC,SAAS;gBACxB,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAyB,CAAC,IAAI,EAAE,CAAC;aACzE,CAAC;AACF,YAAA,IAAI,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE;AACxC,gBAAA,IAAI,CAAC,eAAe,CAACA,WAAU,EAAE,IAAI,CAAC,CAAC;gBAEvC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAEA,WAAU,EAAE,WAAW,CAAC,CAAC;gBAC1D,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;aAChE;;AAGD,YAAA,MAAM,SAAS,GAAI,IAAY,CAACH,WAAU,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACjD,YAAA,KAAK,MAAM,cAAc,IAAI,OAAO,EAAE;AACpC,gBAAA,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,CAAC;aACpD;;;YAGD,KAAK,MAAM,cAAc,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;AACzD,gBAAA,IAAI,qBAAqB,CAAC,cAAc,CAAC,EAAE;AACzC,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;AACtB,wBAAA,MAAM,EAAE,cAAc;AACtB,wBAAA,SAAS,EAAE,WAAW;wBACtB,aAAa,EAAE,cAAc,CAAC,SAAS;AACxC,qBAAA,CAAC,CAAC;oBACH,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAClD,cAAc,CAAC,SAAyD,CAAC,CAAC;iBAC/E;aACF;SACF;KACF;IAEO,iCAAiC,GAAA;QACvC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAChC,CAAC,MAAM,EAAE,IAAI,KAAM,IAAY,CAACV,YAAW,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;KACtC;IAEO,cAAc,CAAC,GAAU,EAAE,UAA2C,EAAA;AAC5E,QAAA,KAAK,MAAM,KAAK,IAAI,GAAG,EAAE;AACvB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;aACxC;iBAAM;AACL,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;aACnC;SACF;KACF;IAEO,iBAAiB,CAAC,QAAmB,EAAE,QAAkB,EAAA;;AAE/D,QAAA,IAAI,CAAC,eAAe,CAACU,WAAU,EAAE,QAAQ,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,eAAe,CAACG,WAAU,EAAE,QAAQ,CAAC,CAAC;AAE3C,QAAAC,oBAAmB,CAAC,QAA6B,EAAE,QAAQ,CAAC,CAAC;KAC9D;AAEO,IAAA,uCAAuC,CAAC,IAAmB,EAAA;AACjE,QAAA,MAAM,eAAe,GAAGlB,wBAAuB,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC5C;KACF;IAEO,SAAS,CAAC,IAAe,EAAE,UAAgD,EAAA;;;AAGjF,QAAA,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC,CAAC;AAEnD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,SAAS,EAAE;;;;AAIb,YAAA,IAAI,gCAAgC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAACI,YAAW,CAAC,EAAE;AAC/E,gBAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAClC;AACD,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;YAiB9B,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC;AACtC,gBAAA,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,qBAAqB,CAAC,WAAW,GAAG;gBACjF,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;aACnD;YACD,OAAO;SACR;AAED,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,cAAc,CAACM,WAAU,CAAC,EAAE;AACpC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAClC;AACD,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9B,OAAO;SACR;AAED,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAACE,YAAW,CAAC,EAAE;AAC7C,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC5B,OAAO;SACR;KACF;AAEO,IAAA,0BAA0B,CAAC,GAAU,EAAA;;;;;AAK3C,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC,QAAA,MAAM,+BAA+B,GAAG,CAAC,GAAU,KAAU;AAC3D,YAAA,KAAK,MAAM,KAAK,IAAI,GAAG,EAAE;AACvB,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACxB,+BAA+B,CAAC,KAAK,CAAC,CAAC;iBACxC;AAAM,qBAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AAChC,oBAAA,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC;AACvB,oBAAA,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBAC1B,SAAS;qBACV;AACD,oBAAA,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;;AAGvB,oBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC5D,+BAA+B,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC5D,+BAA+B,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC7D;AAAM,qBAAA,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;AACvC,oBAAA,+BAA+B,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;iBACnD;AAAM,qBAAA,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;AACvC,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC5B,oBAAA,MAAM,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAEnC,oBAAA,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBAC1B,SAAS;qBACV;AACD,oBAAA,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAEvB,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;AAC3D,oBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;;;;;wBAKlC,IAAI,qBAAqB,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE;AACnE,4BAAA,+BAA+B,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;yBAC/C;6BAAM;AACL,4BAAA,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;yBAClC;AACH,qBAAC,CAAC,CAAC;iBACJ;aACF;AACH,SAAC,CAAC;QACF,+BAA+B,CAAC,GAAG,CAAC,CAAC;KACtC;;;;;;;;AASO,IAAA,iCAAiC,CAAC,GAAU,EAAA;AAClD,QAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAqB,CAAC;AACjD,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAqB,CAAC;AACrD,QAAA,MAAM,wBAAwB,GAAG,CAAC,GAAU,EAAE,IAAyB,KAAU;AAC/E,YAAA,KAAK,MAAM,KAAK,IAAI,GAAG,EAAE;AACvB,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;;;AAGxB,oBAAA,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;iBACvC;AAAM,qBAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AAChC,oBAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;;;;AAI1B,wBAAA,IAAI,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC9B,4BAAA,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;yBACjD;wBACD,SAAS;qBACV;AACD,oBAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACvB,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACrC,wBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;qBACjD;;AAED,oBAAA,MAAM,SAAS,GAAI,KAAa,CAACE,WAAU,CAAC,CAAC;AAC7C,oBAAA,wBAAwB,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;iBAChF;aACF;AACH,SAAC,CAAC;AACF,QAAA,wBAAwB,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAClC,QAAA,OAAO,eAAe,CAAC;KACxB;AAED;;;;;AAKG;IACK,eAAe,CAAC,IAAY,EAAE,IAAe,EAAA;QACnD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACjC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;SACzC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;QAClD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC/D,YAAA,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SACnC;KACF;AAEO,IAAA,qBAAqB,CAAC,IAAe,EAAE,QAAgB,EAAE,SAAiB,EAAA;AAChF,QAAA,MAAM,GAAG,GAAS,IAAY,CAAC,QAAQ,CAAC,CAAC;AACzC,QAAA,MAAM,aAAa,GAAQ,GAAG,CAAC,SAAS,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,aAAa,EAAC,CAAC,CAAC;KAClE;AAED;;;;AAIG;IACK,6BAA6B,GAAA;AACnC,QAAA,IAAI,IAAI,CAAC,gCAAgC,KAAK,IAAI,EAAE;AAClD,YAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI,GAAG,EAAE,CAAC;SACnD;QACD,yCAAyC,EAAE,CAAC,OAAO,CAC/C,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,CAAC,gCAAiC,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;KAC7E;AAED;;;;AAIG;IACK,+BAA+B,GAAA;AACrC,QAAA,IAAI,IAAI,CAAC,gCAAgC,KAAK,IAAI,EAAE;AAClD,YAAA,gCAAgC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;SAC9C;KACF;IAED,oBAAoB,GAAA;;;QAGlB,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,EAAoB,KAAI;YACxD,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC;AAC7C,SAAC,CAAC,CAAC;;QAEH,IAAI,CAAC,aAAa,CAAC,OAAO,CACtB,CAAC,IAA+C,EAAE,IAAe,KAAI;YACnE,IAAIb,iCAAgC,EAAE;AACpC,gBAAAC,YAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aACtC;YACD,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,IAAI,KAAI;gBAChC,IAAI,CAAC,UAAU,EAAE;;;;;;;AAOf,oBAAA,OAAQ,IAAY,CAAC,IAAI,CAAC,CAAC;iBAC5B;qBAAM;oBACL,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;iBAC/C;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AACP,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,+BAA+B,EAAE,CAAC;;QAEvCK,YAAW,CAACD,kBAAiB,CAAC,CAAC;KAChC;IAEO,iBAAiB,GAAA;AACvB,QAAA,MAAM,eAAe,CAAA;AAAG,SAAA;QACxBY,oBAAmB,CAAC,eAAoC,EAAE;AACxD,YAAA,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC3C,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,SAAS,GAAG;AAChB,YAAA,0BAA0B,EAAE;AAC5B,YAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,IAAI,cAAc,CAAC,IAAI,CAAC,EAAC;AAC/D,YAAA,EAAC,OAAO,EAAEC,mBAAkB,EAAE,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAC,EAAC;YAC5E,GAAG,IAAI,CAAC,SAAS;YACjB,GAAG,IAAI,CAAC,iBAAiB;SAC1B,CAAC;AACF,QAAA,MAAM,OAAO,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;;AAGlF,QAAAD,oBAAmB,CAAC,IAAI,CAAC,cAAc,EAAE;YACvC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,OAAO;YACP,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS;AACV,SAAA,yCAAyC,IAAI,CAAC,CAAC;;AAGhD,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACzD;AAED,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YAC3B,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QAED,MAAM,SAAS,GAAqB,EAAE,CAAC;AACvC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AACrE,QAAA,eAAe,CAAC,OAAO,CAAC,IAAI,IAAG;AAC7B,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,gBAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAChC;AACH,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACnC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAqC,CAAC,CAAC;SAC/D;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAC,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;AAGO,IAAA,0BAA0B,CAAC,QAAkB,EAAA;AACnD,QAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;KACzD;AAEO,IAAA,oBAAoB,CAAC,SAAwD,EAAA;AAEnF,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,KAAK,CAAC;AAAE,YAAA,OAAO,EAAE,CAAC;;;;;;QAM3F,OAAO,OAAO,CAAC,gBAAgB,CAC3B,SAAS,EAAE,CAAC,QAAkB,KAAK,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KAC1F;AAEO,IAAA,sBAAsB,CAAC,SAAwD,EAAA;AAErF,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,KAAK,CAAC;AAAE,YAAA,OAAO,EAAE,CAAC;AAE3F,QAAA,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAChE,MAAM,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,EAAE,GAAG,SAAS,CAAC,CAAC;QAClE,MAAM,KAAK,GAAe,EAAE,CAAC;AAC7B,QAAA,MAAM,uBAAuB,GAAG,IAAI,GAAG,EAAY,CAAC;;;;;AAMpD,QAAA,YAAY,CAAC,mBAAmB,EAAE,CAAC,QAAa,KAAI;AAClD,YAAA,MAAM,KAAK,GAAQ,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC5C,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACvC,oBAAA,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;;;;AAInC,oBAAA,KAAK,CAAC,OAAO,CAAC,EAAC,GAAG,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC,CAAC;iBAC5C;aACF;iBAAM;AACL,gBAAA,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;aACzB;AACH,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,KAAK,CAAC;KACd;AAEO,IAAA,oBAAoB,CAAC,SAAwD,EAAA;QACnF,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACxD;IAEO,6BAA6B,CAAC,WAAsB,EAAE,KAAa,EAAA;AACzE,QAAA,MAAM,GAAG,GAAI,WAAmB,CAAC,KAAK,CAAC,CAAC;AACxC,QAAA,IAAI,GAAG,IAAI,GAAG,CAAC,iBAAiB,EAAE;AAChC,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAEzC,YAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,iBAAiB,CAAC;AACvC,YAAA,MAAM,kBAAkB,GAAG,CAAC,SAAqB,KAAK,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAC7F,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;AACpE,YAAA,GAAG,CAAC,iBAAiB,GAAG,CAAC,KAAwB,KAAK,QAAQ,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;SAC3F;KACF;AACF,CAAA;AAED,SAAS,aAAa,GAAA;IACpB,OAAO;QACL,MAAM,EAAE,IAAI,gBAAgB,EAAE;QAC9B,SAAS,EAAE,IAAI,iBAAiB,EAAE;QAClC,SAAS,EAAE,IAAI,iBAAiB,EAAE;QAClC,IAAI,EAAE,IAAI,YAAY,EAAE;KACzB,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAAI,KAAc,EAAA;AAC9C,IAAA,MAAM,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AACnC,IAAA,OAAO,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC;AAC3B,CAAC;AAID,SAAS,eAAe,CAAC,KAAoB,EAAA;AAC3C,IAAA,OAAQ,KAAa,CAAC,IAAI,IAAI,IAAI,CAAC;AACrC,CAAC;AAED,SAAS,cAAc,CAAI,KAAc,EAAA;AACvC,IAAA,OAAO,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,UAAU,CAAI,KAAc,EAAA;AACnC,IAAA,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,aAAa,CAAI,OAAoB,EAAA;AAC5C,IAAA,OAAO,OAAO,YAAY,QAAQ,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC;AAC3D,CAAC;AAED,SAAS,OAAO,CAAI,MAAa,EAAA;IAC/B,MAAM,GAAG,GAAQ,EAAE,CAAC;AACpB,IAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAG;AACrB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAI,KAAK,CAAC,CAAC,CAAC;SAChC;aAAM;AACL,YAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjB;AACH,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,UAAU,CAAI,KAAQ,EAAA;AAC7B,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAKD,SAAS,gBAAgB,CACrB,SAAuD,EACvD,QAAqC,UAAU,EAAA;IACjD,MAAM,GAAG,GAAU,EAAE,CAAC;AACtB,IAAA,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;AAC9B,QAAA,IAAIE,uBAAsB,CAAC,QAAQ,CAAC,EAAE;AACpC,YAAA,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;SAChC;AACD,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;SAChD;aAAM;YACL,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC3B;KACF;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAkB,EAAE,KAAa,EAAA;IACzD,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAK,QAAgB,CAAC,KAAK,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAkB,EAAA;IAC1C,OAAO,gBAAgB,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,QAAQ,CAAC;AAC3D,CAAC;AAED,SAAS,qBAAqB,CAAC,KAAU,EAAA;AACvC,IAAA,OAAO,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,YAAY,CAAI,MAAW,EAAE,EAAmC,EAAA;AACvE,IAAA,KAAK,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;QACjD,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;KACtB;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAY,EAAE,YAAoB,EAAA;IAC1D,OAAO,IAAI,KAAK,CAAC,CAAA,EAAG,IAAI,CAAwB,qBAAA,EAAA,YAAY,CAAoC,kCAAA,CAAA,CAAC,CAAC;AACpG,CAAC;AAED,MAAM,cAAc,CAAA;AAClB,IAAA,WAAA,CAAoB,OAAwB,EAAA;QAAxB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;KAAI;AAEhD,IAAA,iBAAiB,CAAI,UAAmB,EAAA;AACtC,QAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAC9C,QAAA,OAAO,IAAIC,gBAAiB,CAAC,UAAU,CAAC,CAAC;KAC1C;IAED,MAAM,kBAAkB,CAAI,UAAmB,EAAA;QAC7C,MAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;AACrD,QAAA,OAAO,IAAIA,gBAAiB,CAAC,UAAU,CAAC,CAAC;KAC1C;AAED,IAAA,iCAAiC,CAAI,UAAmB,EAAA;QACtD,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAC3D,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAA6B,CAAC,CAAC;AAC9F,QAAA,OAAO,IAAI,4BAA4B,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;KAC9E;IAED,MAAM,kCAAkC,CAAI,UAAmB,EAAA;QAE7D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAClE,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAA6B,CAAC,CAAC;AAC9F,QAAA,OAAO,IAAI,4BAA4B,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;KAC9E;AAED,IAAA,UAAU,MAAW;IAErB,aAAa,CAAC,IAAe,EAAA,GAAU;AAEvC,IAAA,WAAW,CAAC,UAAqB,EAAA;AAC/B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACnE,QAAA,OAAO,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,SAAS,CAAC;KACrC;AACF;;AC5iCD;AAoJA,IAAI,kBAAkB,GAAG,CAAC,CAAC;AAE3B;;;;AAIG;SACa,UAAU,GAAA;IACxB,OAAO,WAAW,CAAC,QAAQ,CAAC;AAC9B,CAAC;AAED;;;;;;AAMG;MACU,WAAW,CAAA;AAAxB,IAAA,WAAA,GAAA;AA+BE;;;AAGG;QACK,IAA2B,CAAA,2BAAA,GAAG,4BAA4B,CAAC;;QAqLnE,IAAQ,CAAA,QAAA,GAAgB,IAAK,CAAC;QAC9B,IAAQ,CAAA,QAAA,GAA0B,IAAK,CAAC;QAEhC,IAAS,CAAA,SAAA,GAAyB,IAAI,CAAC;QACvC,IAAc,CAAA,cAAA,GAA0B,IAAI,CAAC;QAE7C,IAAe,CAAA,eAAA,GAA4B,EAAE,CAAC;AAEtD;;;;AAIG;QACH,IAAwB,CAAA,wBAAA,GAAG,KAAK,CAAC;KAwYlC;aA5mBgB,IAAS,CAAA,SAAA,GAAqB,IAArB,CAA0B,EAAA;AAElD,IAAA,WAAW,QAAQ,GAAA;QACjB,OAAO,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,IAAI,IAAI,WAAW,EAAE,CAAC;KAC3E;AAwDD;;;;;;;;;;;;AAYG;AACH,IAAA,OAAO,mBAAmB,CACtB,QAA+B,EAAE,QAAqB,EACtD,OAAgC,EAAA;AAClC,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;QACrC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;AAIG;AACH,IAAA,OAAO,oBAAoB,GAAA;AACzB,QAAA,WAAW,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;KAC7C;IAED,OAAO,iBAAiB,CAAC,MAA8C,EAAA;QACrE,OAAO,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;KACvD;AAED;;;AAGG;IACH,OAAO,sBAAsB,CAAC,SAA6B,EAAA;QACzD,OAAO,WAAW,CAAC,QAAQ,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;KAC/D;AAED;;;;AAIG;AACH,IAAA,OAAO,iBAAiB,GAAA;AACtB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;KACjD;AAED,IAAA,OAAO,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAAA;QAC7E,OAAO,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;KAChE;AAED,IAAA,OAAO,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;QAClF,OAAO,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KACpE;AAED,IAAA,OAAO,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;QAClF,OAAO,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KACpE;AAED,IAAA,OAAO,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAA;QACnE,OAAO,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KAC1D;AAED,IAAA,OAAO,gBAAgB,CAAC,SAAoB,EAAE,QAAgB,EAAA;QAC5D,OAAO,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KACnE;AAED;;;;;AAKG;AACH,IAAA,OAAO,kCAAkC,CAAC,SAAoB,EAAE,QAAgB,EAAA;QAC9E,OAAO,WAAW,CAAC,QAAQ,CAAC,kCAAkC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KACrF;AAOD,IAAA,OAAO,gBAAgB,CAAC,KAAU,EAAE,QAInC,EAAA;QACC,OAAO,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;KAC/D;AAYD,IAAA,OAAO,MAAM,CACT,KAAuB,EAAE,aAAsB,EAAE,KAAiC,EAAA;AACpF,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,EAAEC,kBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;KACpF;;AAOD,IAAA,OAAO,GAAG,CACN,KAAU,EAAE,aAAA,GAAqB,QAAQ,CAAC,kBAAkB,EAC5D,KAAqB,GAAA,WAAW,CAAC,OAAO,EAAA;AAC1C,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;KACjE;AAED;;;;AAIG;IACH,OAAO,qBAAqB,CAAI,EAAW,EAAA;QACzC,OAAO,WAAW,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;KACvD;IAED,OAAO,eAAe,CAAI,SAAkB,EAAA;QAC1C,OAAO,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;KACxD;AAED,IAAA,OAAO,kBAAkB,GAAA;AACvB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;KAClD;AAED,IAAA,OAAO,OAAO,CAAC,MAAa,EAAE,EAAY,EAAE,OAAa,EAAA;AACvD,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;KAC1D;AAED,IAAA,WAAW,QAAQ,GAAA;AACjB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;KACtC;AAED,IAAA,WAAW,QAAQ,GAAA;AACjB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;KACtC;AAED,IAAA,OAAO,YAAY,GAAA;AACjB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;KAC5C;AAmBD;;;;;;;;;;;;AAYG;AACH,IAAA,mBAAmB,CACf,QAA+B,EAAE,QAAqB,EACtD,OAAgC,EAAA;QAClC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;SACjF;AAED,QAAA,WAAW,CAAC,2BAA2B,GAAG,OAAO,EAAE,QAAQ,CAAC;AAE5D,QAAA,WAAW,CAAC,wCAAwC,GAAG,OAAO,EAAE,sBAAsB,CAAC;AAEvF,QAAA,WAAW,CAAC,0CAA0C,GAAG,OAAO,EAAE,wBAAwB,CAAC;AAE3F,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;;;;QAMnEC,oCAAmC,CAAC,IAAI,CAAC,CAAC;KAC3C;AAED;;;;AAIG;IACH,oBAAoB,GAAA;QAClB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAK,CAAC;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAK,CAAC;AACtB,QAAA,WAAW,CAAC,2BAA2B,GAAG,SAAS,CAAC;QACpDA,oCAAmC,CAAC,KAAK,CAAC,CAAC;KAC5C;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,8BAA8B,EAAE,CAAC;AACtC,QAAAC,wBAAuB,EAAE,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;SACtC;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;AAEnE,QAAAC,4BAA2B,CACvB,IAAI,CAAC,qCAAqC,IAAI,iCAAiC,CAAC,CAAC;;AAErF,QAAAC,6BAA4B,CACxB,IAAI,CAAC,uCAAuC,IAAI,mCAAmC,CAAC,CAAC;;;;AAKzF,QAAA,IAAI;YACF,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC9B;gBAAS;AACR,YAAA,IAAI;AACF,gBAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE;oBACtC,IAAI,CAAC,qBAAqB,EAAE,CAAC;iBAC9B;aACF;oBAAS;AACR,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,gBAAA,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;AAC1C,gBAAA,IAAI,CAAC,qCAAqC,GAAG,SAAS,CAAC;AACvD,gBAAA,IAAI,CAAC,uCAAuC,GAAG,SAAS,CAAC;AACzD,gBAAA,IAAI,CAAC,2BAA2B,GAAG,4BAA4B,CAAC;aACjE;SACF;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,iBAAiB,CAAC,MAA8C,EAAA;AAC9D,QAAA,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;AACzB,YAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACvE;AAED,QAAA,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACtD;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,sBAAsB,CAAC,SAA6B,EAAA;AAClD,QAAA,IAAI,CAAC,qBAAqB,CAAC,gCAAgC,EAAE,2BAA2B,CAAC,CAAC;;;;;QAM1F,IAAI,CAAC,8BAA8B,EAAE,CAAC;;;AAItC,QAAA,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC,QAAQ,CAAC;AACnD,QAAA,IAAI,CAAC,qCAAqC,GAAG,SAAS,CAAC,sBAAsB,CAAC;AAC9E,QAAA,IAAI,CAAC,uCAAuC,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAClF,IAAI,CAAC,2BAA2B,GAAG,SAAS,CAAC,kBAAkB,IAAI,4BAA4B,CAAC;;;AAGhG,QAAA,IAAI,CAAC,qCAAqC,GAAGC,4BAA2B,EAAE,CAAC;AAC3E,QAAAF,4BAA2B,CAAC,IAAI,CAAC,iCAAiC,EAAE,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,uCAAuC,GAAGG,6BAA4B,EAAE,CAAC;AAC9E,QAAAF,6BAA4B,CAAC,IAAI,CAAC,mCAAmC,EAAE,CAAC,CAAC;AACzE,QAAA,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;AAChD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;KAC1C;AAWD,IAAA,MAAM,CAAI,KAAuB,EAAE,aAAsB,EAAE,KAAiC,EAAA;AAE1F,QAAA,IAAI,KAAgB,KAAK,OAAO,EAAE;AAChC,YAAA,OAAO,IAAW,CAAC;SACpB;QACD,MAAM,SAAS,GAAG,EAAkB,CAAC;AACrC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,EAAEJ,kBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3F,OAAO,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,CAAQ;AAC9D,YAAA,MAAM,CAAC;KACtC;;IAOD,GAAG,CAAC,KAAU,EAAE,aAAqB,GAAA,QAAQ,CAAC,kBAAkB,EAC5D,KAAA,GAAqB,WAAW,CAAC,OAAO,EAAA;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;KACjD;AAED,IAAA,qBAAqB,CAAI,EAAW,EAAA;QAClC,OAAO,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,EAAE,CAAC,CAAC;KACpE;AAED,IAAA,OAAO,CAAC,MAAa,EAAE,EAAY,EAAE,OAAa,EAAA;AAChD,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAClC;IAED,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAAA;AACtE,QAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,CAAC;QACzE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACjD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;AAC3E,QAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC,CAAC;QAC/E,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACrD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,kCAAkC,CAAC,SAAoB,EAAE,QAAgB,EAAA;AACvE,QAAA,IAAI,CAAC,qBAAqB,CACtB,4CAA4C,EAC5C,6EAA6E,CAAC,CAAC;QACnF,IAAI,CAAC,QAAQ,CAAC,kCAAkC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACtE,QAAA,OAAO,IAAI,CAAC;KACb;IAED,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;AAC3E,QAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC,CAAC;QAC/E,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACrD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAA;AAC5D,QAAA,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,wBAAwB,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC3C,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;AAEG;IACH,gBAAgB,CAAC,KAAU,EAAE,QAA+D,EAAA;AAE1F,QAAA,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAChD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,gBAAgB,CAAC,SAAoB,EAAE,QAAgB,EAAA;AACrD,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,EAAC,GAAG,EAAE,EAAC,QAAQ,EAAE,WAAW,EAAE,IAAK,EAAC,EAAC,CAAC,CAAC;KACjF;AAED,IAAA,eAAe,CAAI,IAAa,EAAA;QAC9B,MAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;AACjE,QAAA,MAAM,QAAQ,GAAG,CAAA,IAAA,EAAO,kBAAkB,EAAE,EAAE,CAAC;AAC/C,QAAA,qBAAqB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAElD,QAAA,IAAItB,wBAAuB,CAAC,IAAI,CAAC,EAAE;AACjC,YAAA,MAAM,IAAI,KAAK,CACX,cAAc,IAAI,CAAC,IAAI,CAA6B,2BAAA,CAAA;AACpD,gBAAA,CAAA,2EAAA,CAA6E,CAAC,CAAC;SACpF;AAED,QAAA,MAAM,YAAY,GAAI,IAAY,CAAC,IAAI,CAAC;QAExC,IAAI,CAAC,YAAY,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,CAAkB,eAAA,EAAAF,UAAS,CAAC,IAAI,CAAC,CAA0B,wBAAA,CAAA,CAAC,CAAC;SAC9E;AAED,QAAA,MAAM,gBAAgB,GAAG,IAAIU,wBAAgB,CAAC,YAAY,CAAC,CAAC;QAC5D,MAAM,aAAa,GAAG,MAAK;YACzB,MAAM,YAAY,GACd,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,CAAA,CAAA,EAAI,QAAQ,CAAE,CAAA,EAAE,IAAI,CAAC,aAAa,CAC9D,CAAC;AACpB,YAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAK;AACrC,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAACqB,yBAAwB,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC;AAC1E,gBAAA,MAAM,OAAO,GAAG,YAAY,GAAG,IAAI,yBAAyB,CAAC,YAAY,CAAC;AAC3C,oBAAA,IAAI,iCAAiC,CAAC,YAAY,CAAC,CAAC;gBACnF,OAAO,CAAC,UAAU,EAAE,CAAC;AACrB,gBAAA,OAAO,OAAO,CAAC;AACjB,aAAC,CAAC,CAAC;AACL,SAAC,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;AAC9D,QAAA,MAAM,MAAM,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC3D,QAAA,MAAM,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,aAAa,EAAE,CAAC;AACrE,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACnC,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;AAGG;AACH,IAAA,IAAY,QAAQ,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,gDAAA,CAAkD,CAAC,CAAC;SACrE;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAED;;;AAGG;AACH,IAAA,IAAY,aAAa,GAAA;AACvB,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;SAChD;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;IAEO,qBAAqB,CAAC,UAAkB,EAAE,iBAAyB,EAAA;AACzE,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAChC,YAAA,MAAM,IAAI,KAAK,CACX,CAAA,OAAA,EAAU,iBAAiB,CAAuD,qDAAA,CAAA;gBAClF,CAAmD,gDAAA,EAAA,UAAU,CAAK,GAAA,CAAA,CAAC,CAAC;SACzE;KACF;AAED;;;;;;;;;;;AAWG;IACK,8BAA8B,GAAA;;;QAGpC,IAAI,CAAC,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAClE,YAAAC,wCAAuC,EAAE,CAAC;SAC3C;AACD,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;KACtC;IAEO,qBAAqB,GAAA;QAC3B,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AACvC,YAAA,IAAI;gBACF,OAAO,CAAC,OAAO,EAAE,CAAC;aACnB;YAAC,OAAO,CAAC,EAAE;AACV,gBAAA,UAAU,EAAE,CAAC;AACb,gBAAA,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE;oBACjD,SAAS,EAAE,OAAO,CAAC,iBAAiB;AACpC,oBAAA,UAAU,EAAE,CAAC;AACd,iBAAA,CAAC,CAAC;aACJ;AACH,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAE1B,IAAI,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE;AACxD,YAAA,MAAM,KAAK,CACP,CAAA,EAAG,UAAU,CAAI,CAAA,GAAC,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,YAAY,EAAI,CAAA,CAAA;AACnE,gBAAA,CAAA,2BAAA,CAA6B,CAAC,CAAC;SACpC;KACF;IAED,2BAA2B,GAAA;AACzB,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC;AACtD,QAAA,MAAM,kBAAkB,GAAG,WAAW,CAAC,2BAA2B,CAAC;;AAGnE,QAAA,IAAI,CAAC,eAAe,IAAI,CAAC,kBAAkB,EAAE;AAC3C,YAAA,OAAO,0CAA0C,CAAC;SACnD;;AAGD,QAAA,OAAO,eAAe,EAAE,aAAa,IAAI,kBAAkB,EAAE,aAAa;YACtE,IAAI,CAAC,2BAA2B,EAAE,CAAC;KACxC;IAED,iCAAiC,GAAA;;QAE/B,OAAO,IAAI,CAAC,qCAAqC;AAC7C,YAAA,WAAW,CAAC,wCAAwC,IAAI,iCAAiC,CAAC;KAC/F;IAED,mCAAmC,GAAA;;QAEjC,OAAO,IAAI,CAAC,uCAAuC;AAC/C,YAAA,WAAW,CAAC,0CAA0C;AACtD,YAAA,mCAAmC,CAAC;KACzC;IAED,2BAA2B,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,gBAAgB;YAClD,WAAW,CAAC,2BAA2B,EAAE,gBAAgB;AACzD,YAAA,0CAA0C,CAAC;KAChD;IAED,qBAAqB,GAAA;QACnB,OAAO,IAAI,CAAC,2BAA2B,CAAC;KACzC;IAED,qBAAqB,GAAA;;AAEnB,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAChC,OAAO;SACR;;;QAGD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;AACxD,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SAC/B;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE;AACtC,gBAAA,MAAM,CAAC,CAAC;aACT;iBAAM;AACL,gBAAA,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE;AACxD,oBAAA,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;AACvC,oBAAA,UAAU,EAAE,CAAC;AACd,iBAAA,CAAC,CAAC;aACJ;SACF;gBAAS;AACR,YAAA,YAAY,CAAC,qBAAqB,IAAI,CAAC;SACxC;KACF;AAED;;;;AAIG;IACH,YAAY,GAAA;QACV,IAAI,CAAC,MAAM,CAAClC,gBAAe,CAAC,CAAC,KAAK,EAAE,CAAC;KACtC;;AAGH;;;;;;;;AAQG;AACI,MAAM,OAAO,GAAkB,YAAY;AAElD;;;;;;;;;;;;;;;;;;;;;AAqBG;AACa,SAAA,MAAM,CAAC,MAAa,EAAE,EAAY,EAAA;AAChD,IAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;;IAErC,OAAO,YAAA;QACL,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC3C,KAAC,CAAC;AACJ,CAAC;AAED;;AAEG;MACU,kBAAkB,CAAA;AAC7B,IAAA,WAAA,CAAoB,UAAoC,EAAA;QAApC,IAAU,CAAA,UAAA,GAAV,UAAU,CAA0B;KAAI;IAEpD,UAAU,GAAA;AAChB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACpC,IAAI,SAAS,EAAE;AACb,YAAA,WAAW,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;SAC/C;KACF;IAED,MAAM,CAAC,MAAa,EAAE,EAAY,EAAA;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC;;QAElB,OAAO,YAAA;YACL,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvC,SAAC,CAAC;KACH;AACF,CAAA;AAOe,SAAA,UAAU,CAAC,SAA6B,EAAE,EAAkB,EAAA;IAE1E,IAAI,EAAE,EAAE;;QAEN,OAAO,YAAA;AACL,YAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;YACrC,IAAI,SAAS,EAAE;AACb,gBAAA,OAAO,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;aAC3C;AACD,YAAA,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACxB,SAAC,CAAC;KACH;IACD,OAAO,IAAI,kBAAkB,CAAC,MAAM,SAAS,CAAC,CAAC;AACjD;;ACv2BA;;;;AAIG;AAKH;AACA;AACA;AACA,UAAU,CAAC,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;AAE/C;AACA;AACA;AACA;AACA;AACA,UAAU,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AAE7C,SAAS,cAAc,CAAC,qBAA8B,EAAA;AACpD,IAAA,OAAO,MAAK;AACV,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;AACrC,QAAA,IAAI,OAAO,CAAC,2BAA2B,EAAE,KAAK,qBAAqB,EAAE;YACnE,OAAO,CAAC,kBAAkB,EAAE,CAAC;AAC7B,YAAA,0BAA0B,EAAE,CAAC;SAC9B;AACH,KAAC,CAAC;AACJ,CAAC;AAED;;;;;;;AAOG;AACH;AACO,MAAM,oCAAoC,GAAG;;ACxCpD;;;;AAIG;;ACJH;AASA;;ACTA;;ACRA;;AAEG;;;;"}