/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["eu", [["g", "a"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["I", "A", "A", "A", "O", "O", "L"], ["ig.", "al.", "ar.", "az.", "og.", "or.", "lr."], ["igandea", "astelehena", "asteartea", "asteazkena", "osteguna", "ostirala", "larunbata"], ["ig.", "al.", "ar.", "az.", "og.", "or.", "lr."]], u, [["U", "O", "M", "A", "M", "E", "U", "A", "I", "U", "A", "A"], ["urt.", "ots.", "mar.", "api.", "mai.", "eka.", "uzt.", "abu.", "ira.", "urr.", "aza.", "abe."], ["urtarrilak", "otsailak", "martxoak", "apirilak", "maiatzak", "ekainak", "uztailak", "abuztuak", "irailak", "urriak", "azaroak", "abenduak"]], [["U", "O", "M", "A", "M", "E", "U", "A", "I", "U", "A", "A"], ["urt.", "ots.", "mar.", "api.", "mai.", "eka.", "uzt.", "abu.", "ira.", "urr.", "aza.", "abe."], ["urtarrila", "otsaila", "martxoa", "apirila", "maiatza", "ekaina", "uztaila", "abuztua", "iraila", "urria", "azaroa", "abendua"]], [["a", "o"], ["K.a.", "K.o."], ["K.a.", "Kristo ondoren"]], 1, [6, 0], ["yy/M/d", "y('e')'ko' MMM d('a')", "y('e')'ko' MMMM'ren' d('a')", "y('e')'ko' MMMM'ren' d('a'), EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss (z)", "HH:mm:ss (zzzz)"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "−", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "% #,##0", "#,##0.00 ¤", "#E0"], "EUR", "€", "euroa", { "BYN": [u, "р."], "ESP": ["₧"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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