export declare type WeakAssert = (input: unknown, message?: string) => void;
export declare type SubType<Input, Output> = Output extends Input ? Output : never;
export declare type Assert<Input = unknown, Output = Input> = (input: Input, message?: string) => asserts input is SubType<Input, Output>;
export declare type Check<Input = unknown, Output = Input> = (input: Input) => input is SubType<Input, Output>;
export declare const defaultAssert: WeakAssert;
export declare const assert: Assert<boolean, true>;
export declare function setBaseAssert(assert?: WeakAssert): void;
export declare const safeJsonParse: (json: string) => unknown;
export declare function isUnknown(_input: unknown): _input is unknown;
export declare function isNever(_input: never, message?: string): never;
export declare function isNotNull<T>(input: null | T, message?: string): asserts input is T;
export declare function isNotUndefined<T>(input: undefined | T, message?: string): asserts input is T;
export declare function isNotVoid<T>(input: T, message?: string): asserts input is Exclude<T, undefined | null | void>;
export declare function isExactly<Input, Output>(input: Input, value: Output, message?: string): asserts input is SubType<Input, Output>;
export declare function isBoolean(input: unknown, message?: string): asserts input is boolean;
export declare function isNumber(input: unknown, message?: string): asserts input is number;
export declare function isString(input: unknown, message?: string): asserts input is string;
export declare function isDate(input: unknown, message?: string): asserts input is Date;
export declare function isRecord(input: unknown, message?: string): asserts input is Record<string, unknown>;
export declare function isRecordWithKeys<K extends string>(input: unknown, keys: K[], message?: string): asserts input is {
    readonly [Key in K]: unknown;
};
export declare function isArray(input: unknown, message?: string): asserts input is unknown[];
export declare function isRecordOfType<T>(input: unknown, assertT: Assert<unknown, T>, message?: string, itemMessage?: string): asserts input is Record<string, T>;
export declare function isArrayOfType<T>(input: unknown, assertT: Assert<unknown, T>, message?: string, itemMessage?: string): asserts input is T[];
export declare function isOptionOfType<Input, Output>(input: Input | undefined, assertT: Assert<Input, Output>, message?: string): asserts input is SubType<Input, Output | undefined>;
export declare function isOneOf<Input, Output>(input: Input, values: readonly Output[], message?: string): asserts input is SubType<Input, Output>;
export declare function isOneOfType<T>(input: unknown, assertT: Assert<unknown, T>[], message?: string, itemMessage?: string): asserts input is T;
export declare function isInstanceOf<T>(input: unknown, constructor: new (...args: any[]) => T, message?: string): asserts input is T;
export declare function isPromise(input: unknown, message?: string): asserts input is Promise<unknown>;
export declare function check<Input, Output>(assertT: Assert<Input, Output>): Check<Input, Output>;
