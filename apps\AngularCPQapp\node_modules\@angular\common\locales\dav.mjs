/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["dav", [["Luma lwa K", "luma lwa p"], u, u], u, [["J", "J", "K", "K", "K", "K", "N"], ["<PERSON><PERSON>", "<PERSON>", "<PERSON>w", "Kad", "Kan", "Ka<PERSON>", "<PERSON>u"], ["<PERSON>uku ja jumwa", "<PERSON><PERSON><PERSON> jimweri", "<PERSON><PERSON>uka kawi", "<PERSON><PERSON>uka kadadu", "<PERSON><PERSON>uka kana", "<PERSON><PERSON><PERSON> kasanu", "<PERSON><PERSON>a nguwo"], ["Jum", "<PERSON>", "<PERSON>w", "Kad", "Kan", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "W", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["Imb", "Kaw", "Kad", "Kan", "<PERSON>s", "Kar", "<PERSON>fu", "<PERSON>n", "Ike", "Iku", "Imw", "Iwi"], ["<PERSON>ri ghwa imbiri", "<PERSON>ri ghwa kawi", "<PERSON>ri ghwa kadadu", "<PERSON>ri ghwa kana", "<PERSON>ri ghwa kasanu", "<PERSON>ri ghwa ka<PERSON>adu", "<PERSON>ri ghwa mfungade", "<PERSON>ri ghwa wunyanya", "<PERSON>ri ghwa ikenda", "Mori ghwa ikumi", "Mori ghwa ikumi na imweri", "Mori ghwa ikumi na iwi"]], u, [["KK", "BK"], u, ["Kabla ya Kristo", "Baada ya Kristo"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "KES", "Ksh", "Shilingi ya Kenya", { "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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