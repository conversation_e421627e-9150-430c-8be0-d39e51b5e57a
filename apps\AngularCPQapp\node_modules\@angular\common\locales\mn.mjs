/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["mn", [["ү.ө.", "ү.х."], u, u], u, [["Ня", "Да", "Мя", "Лх", "Пү", "Ба", "Бя"], u, ["ням", "даваа", "мягмар", "лхагва", "пүрэв", "баасан", "бямба"], ["Ня", "Да", "Мя", "Лх", "Пү", "Ба", "Бя"]], [["Ня", "Да", "Мя", "Лх", "Пү", "Ба", "Бя"], u, ["Ням", "Даваа", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Лхагва", "Пүрэв", "<PERSON>а<PERSON><PERSON><PERSON><PERSON>", "Бямба"], ["Ня", "Да", "Мя", "Лх", "Пү", "Ба", "Бя"]], [["I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X", "XI", "XII"], ["1-р сар", "2-р сар", "3-р сар", "4-р сар", "5-р сар", "6-р сар", "7-р сар", "8-р сар", "9-р сар", "10-р сар", "11-р сар", "12-р сар"], ["нэгдүгээр сар", "хоёрдугаар сар", "гуравдугаар сар", "дөрөвдүгээр сар", "тавдугаар сар", "зургаадугаар сар", "долоодугаар сар", "наймдугаар сар", "есдүгээр сар", "аравдугаар сар", "арван нэгдүгээр сар", "арван хоёрдугаар сар"]], [["I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X", "XI", "XII"], ["1-р сар", "2-р сар", "3-р сар", "4-р сар", "5-р сар", "6-р сар", "7-р сар", "8-р сар", "9-р сар", "10-р сар", "11-р сар", "12-р сар"], ["Нэгдүгээр сар", "Хоёрдугаар сар", "Гуравдугаар сар", "Дөрөвдүгээр сар", "Тавдугаар сар", "Зургаадугаар сар", "Долоодугаар сар", "Наймдугаар сар", "Есдүгээр сар", "Аравдугаар сар", "Арван нэгдүгээр сар", "Арван хоёрдугаар сар"]], [["МЭӨ", "МЭ"], u, ["манай эриний өмнөх", "манай эриний"]], 1, [6, 0], ["y.MM.dd", "y 'оны' MMM'ын' d", "y 'оны' MMMM'ын' d", "y 'оны' MMMM'ын' d, EEEE 'гараг'"], ["HH:mm", "HH:mm:ss", "HH:mm:ss (z)", "HH:mm:ss (zzzz)"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "MNT", "₮", "Монгол төгрөг", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "MNT": ["₮"], "PHP": [u, "₱"], "SEK": [u, "кр"], "THB": ["฿"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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