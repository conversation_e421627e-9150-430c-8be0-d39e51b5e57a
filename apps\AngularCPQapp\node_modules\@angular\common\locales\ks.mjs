/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ks", [["AM", "PM"], u, u], u, [["ا", "ژ", "ب", "ب", "ب", "ج", "ب"], ["آتھوار", "ژٔندٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"], ["اَتھوار", "ژٔندرٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"], ["آتھوار", "ژٔندٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"]], u, [["ج", "ف", "م", "ا", "م", "ج", "ج", "ا", "س", "س", "ا", "ن"], ["جنؤری", "فرؤری", "مارٕچ", "اپریل", "مئی", "جوٗن", "جوٗلایی", "اگست", "ستمبر", "اکتوٗبر", "نومبر", "دسمبر"], u], u, [["بی سی", "اے ڈی"], u, ["قبٕل مسیٖح", "عیٖسوی سنہٕ"]], 0, [0, 0], ["M/d/yy", "MMM d, y", "MMMM d, y", "EEEE, MMMM d, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{0} پٮ۪ٹھۍ {1}", u], [".", "،", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "INR", "₹", "ہِندُستٲنۍ رۄپَے", {}, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,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