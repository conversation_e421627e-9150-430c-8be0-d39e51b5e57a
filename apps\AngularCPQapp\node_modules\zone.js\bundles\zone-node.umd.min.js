"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign.apply(this,arguments)};
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e=globalThis;function t(t){return(e.__Zone_symbol_prefix||"__zone_symbol__")+t}function n(){var n,r=e.performance;function o(e){r&&r.mark&&r.mark(e)}function i(e,t){r&&r.measure&&r.measure(e,t)}o("Zone");var a=function(){function r(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new u(this,this._parent&&this._parent._zoneDelegate,t)}return r.assertZonePatched=function(){if(e.Promise!==j.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")},Object.defineProperty(r,"root",{get:function(){for(var e=n.current;e.parent;)e=e.parent;return e},enumerable:!1,configurable:!0}),Object.defineProperty(r,"current",{get:function(){return z.zone},enumerable:!1,configurable:!0}),Object.defineProperty(r,"currentTask",{get:function(){return C},enumerable:!1,configurable:!0}),r.__load_patch=function(r,a,s){if(void 0===s&&(s=!1),j.hasOwnProperty(r)){var c=!0===e[t("forceDuplicateZoneCheck")];if(!s&&c)throw Error("Already loaded patch: "+r)}else if(!e["__Zone_disable_"+r]){var u="Zone:"+r;o(u),j[r]=a(e,n,O),i(u,u)}},Object.defineProperty(r.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),r.prototype.get=function(e){var t=this.getZoneWith(e);if(t)return t._properties[e]},r.prototype.getZoneWith=function(e){for(var t=this;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null},r.prototype.fork=function(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)},r.prototype.wrap=function(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);var n=this._zoneDelegate.intercept(this,e,t),r=this;return function(){return r.runGuarded(n,this,arguments,t)}},r.prototype.run=function(e,t,n,r){z={parent:z,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,r)}finally{z=z.parent}},r.prototype.runGuarded=function(e,t,n,r){void 0===t&&(t=null),z={parent:z,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,r)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{z=z.parent}},r.prototype.runTask=function(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||y).name+"; Execution: "+this.name+")");var r=e,o=e.type,i=e.data,a=void 0===i?{}:i,s=a.isPeriodic,c=void 0!==s&&s,u=a.isRefreshable,l=void 0!==u&&u;if(e.state!==m||o!==P&&o!==D){var f=e.state!=w;f&&r._transitionTo(w,b);var h=C;C=r,z={parent:z,zone:this};try{o!=D||!e.data||c||l||(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,r,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{var p=e.state;if(p!==m&&p!==Z)if(o==P||c||l&&p===T)f&&r._transitionTo(b,w,T);else{var v=r._zoneDelegates;this._updateTaskCount(r,-1),f&&r._transitionTo(m,w,m),l&&(r._zoneDelegates=v)}z=z.parent,C=h}}},r.prototype.scheduleTask=function(e){if(e.zone&&e.zone!==this)for(var t=this;t;){if(t===e.zone)throw Error("can not reschedule task to ".concat(this.name," which is descendants of the original zone ").concat(e.zone.name));t=t.parent}e._transitionTo(T,m);var n=[];e._zoneDelegates=n,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(Z,T,m),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===n&&this._updateTaskCount(e,1),e.state==T&&e._transitionTo(b,T),e},r.prototype.scheduleMicroTask=function(e,t,n,r){return this.scheduleTask(new l(S,e,t,n,r,void 0))},r.prototype.scheduleMacroTask=function(e,t,n,r,o){return this.scheduleTask(new l(D,e,t,n,r,o))},r.prototype.scheduleEventTask=function(e,t,n,r,o){return this.scheduleTask(new l(P,e,t,n,r,o))},r.prototype.cancelTask=function(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||y).name+"; Execution: "+this.name+")");if(e.state===b||e.state===w){e._transitionTo(E,b,w);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(Z,E),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(m,E),e.runCount=-1,e}},r.prototype._updateTaskCount=function(e,t){var n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(var r=0;r<n.length;r++)n[r]._updateTaskCount(e.type,t)},r}();(n=a).__symbol__=t;var s,c={name:"",onHasTask:function(e,t,n,r){return e.hasTask(n,r)},onScheduleTask:function(e,t,n,r){return e.scheduleTask(n,r)},onInvokeTask:function(e,t,n,r,o,i){return e.invokeTask(n,r,o,i)},onCancelTask:function(e,t,n,r){return e.cancelTask(n,r)}},u=function(){function e(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this._zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this._zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this._zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this._zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this._zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this._zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this._zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;var r=n&&n.onHasTask;(r||t&&t._hasTaskZS)&&(this._hasTaskZS=r?n:c,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,n.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this._zone),n.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this._zone),n.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this._zone))}return Object.defineProperty(e.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),e.prototype.fork=function(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new a(e,t)},e.prototype.intercept=function(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t},e.prototype.invoke=function(e,t,n,r,o){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,r,o):t.apply(n,r)},e.prototype.handleError=function(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)},e.prototype.scheduleTask=function(e,t){var n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),(n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t))||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=S)throw new Error("Task is missing scheduleFn.");k(t)}return n},e.prototype.invokeTask=function(e,t,n,r){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,r):t.callback.apply(n,r)},e.prototype.cancelTask=function(e,t){var n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n},e.prototype.hasTask=function(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}},e.prototype._updateTaskCount=function(e,t){var n=this._taskCounts,r=n[e],o=n[e]=r+t;if(o<0)throw new Error("More tasks executed then were scheduled.");0!=r&&0!=o||this.hasTask(this._zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})},e}(),l=function(){function t(n,r,o,i,a,s){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=n,this.source=r,this.data=i,this.scheduleFn=a,this.cancelFn=s,!o)throw new Error("callback is not defined");this.callback=o;var c=this;this.invoke=n===P&&i&&i.useG?t.invokeTask:function(){return t.invokeTask.call(e,c,this,arguments)}}return t.invokeTask=function(e,t,n){e||(e=this),I++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==I&&g(),I--}},Object.defineProperty(t.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),t.prototype.cancelScheduleRequest=function(){this._transitionTo(m,T)},t.prototype._transitionTo=function(e,t,n){if(this._state!==t&&this._state!==n)throw new Error("".concat(this.type," '").concat(this.source,"': can not transition to '").concat(e,"', expecting state '").concat(t,"'").concat(n?" or '"+n+"'":"",", was '").concat(this._state,"'."));this._state=e,e==m&&(this._zoneDelegates=null)},t.prototype.toString=function(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)},t.prototype.toJSON=function(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}},t}(),f=t("setTimeout"),h=t("Promise"),p=t("then"),v=[],d=!1;function _(t){if(s||e[h]&&(s=e[h].resolve(0)),s){var n=s[p];n||(n=s.then),n.call(s,t)}else e[f](t,0)}function k(e){0===I&&0===v.length&&_(g),e&&v.push(e)}function g(){if(!d){for(d=!0;v.length;){var e=v;v=[];for(var t=0;t<e.length;t++){var n=e[t];try{n.zone.runTask(n,null,null)}catch(e){O.onUnhandledError(e)}}}O.microtaskDrainDone(),d=!1}}var y={name:"NO ZONE"},m="notScheduled",T="scheduling",b="scheduled",w="running",E="canceling",Z="unknown",S="microTask",D="macroTask",P="eventTask",j={},O={symbol:t,currentZoneFrame:function(){return z},onUnhandledError:A,microtaskDrainDone:A,scheduleMicroTask:k,showUncaughtError:function(){return!a[t("ignoreConsoleErrorUncaughtError")]},patchEventTarget:function(){return[]},patchOnProperties:A,patchMethod:function(){return A},bindArguments:function(){return[]},patchThen:function(){return A},patchMacroTask:function(){return A},patchEventPrototype:function(){return A},isIEOrEdge:function(){return!1},getGlobalObjects:function(){},ObjectDefineProperty:function(){return A},ObjectGetOwnPropertyDescriptor:function(){},ObjectCreate:function(){},ArraySlice:function(){return[]},patchClass:function(){return A},wrapWithCurrentZone:function(){return A},filterProperties:function(){return[]},attachOriginToPatched:function(){return A},_redefineProperty:function(){return A},patchCallbacks:function(){return A},nativeScheduleMicroTask:_},z={parent:null,zone:new a(null,null)},C=null,I=0;function A(){}return i("Zone","Zone"),a}var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,i=Object.getPrototypeOf,a=Array.prototype.slice,s="addEventListener",c="removeEventListener",u="true",l="false",f=t("");function h(e,t){return Zone.current.wrap(e,t)}function p(e,t,n,r,o){return Zone.current.scheduleMacroTask(e,t,n,r,o)}var v=t,d="undefined"!=typeof window,_=d?window:void 0,k=d&&_||globalThis,g="removeAttribute";function y(e,t){for(var n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=h(e[n],t+"_"+n));return e}var m="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,T=!("nw"in k)&&void 0!==k.process&&"[object process]"===k.process.toString(),b=!T&&!m&&!(!d||!_.HTMLElement),w=void 0!==k.process&&"[object process]"===k.process.toString()&&!m&&!(!d||!_.HTMLElement),E={},Z=v("enable_beforeunload"),S=function(e){if(e=e||k.event){var t=E[e.type];t||(t=E[e.type]=v("ON_PROPERTY"+e.type));var n,r=this||e.target||k,o=r[t];return b&&r===_&&"error"===e.type?!0===(n=o&&o.call(this,e.message,e.filename,e.lineno,e.colno,e.error))&&e.preventDefault():(n=o&&o.apply(this,arguments),"beforeunload"===e.type&&k[Z]&&"string"==typeof n?e.returnValue=n:null==n||n||e.preventDefault()),n}};function D(e,t,n){var i=r(e,t);if(!i&&n&&r(n,t)&&(i={enumerable:!0,configurable:!0}),i&&i.configurable){var a=v("on"+t+"patched");if(!e.hasOwnProperty(a)||!e[a]){delete i.writable,delete i.value;var s=i.get,c=i.set,u=t.slice(2),l=E[u];l||(l=E[u]=v("ON_PROPERTY"+u)),i.set=function(t){var n=this;n||e!==k||(n=k),n&&("function"==typeof n[l]&&n.removeEventListener(u,S),c&&c.call(n,null),n[l]=t,"function"==typeof t&&n.addEventListener(u,S,!1))},i.get=function(){var n=this;if(n||e!==k||(n=k),!n)return null;var r=n[l];if(r)return r;if(s){var o=s.call(this);if(o)return i.set.call(this,o),"function"==typeof n[g]&&n.removeAttribute(t),o}return null},o(e,t,i),e[a]=!0}}}function P(e,t,n){if(t)for(var r=0;r<t.length;r++)D(e,"on"+t[r],n);else{var o=[];for(var i in e)"on"==i.slice(0,2)&&o.push(i);for(var a=0;a<o.length;a++)D(e,o[a],n)}}var j=!1;function O(e,t,n){for(var o=e;o&&!o.hasOwnProperty(t);)o=i(o);!o&&e[t]&&(o=e);var a=v(t),s=null;if(o&&(!(s=o[a])||!o.hasOwnProperty(a))&&(s=o[a]=o[t],function c(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}(o&&r(o,t)))){var u=n(s,a,t);o[t]=function(){return u(this,arguments)},I(o[t],s),j&&function e(t,n){"function"==typeof Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(n,e,{get:function(){return t[e]},set:function(n){(!r||r.writable&&"function"==typeof r.set)&&(t[e]=n)},enumerable:!r||r.enumerable,configurable:!r||r.configurable})}))}(s,o[t])}return s}function z(e,t,n){var r=null;function o(e){var t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=O(e,t,(function(e){return function(t,r){var i=n(t,r);return i.cbIdx>=0&&"function"==typeof r[i.cbIdx]?p(i.name,r[i.cbIdx],i,o):e.apply(t,r)}}))}function C(e,t,n){var r=null;function o(e){var t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=O(e,t,(function(e){return function(t,r){var i=n(t,r);return i.cbIdx>=0&&"function"==typeof r[i.cbIdx]?Zone.current.scheduleMicroTask(i.name,r[i.cbIdx],i,o):e.apply(t,r)}}))}function I(e,t){e[v("OriginalDelegate")]=t}function A(e){return"function"==typeof e}function N(e){return"number"==typeof e}var x=!1;if("undefined"!=typeof window)try{var R=Object.defineProperty({},"passive",{get:function(){x=!0}});window.addEventListener("test",R,R),window.removeEventListener("test",R,R)}catch(e){x=!1}var M={useG:!0},L={},F={},H=new RegExp("^"+f+"(\\w+)(true|false)$"),G=v("propagationStopped");function U(e,t){var n=(t?t(e):e)+l,r=(t?t(e):e)+u,o=f+n,i=f+r;L[e]={},L[e][l]=o,L[e][u]=i}function q(e,t,n,r){var o=r&&r.add||s,a=r&&r.rm||c,h=r&&r.listeners||"eventListeners",p=r&&r.rmAll||"removeAllListeners",d=v(o),_="."+o+":",k="prependListener",g="."+k+":",y=function(e,t,n){if(!e.isRemoved){var r,o=e.callback;"object"==typeof o&&o.handleEvent&&(e.callback=function(e){return o.handleEvent(e)},e.originalDelegate=o);try{e.invoke(e,t,[n])}catch(e){r=e}var i=e.options;return i&&"object"==typeof i&&i.once&&t[a].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,i),r}};function m(n,r,o){if(r=r||e.event){var i=n||r.target||e,a=i[L[r.type][o?u:l]];if(a){var s=[];if(1===a.length)(h=y(a[0],i,r))&&s.push(h);else for(var c=a.slice(),f=0;f<c.length&&(!r||!0!==r[G]);f++){var h;(h=y(c[f],i,r))&&s.push(h)}if(1===s.length)throw s[0];var p=function(e){var n=s[e];t.nativeScheduleMicroTask((function(){throw n}))};for(f=0;f<s.length;f++)p(f)}}}var b=function(e){return m(this,e,!1)},w=function(e){return m(this,e,!0)};function E(t,n){if(!t)return!1;var r=!0;n&&void 0!==n.useG&&(r=n.useG);var s=n&&n.vh,c=!0;n&&void 0!==n.chkDup&&(c=n.chkDup);var y=!1;n&&void 0!==n.rt&&(y=n.rt);for(var m=t;m&&!m.hasOwnProperty(o);)m=i(m);if(!m&&t[o]&&(m=t),!m)return!1;if(m[d])return!1;var E,Z=n&&n.eventNameToString,S={},D=m[d]=m[o],P=m[v(a)]=m[a],j=m[v(h)]=m[h],O=m[v(p)]=m[p];n&&n.prepend&&(E=m[v(n.prepend)]=m[n.prepend]);var z=r?function(e){if(!S.isExisting)return D.call(S.target,S.eventName,S.capture?w:b,S.options)}:function(e){return D.call(S.target,S.eventName,e.invoke,S.options)},C=r?function(e){if(!e.isRemoved){var t=L[e.eventName],n=void 0;t&&(n=t[e.capture?u:l]);var r=n&&e.target[n];if(r)for(var o=0;o<r.length;o++)if(r[o]===e){r.splice(o,1),e.isRemoved=!0,e.removeAbortListener&&(e.removeAbortListener(),e.removeAbortListener=null),0===r.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return P.call(e.target,e.eventName,e.capture?w:b,e.options)}:function(e){return P.call(e.target,e.eventName,e.invoke,e.options)},A=n&&n.diff?n.diff:function(e,t){var n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},N=Zone[v("UNPATCHED_EVENTS")],R=e[v("PASSIVE_EVENTS")],G=function(t,o,i,a,f,h){return void 0===f&&(f=!1),void 0===h&&(h=!1),function(){var p=this||e,v=arguments[0];n&&n.transferEventName&&(v=n.transferEventName(v));var d=arguments[1];if(!d)return t.apply(this,arguments);if(T&&"uncaughtException"===v)return t.apply(this,arguments);var _=!1;if("function"!=typeof d){if(!d.handleEvent)return t.apply(this,arguments);_=!0}if(!s||s(t,d,p,arguments)){var k=x&&!!R&&-1!==R.indexOf(v),g=function n(e){if("object"==typeof e&&null!==e){var t=__assign({},e);return e.signal&&(t.signal=e.signal),t}return e}(function e(t,n){return!x&&"object"==typeof t&&t?!!t.capture:x&&n?"boolean"==typeof t?{capture:t,passive:!0}:t?"object"==typeof t&&!1!==t.passive?__assign(__assign({},t),{passive:!0}):t:{passive:!0}:t}(arguments[2],k)),y=null==g?void 0:g.signal;if(!(null==y?void 0:y.aborted)){if(N)for(var m=0;m<N.length;m++)if(v===N[m])return k?t.call(p,v,d,g):t.apply(this,arguments);var b=!!g&&("boolean"==typeof g||g.capture),w=!(!g||"object"!=typeof g)&&g.once,E=Zone.current,D=L[v];D||(U(v,Z),D=L[v]);var P,j=D[b?u:l],O=p[j],z=!1;if(O){if(z=!0,c)for(m=0;m<O.length;m++)if(A(O[m],d))return}else O=p[j]=[];var C=p.constructor.name,I=F[C];I&&(P=I[v]),P||(P=C+o+(Z?Z(v):v)),S.options=g,w&&(S.options.once=!1),S.target=p,S.capture=b,S.eventName=v,S.isExisting=z;var H=r?M:void 0;H&&(H.taskData=S),y&&(S.options.signal=void 0);var G=E.scheduleEventTask(P,d,H,i,a);if(y){S.options.signal=y;var q=function(){return G.zone.cancelTask(G)};t.call(y,"abort",q,{once:!0}),G.removeAbortListener=function(){return y.removeEventListener("abort",q)}}return S.target=null,H&&(H.taskData=null),w&&(S.options.once=!0),(x||"boolean"!=typeof G.options)&&(G.options=g),G.target=p,G.capture=b,G.eventName=v,_&&(G.originalDelegate=d),h?O.unshift(G):O.push(G),f?p:void 0}}}};return m[o]=G(D,_,z,C,y),E&&(m[k]=G(E,g,(function(e){return E.call(S.target,S.eventName,e.invoke,S.options)}),C,y,!0)),m[a]=function(){var t=this||e,r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));var o=arguments[2],i=!!o&&("boolean"==typeof o||o.capture),a=arguments[1];if(!a)return P.apply(this,arguments);if(!s||s(P,a,t,arguments)){var c,h=L[r];h&&(c=h[i?u:l]);var p=c&&t[c];if(p)for(var v=0;v<p.length;v++){var d=p[v];if(A(d,a))return p.splice(v,1),d.isRemoved=!0,0===p.length&&(d.allRemoved=!0,t[c]=null,i||"string"!=typeof r||(t[f+"ON_PROPERTY"+r]=null)),d.zone.cancelTask(d),y?t:void 0}return P.apply(this,arguments)}},m[h]=function(){var t=this||e,r=arguments[0];n&&n.transferEventName&&(r=n.transferEventName(r));for(var o=[],i=W(t,Z?Z(r):r),a=0;a<i.length;a++){var s=i[a];o.push(s.originalDelegate?s.originalDelegate:s.callback)}return o},m[p]=function(){var t=this||e,r=arguments[0];if(r){n&&n.transferEventName&&(r=n.transferEventName(r));var o=L[r];if(o){var i=t[o[l]],s=t[o[u]];if(i){var c=i.slice();for(v=0;v<c.length;v++)this[a].call(this,r,(f=c[v]).originalDelegate?f.originalDelegate:f.callback,f.options)}if(s)for(c=s.slice(),v=0;v<c.length;v++){var f;this[a].call(this,r,(f=c[v]).originalDelegate?f.originalDelegate:f.callback,f.options)}}}else{for(var h=Object.keys(t),v=0;v<h.length;v++){var d=H.exec(h[v]),_=d&&d[1];_&&"removeListener"!==_&&this[p].call(this,_)}this[p].call(this,"removeListener")}if(y)return this},I(m[o],D),I(m[a],P),O&&I(m[p],O),j&&I(m[h],j),!0}for(var Z=[],S=0;S<n.length;S++)Z[S]=E(n[S],r);return Z}function W(e,t){if(!t){var n=[];for(var r in e){var o=H.exec(r),i=o&&o[1];if(i&&(!t||i===t)){var a=e[r];if(a)for(var s=0;s<a.length;s++)n.push(a[s])}}return n}var c=L[t];c||(U(t),c=L[t]);var f=e[c[l]],h=e[c[u]];return f?h?f.concat(h):f.slice():h?h.slice():[]}function V(e,t){t.patchMethod(e,"queueMicrotask",(function(e){return function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}}))}var J=v("zoneTask");function Y(e,t,n,r){var o=null,i=null;n+=r;var a={};function s(t){var n=t.data;n.args[0]=function(){return t.invoke.apply(this,arguments)};var r=o.apply(e,n.args);return N(r)?n.handleId=r:(n.handle=r,n.isRefreshable=A(r.refresh)),t}function c(t){var n=t.data,r=n.handle;return i.call(e,null!=r?r:n.handleId)}o=O(e,t+=r,(function(n){return function(o,i){var u;if(A(i[0])){var l={isRefreshable:!1,isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?i[1]||0:void 0,args:i},f=i[0];i[0]=function e(){try{return f.apply(this,arguments)}finally{var t=l.handle,n=l.handleId;l.isPeriodic||l.isRefreshable||(n?delete a[n]:t&&(t[J]=null))}};var h=p(t,i[0],l,s,c);if(!h)return h;var v=h.data,d=v.handleId,_=v.handle,k=v.isRefreshable,g=v.isPeriodic;if(d)a[d]=h;else if(_&&(_[J]=h,k&&!g)){var y=_.refresh;_.refresh=function(){var e=h.zone,t=h.state;return"notScheduled"===t?(h._state="scheduled",e._updateTaskCount(h,1)):"running"===t&&(h._state="scheduling"),y.call(this)}}return null!==(u=null!=_?_:d)&&void 0!==u?u:h}return n.apply(e,i)}})),i=O(e,n,(function(t){return function(n,r){var o,i=r[0];N(i)?(o=a[i],delete a[i]):(o=null==i?void 0:i[J])?i[J]=null:o=i,(null==o?void 0:o.type)?o.cancelFn&&o.zone.cancelTask(o):t.apply(e,r)}}))}var B="set",$="clear";!function K(){var e=function r(){var e,r=globalThis,o=!0===r[t("forceDuplicateZoneCheck")];if(r.Zone&&(o||"function"!=typeof r.Zone.__symbol__))throw new Error("Zone already loaded.");return null!==(e=r.Zone)&&void 0!==e||(r.Zone=n()),r.Zone}();(function o(e){(function t(e){e.__load_patch("node_util",(function(e,t,n){n.patchOnProperties=P,n.patchMethod=O,n.bindArguments=y,n.patchMacroTask=z,function r(e){j=e}(!0)}))})(e),function n(e){e.__load_patch("EventEmitter",(function(e,t,n){var r,o="addListener",i="removeListener",a=function(e,t){return e.callback===t||e.callback.listener===t},s=function(e){return"string"==typeof e?e:e?e.toString().replace("(","_").replace(")","_"):""};try{r=require("events")}catch(e){}r&&r.EventEmitter&&function c(t){var r=q(e,n,[t],{useG:!1,add:o,rm:i,prepend:"prependListener",rmAll:"removeAllListeners",listeners:"listeners",chkDup:!1,rt:!0,diff:a,eventNameToString:s});r&&r[0]&&(t.on=t[o],t.off=t[i])}(r.EventEmitter.prototype)}))}(e),function r(e){e.__load_patch("fs",(function(e,t,n){var r,o;try{o=require("fs")}catch(e){}if(o){["access","appendFile","chmod","chown","close","exists","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","lutimes","link","lstat","mkdir","mkdtemp","open","opendir","read","readdir","readFile","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","write","writeFile","writev"].filter((function(e){return!!o[e]&&"function"==typeof o[e]})).forEach((function(e){z(o,e,(function(t,n){return{name:"fs."+e,args:n,cbIdx:n.length>0?n.length-1:-1,target:t}}))}));var i=null===(r=o.realpath)||void 0===r?void 0:r[n.symbol("OriginalDelegate")];(null==i?void 0:i.native)&&(o.realpath.native=i.native,z(o.realpath,"native",(function(e,t){return{args:t,target:e,cbIdx:t.length>0?t.length-1:-1,name:"fs.realpath.native"}})))}}))}(e),e.__load_patch("node_timers",(function(e,t){var n=!1;try{var r=require("timers");if(e.setTimeout!==r.setTimeout&&!w){var o=r.setTimeout;r.setTimeout=function(){return n=!0,o.apply(this,arguments)};var i=e.setTimeout((function(){}),100);clearTimeout(i),r.setTimeout=o}Y(r,B,$,"Timeout"),Y(r,B,$,"Interval"),Y(r,B,$,"Immediate")}catch(e){}w||(n?(e[t.__symbol__("setTimeout")]=e.setTimeout,e[t.__symbol__("setInterval")]=e.setInterval,e[t.__symbol__("setImmediate")]=e.setImmediate):(Y(e,B,$,"Timeout"),Y(e,B,$,"Interval"),Y(e,B,$,"Immediate")))})),e.__load_patch("nextTick",(function(){C(process,"nextTick",(function(e,t){return{name:"process.nextTick",args:t,cbIdx:t.length>0&&"function"==typeof t[0]?0:-1,target:process}}))})),e.__load_patch("handleUnhandledPromiseRejection",(function(e,t,n){function r(e){return function(t){W(process,e).forEach((function(n){"unhandledRejection"===e?n.invoke(t.rejection,t.promise):"rejectionHandled"===e&&n.invoke(t.promise)}))}}t[n.symbol("unhandledPromiseRejectionHandler")]=r("unhandledRejection"),t[n.symbol("rejectionHandledHandler")]=r("rejectionHandled")})),e.__load_patch("crypto",(function(){var e;try{e=require("crypto")}catch(e){}e&&["randomBytes","pbkdf2"].forEach((function(t){z(e,t,(function(n,r){return{name:"crypto."+t,args:r,cbIdx:r.length>0&&"function"==typeof r[r.length-1]?r.length-1:-1,target:e}}))}))})),e.__load_patch("console",(function(e,t){["dir","log","info","error","warn","assert","debug","timeEnd","trace"].forEach((function(e){var n=console[t.__symbol__(e)]=console[e];n&&(console[e]=function(){var e=a.call(arguments);return t.current===t.root?n.apply(this,e):t.root.run(n,this,e)})}))})),e.__load_patch("queueMicrotask",(function(e,t,n){V(e,n)}))})(e),function i(e){e.__load_patch("ZoneAwarePromise",(function(e,t,n){var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,i=n.symbol,a=[],s=!1!==e[i("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=i("Promise"),u=i("then"),l="__creationTrace__";n.onUnhandledError=function(e){if(n.showUncaughtError()){var t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=function(){for(var e=function(){var e=a.shift();try{e.zone.runGuarded((function(){if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){!function r(e){n.onUnhandledError(e);try{var r=t[f];"function"==typeof r&&r.call(this,e)}catch(e){}}(e)}};a.length;)e()};var f=i("unhandledPromiseRejectionHandler");function h(e){return e&&e.then}function p(e){return e}function v(e){return x.reject(e)}var d=i("state"),_=i("value"),k=i("finally"),g=i("parentPromiseValue"),y=i("parentPromiseState"),m="Promise.then",T=null,b=!0,w=!1,E=0;function Z(e,t){return function(n){try{j(e,t,n)}catch(t){j(e,!1,t)}}}var S=function(){var e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},D="Promise resolved with itself",P=i("currentTaskTrace");function j(e,r,i){var c=S();if(e===i)throw new TypeError(D);if(e[d]===T){var u=null;try{"object"!=typeof i&&"function"!=typeof i||(u=i&&i.then)}catch(t){return c((function(){j(e,!1,t)}))(),e}if(r!==w&&i instanceof x&&i.hasOwnProperty(d)&&i.hasOwnProperty(_)&&i[d]!==T)C(i),j(e,i[d],i[_]);else if(r!==w&&"function"==typeof u)try{u.call(i,c(Z(e,r)),c(Z(e,!1)))}catch(t){c((function(){j(e,!1,t)}))()}else{e[d]=r;var f=e[_];if(e[_]=i,e[k]===k&&r===b&&(e[d]=e[y],e[_]=e[g]),r===w&&i instanceof Error){var h=t.currentTask&&t.currentTask.data&&t.currentTask.data[l];h&&o(i,P,{configurable:!0,enumerable:!1,writable:!0,value:h})}for(var p=0;p<f.length;)I(e,f[p++],f[p++],f[p++],f[p++]);if(0==f.length&&r==w){e[d]=E;var v=i;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(i)+(i&&i.stack?"\n"+i.stack:""))}catch(e){v=e}s&&(v.throwOriginal=!0),v.rejection=i,v.promise=e,v.zone=t.current,v.task=t.currentTask,a.push(v),n.scheduleMicroTask()}}}return e}var z=i("rejectionHandledHandler");function C(e){if(e[d]===E){try{var n=t[z];n&&"function"==typeof n&&n.call(this,{rejection:e[_],promise:e})}catch(e){}e[d]=w;for(var r=0;r<a.length;r++)e===a[r].promise&&a.splice(r,1)}}function I(e,t,n,r,o){C(e);var i=e[d],a=i?"function"==typeof r?r:p:"function"==typeof o?o:v;t.scheduleMicroTask(m,(function(){try{var r=e[_],o=!!n&&k===n[k];o&&(n[g]=r,n[y]=i);var s=t.run(a,void 0,o&&a!==v&&a!==p?[]:[r]);j(n,!0,s)}catch(e){j(n,!1,e)}}),n)}var A=function(){},N=e.AggregateError,x=function(){function e(t){var n=this;if(!(n instanceof e))throw new Error("Must be an instanceof Promise.");n[d]=T,n[_]=[];try{var r=S();t&&t(r(Z(n,b)),r(Z(n,w)))}catch(e){j(n,!1,e)}}return e.toString=function(){return"function ZoneAwarePromise() { [native code] }"},e.resolve=function(t){return t instanceof e?t:j(new this(null),b,t)},e.reject=function(e){return j(new this(null),w,e)},e.withResolvers=function(){var t={};return t.promise=new e((function(e,n){t.resolve=e,t.reject=n})),t},e.any=function(t){if(!t||"function"!=typeof t[Symbol.iterator])return Promise.reject(new N([],"All promises were rejected"));var n=[],r=0;try{for(var o=0,i=t;o<i.length;o++)r++,n.push(e.resolve(i[o]))}catch(e){return Promise.reject(new N([],"All promises were rejected"))}if(0===r)return Promise.reject(new N([],"All promises were rejected"));var a=!1,s=[];return new e((function(e,t){for(var o=0;o<n.length;o++)n[o].then((function(t){a||(a=!0,e(t))}),(function(e){s.push(e),0==--r&&(a=!0,t(new N(s,"All promises were rejected")))}))}))},e.race=function(e){var t,n,r=new this((function(e,r){t=e,n=r}));function o(e){t(e)}function i(e){n(e)}for(var a=0,s=e;a<s.length;a++){var c=s[a];h(c)||(c=this.resolve(c)),c.then(o,i)}return r},e.all=function(t){return e.allWithCallback(t)},e.allSettled=function(t){return(this&&this.prototype instanceof e?this:e).allWithCallback(t,{thenCallback:function(e){return{status:"fulfilled",value:e}},errorCallback:function(e){return{status:"rejected",reason:e}}})},e.allWithCallback=function(e,t){for(var n,r,o=new this((function(e,t){n=e,r=t})),i=2,a=0,s=[],c=function(e){h(e)||(e=u.resolve(e));var o=a;try{e.then((function(e){s[o]=t?t.thenCallback(e):e,0==--i&&n(s)}),(function(e){t?(s[o]=t.errorCallback(e),0==--i&&n(s)):r(e)}))}catch(e){r(e)}i++,a++},u=this,l=0,f=e;l<f.length;l++)c(f[l]);return 0==(i-=2)&&n(s),o},Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"Promise"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Symbol.species,{get:function(){return e},enumerable:!1,configurable:!0}),e.prototype.then=function(n,r){var o,i=null===(o=this.constructor)||void 0===o?void 0:o[Symbol.species];i&&"function"==typeof i||(i=this.constructor||e);var a=new i(A),s=t.current;return this[d]==T?this[_].push(s,a,n,r):I(this,s,a,n,r),a},e.prototype.catch=function(e){return this.then(null,e)},e.prototype.finally=function(n){var r,o=null===(r=this.constructor)||void 0===r?void 0:r[Symbol.species];o&&"function"==typeof o||(o=e);var i=new o(A);i[k]=k;var a=t.current;return this[d]==T?this[_].push(a,i,n,n):I(this,a,i,n,n),i},e}();x.resolve=x.resolve,x.reject=x.reject,x.race=x.race,x.all=x.all;var R=e[c]=e.Promise;e.Promise=x;var M=i("thenPatched");function L(e){var t=e.prototype,n=r(t,"then");if(!n||!1!==n.writable&&n.configurable){var o=t.then;t[u]=o,e.prototype.then=function(e,t){var n=this;return new x((function(e,t){o.call(n,e,t)})).then(e,t)},e[M]=!0}}return n.patchThen=L,R&&(L(R),O(e,"fetch",(function(e){return function t(e){return function(t,n){var r=e.apply(t,n);if(r instanceof x)return r;var o=r.constructor;return o[M]||L(o),r}}(e)}))),Promise[t.__symbol__("uncaughtPromiseErrors")]=a,x}))}(e),function s(e){e.__load_patch("toString",(function(e){var t=Function.prototype.toString,n=v("OriginalDelegate"),r=v("Promise"),o=v("Error"),i=function i(){if("function"==typeof this){var a=this[n];if(a)return"function"==typeof a?t.call(a):Object.prototype.toString.call(a);if(this===Promise){var s=e[r];if(s)return t.call(s)}if(this===Error){var c=e[o];if(c)return t.call(c)}}return t.call(this)};i[n]=t,Function.prototype.toString=i;var a=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":a.call(this)}}))}(e)}()}));