/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["ff-Adlm-LR", [["𞤢", "𞤩"], ["𞤀𞤎", "𞤇𞤎"], u], [["𞤀𞤎", "𞤇𞤎"], u, u], [["𞤈", "𞤀𞥄", "𞤃", "𞤔", "𞤐", "𞤃", "𞤖"], ["𞤈𞤫𞤬", "𞤀𞥄𞤩𞤵", "𞤃𞤢𞤦", "𞤔𞤫𞤧", "𞤐𞤢𞥄𞤧", "𞤃𞤢𞤣", "𞤖𞤮𞤪"], ["𞤈𞤫𞤬𞤦𞤭𞤪𞥆𞤫", "𞤀𞥄𞤩𞤵𞤲𞥋𞤣𞤫", "𞤃𞤢𞤱𞤦𞤢𞥄𞤪𞤫", "𞤐𞤶𞤫𞤧𞤤𞤢𞥄𞤪𞤫", "𞤐𞤢𞥄𞤧𞤢𞥄𞤲𞤣𞤫", "𞤃𞤢𞤱𞤲𞤣𞤫", "𞤖𞤮𞤪𞤦𞤭𞤪𞥆𞤫"], ["𞤈𞤫𞤬", "𞤀𞥄𞤩𞤵", "𞤃𞤢𞤦", "𞤔𞤫𞤧", "𞤐𞤢𞥄𞤧", "𞤃𞤢𞤣", "𞤖𞤮𞤪"]], u, [["𞤅", "𞤕", "𞤄", "𞤅", "𞤁", "𞤑", "𞤃", "𞤔", "𞤅", "𞤒", "𞤔", "𞤄"], ["𞤅𞤭𞥅𞤤𞤮", "𞤕𞤮𞤤𞤼𞤮", "𞤐𞤦𞤮𞥅𞤴𞤮", "𞤅𞤫𞥅𞤼𞤮", "𞤁𞤵𞥅𞤶𞤮", "𞤑𞤮𞤪𞤧𞤮", "𞤃𞤮𞤪𞤧𞤮", "𞤔𞤵𞤳𞤮", "𞤅𞤭𞤤𞤼𞤮", "𞤒𞤢𞤪𞤳𞤮", "𞤔𞤮𞤤𞤮", "𞤄𞤮𞤱𞤼𞤮"], u], [["𞤅", "𞤕", "𞤄", "𞤅", "𞤁", "𞤑", "𞤃", "𞤔", "𞤅", "𞤒", "𞤔", "𞤄"], ["𞤅𞤭𞥅𞤤", "𞤕𞤮𞤤", "𞤐𞤦𞤮𞥅𞤴", "𞤅𞤫𞥅𞤼", "𞤁𞤵𞥅𞤶", "𞤑𞤮𞤪", "𞤃𞤮𞤪", "𞤔𞤵𞤳", "𞤅𞤭𞤤", "𞤒𞤢𞤪", "𞤔𞤮𞤤", "𞤄𞤮𞤱"], ["𞤅𞤭𞥅𞤤𞤮", "𞤕𞤮𞤤𞤼𞤮", "𞤐𞤦𞤮𞥅𞤴𞤮", "𞤅𞤫𞥅𞤼𞤮", "𞤁𞤵𞥅𞤶𞤮", "𞤑𞤮𞤪𞤧𞤮", "𞤃𞤮𞤪𞤧𞤮", "𞤔𞤵𞤳𞤮", "𞤅𞤭𞤤𞤼𞤮", "𞤒𞤢𞤪𞤳𞤮", "𞤔𞤮𞤤𞤮", "𞤄𞤮𞤱𞤼𞤮"]], [["𞤀𞤀𞤋", "𞤇𞤀𞤋"], u, ["𞤀𞤣𞤮 𞤀𞤲𞥆𞤢𞤦𞤭 𞤋𞥅𞤧𞤢𞥄", "𞤇𞤢𞥄𞤱𞤮 𞤀𞤲𞥆𞤢𞤦𞤭 𞤋𞥅𞤧𞤢𞥄"]], 1, [6, 0], ["d-M-y", "d MMM⹁ y", "d MMMM⹁ y", "EEEE d MMMM⹁ y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, "{1} 𞤉 {0}", u], [".", "⹁", ";", "%", "+", "-", "E", "×", "‰", "∞", "𞤏𞤮𞤈", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "LRD", "$", "𞤁𞤢𞤤𞤢 𞤂𞤭𞤦𞤫𞤪𞤭𞤴𞤢𞤲𞤳𞤮", { "BYN": [u, "р."], "GNF": [u, "𞤊𞤘"], "JPY": ["JP¥", "¥"], "LRD": ["$"], "NGN": ["𞤐𞤐𞤘", "₦"], "PGK": ["𞤑𞤆𞤘"], "PHP": ["𞤆𞤆𞤖", "₱"], "USD": ["US$", "$"], "XAF": ["𞤊𞤅𞤊𞤀"], "XOF": ["𞤅𞤊𞤀"] }, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,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