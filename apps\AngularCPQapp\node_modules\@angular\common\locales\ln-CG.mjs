/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === Math.floor(n) && (n >= 0 && n <= 1))
        return 1;
    return 5;
}
export default ["ln-CG", [["ntɔ́ngɔ́", "mpókwa"], u, u], u, [["e", "y", "m", "m", "m", "m", "p"], ["eye", "ybo", "mbl", "mst", "min", "mtn", "mps"], ["eyenga", "mokɔlɔ mwa yambo", "mokɔlɔ mwa míbalé", "mokɔlɔ mwa mísáto", "mokɔlɔ ya mínéi", "mokɔlɔ ya mítáno", "mpɔ́sɔ"], ["eye", "ybo", "mbl", "mst", "min", "mtn", "mps"]], u, [["y", "f", "m", "a", "m", "y", "y", "a", "s", "ɔ", "n", "d"], ["yan", "fbl", "msi", "apl", "mai", "yun", "yul", "agt", "stb", "ɔtb", "nvb", "dsb"], ["sánzá ya yambo", "sánzá ya míbalé", "sánzá ya mísáto", "sánzá ya mínei", "sánzá ya mítáno", "sánzá ya motóbá", "sánzá ya nsambo", "sánzá ya mwambe", "sánzá ya libwa", "sánzá ya zómi", "sánzá ya zómi na mɔ̌kɔ́", "sánzá ya zómi na míbalé"]], u, [["libóso ya", "nsima ya Y"], u, ["Yambo ya Yézu Krís", "Nsima ya Yézu Krís"]], 1, [6, 0], ["d/M/y", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "XAF", "FCFA", "Falánga CFA BEAC", { "CDF": ["FC"], "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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