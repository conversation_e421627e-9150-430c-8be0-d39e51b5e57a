/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["fa-AF", [["ق", "ب"], ["ق.ظ.", "ب.ظ."], ["قبل‌ازظهر", "بعدازظهر"]], u, [["ی", "د", "س", "چ", "پ", "ج", "ش"], ["یکشنبه", "دوشنبه", "سه‌شنبه", "چهارشنبه", "پنجشنبه", "جمعه", "شنبه"], u, ["۱ش", "۲ش", "۳ش", "۴ش", "۵ش", "ج", "ش"]], u, [["ج", "ف", "م", "ا", "م", "ج", "ج", "ا", "س", "ا", "ن", "د"], ["جنو", "فبروری", "مارچ", "اپریل", "می", "جون", "جول", "اگست", "سپتمبر", "اکتوبر", "نومبر", "دسم"], ["جنوری", "فبروری", "مارچ", "اپریل", "می", "جون", "جولای", "اگست", "سپتمبر", "اکتوبر", "نومبر", "دسمبر"]], [["ج", "ف", "م", "ا", "م", "ج", "ج", "ا", "س", "ا", "ن", "د"], ["جنوری", "فبروری", "مارچ", "اپریل", "می", "جون", "جولای", "اگست", "سپتمبر", "اکتوبر", "نومبر", "دسمبر"], u], [["ق", "م"], ["ق.م.", "م."], ["قبل از میلاد", "میلادی"]], 6, [4, 5], ["y/M/d", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["H:mm", "H:mm:ss", "H:mm:ss (z)", "H:mm:ss (zzzz)"], ["{1}،‏ {0}", u, "{1}، ساعت {0}", u], [".", ",", ";", "%", "‎+", "‎−", "E", "×", "‰", "∞", "ناعدد", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "AFN", "؋", "افغانی افغانستان", { "AFN": ["؋"], "BYN": [u, "р."], "CAD": ["$CA", "$"], "CNY": ["¥CN", "¥"], "HKD": ["$HK", "$"], "IRR": ["ریال"], "MXN": ["$MX", "$"], "NZD": ["$NZ", "$"], "PHP": [u, "₱"], "THB": ["฿"], "XCD": ["$EC", "$"], "XOF": ["فرانک CFA"] }, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,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