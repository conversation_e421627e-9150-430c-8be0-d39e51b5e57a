/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["az-Cyrl", [["а", "п"], ["АМ", "ПМ"], u], [["АМ", "ПМ"], u, u], [["7", "1", "2", "3", "4", "5", "6"], ["Б.", "Б.Е.", "Ч.А.", "Ч.", "Ҹ.А.", "Ҹ.", "Ш."], ["базар", "базар ертәси", "чәршәнбә ахшамы", "чәршәнбә", "ҹүмә ахшамы", "ҹүмә", "шәнбә"], ["Б.", "Б.Е.", "Ч.А.", "Ч.", "Ҹ.А.", "Ҹ.", "Ш."]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["јан", "фев", "мар", "апр", "май", "ијн", "ијл", "авг", "сен", "окт", "ној", "дек"], ["јанвар", "феврал", "март", "апрел", "май", "ијун", "ијул", "август", "сентјабр", "октјабр", "нојабр", "декабр"]], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["јан", "фев", "мар", "апр", "май", "ијн", "ијл", "авг", "сен", "окт", "ној", "дек"], ["Јанвар", "Феврал", "Март", "Апрел", "Май", "Ијун", "Ијул", "Август", "Сентјабр", "Октјабр", "Нојабр", "Декабр"]], [["е.ә.", "ј.е."], u, ["ерамыздан әввәл", "јени ера"]], 1, [6, 0], ["dd.MM.yy", "d MMM y", "d MMMM y", "d MMMM y, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "AZN", "₼", "AZN", { "AZN": ["₼"], "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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