// /**
//  * @fileoverview Composant principal pour la configuration CPQ
//  * @description Interface utilisateur pour sélectionner catégories, familles et produits
//  */

// import { Component, OnInit, OnDestroy } from '@angular/core';
// import { CommonModule } from '@angular/common';
// import { FormsModule } from '@angular/forms';
// import { HttpClient } from '@angular/common/http';
// import { TuiAccordionModule } from '@taiga-ui/kit';
// import { TuiExpandModule } from '@taiga-ui/core';

// import { Product } from '../../models/product/product';
// import { LUXURY_CARS_CATEGORY, CAR_FAMILIES, LUXURY_CARS_PRODUCTS } from '../../constants/cpq.constants';
// import { ProductTreeComponent } from '../product-tree/product-tree.component';

// import { ProductProperty, PropertySelection, PropertyUtils } from '../../models/property/property';
// import { PRODUCT_PROPERTIES, PRODUCT_PROPERTY_ASSOCIATIONS } from '../../constants/property.constants';


// // ===== INTERFACES =====
// interface ProductSelection {
//   product: Product;
//   quantity: number;
//   selectionSource: 'MAIN_LIST' | 'TREE_INCLUSION';
// }

// interface FinalConfiguration {
//   selectedCategory: any;
//   selectedFamilies: any[];
//   selectedProducts: any[];
//   selectedProperties: PropertySelection[];
//   priceBreakdown: {
//     mainProducts: number;
//     properties: number;
//     total: number;
//   };
//   totalPrice: number;
//   timestamp: string;
// }

// // ===== INTERFACES POUR FILTRES ET RECHERCHE =====
// interface ProductFilters {
//   searchTerm: string;
//   familyFilter: string;
//   typeFilter: 'all' | 'main' | 'option';
// }

// interface PaginationConfig {
//   currentPage: number;
//   itemsPerPage: number;
//   totalItems: number;
//   availablePageSizes: number[];
// }

// @Component({
//   selector: 'app-cpq-configurator',
//   standalone: true,
//   imports: [CommonModule, FormsModule, ProductTreeComponent],
//   templateUrl: './filecpq.html',
//   styleUrls: ['./filecpq.css']
// })
// export class CpqConfiguratorComponent implements OnInit, OnDestroy {

//   // ===== PROPRIÉTÉS PRINCIPALES =====
//   allProducts: Product[] = LUXURY_CARS_PRODUCTS;
//   availableProducts: Product[] = [];
//   filteredProducts: Product[] = [];
  
//   // Données fixes
//   readonly FIXED_CATEGORY = LUXURY_CARS_CATEGORY;
//   readonly CAR_FAMILIES = CAR_FAMILIES;
  
//   // État de sélection
//   selectedCategory = this.FIXED_CATEGORY;
//   selectedFamilyIds: string[] = [];
//   tempSelectedProducts: ProductSelection[] = [];
//   confirmedSelectedProducts: ProductSelection[] = [];
  
//   // Gestion des conflits
//   conflictProduct: Product | null = null;
//   selectedChildrenProducts: ProductSelection[] = [];
//   private modalRef: any;

//     // ===== NOUVELLES PROPRIÉTÉS POUR LES PROPRIÉTÉS =====
//   allProperties: ProductProperty[] = PRODUCT_PROPERTIES;
//   selectedProperties: PropertySelection[] = [];
//   expandedPropertySections = new Map<string, boolean>();
//   propertiesPrice: number = 0;

  
//   // État de l'interface
//   currentStep: number = 1;
//   maxSteps: number = 4;
  
//   // Messages
//   errorMessage: string = '';
//   successMessage: string = '';
  
//   // Hiérarchie et expansion
//   expandedProducts = new Map<string, boolean>();
//   totalPrice: number = 0;
  
//   // Configuration finale
//   finalConfiguration: FinalConfiguration | null = null;

//   // ===== NOUVELLES PROPRIÉTÉS POUR FILTRES ET RECHERCHE =====
//   productFilters: ProductFilters = {
//     searchTerm: '',
//     familyFilter: 'all',
//     typeFilter: 'all'
//   };

//   pagination: PaginationConfig = {
//     currentPage: 1,
//     itemsPerPage: 25,
//     totalItems: 0,
//     availablePageSizes: [25, 50, 75, 0] // 0 = tous les éléments
//   };

  

//   // ===== CONSTRUCTEUR =====
//   constructor(private http: HttpClient) {}

//   // ===== LIFECYCLE HOOKS =====
//   ngOnInit(): void {
//     console.log('🚗 Initialisation du configurateur Voitures de Luxe');
//   }

//   ngOnDestroy(): void {
//     this.clearMessages();
//   }

//   // ===== MÉTHODES UTILISÉES DANS LE TEMPLATE =====

//   /**
//    * Obtient le pourcentage de progression
//    */
//   getProgressPercentage(): number {
//     return Math.round((this.currentStep / this.maxSteps) * 100);
//   }

//   /**
//    * Bascule la sélection d'une famille
//    */
//   toggleFamilySelection(familyId: string): void {
//     const index = this.selectedFamilyIds.indexOf(familyId);
    
//     if (index > -1) {
//       this.selectedFamilyIds.splice(index, 1);
//     } else {
//       this.selectedFamilyIds.push(familyId);
//     }
    
//     this.loadAvailableProducts();
//   }

//   /**
//    * Vérifie si une famille est sélectionnée
//    */
//   isFamilySelected(familyId: string): boolean {
//     return this.selectedFamilyIds.includes(familyId);
//   }

//   /**
//    * Obtient le nom d'une famille par son ID
//    */
//   getFamilyName(familyId: string): string {
//     const family = this.CAR_FAMILIES.find(f => f.id === familyId);
//     return family ? family.name : familyId;
//   }
  
//   /**
//    * Obtient le nom d'un produit par son ID
//    */
//   getProductName(productId: string): string {
//     const product = this.allProducts.find(p => p.productid === productId);
//     return product ? product.name : productId;
//   }

//   /**
//    * Charge les produits disponibles selon les familles sélectionnées
//    */
//   loadAvailableProducts(): void {
//     if (this.selectedFamilyIds.length === 0) {
//       this.availableProducts = [];
//       this.applyFiltersAndPagination();
//       return;
//     }
    
//     this.availableProducts = this.allProducts.filter(product => 
//       this.selectedFamilyIds.includes(product.familyId || '')
//     );
    
//     this.applyFiltersAndPagination();
//   }

//   // ===== NOUVELLES MÉTHODES POUR FILTRES ET RECHERCHE =====

//   /**
//    * Applique les filtres et la pagination
//    */
//   applyFiltersAndPagination(): void {
//     let filtered = [...this.availableProducts];

//     // Filtre par terme de recherche
//     if (this.productFilters.searchTerm.trim()) {
//       const searchTerm = this.productFilters.searchTerm.toLowerCase().trim();
//       filtered = filtered.filter(product => 
//         product.name.toLowerCase().includes(searchTerm) ||
//         product.description?.toLowerCase().includes(searchTerm) ||
//         product.productid.toLowerCase().includes(searchTerm)
//       );
//     }

//     // Filtre par famille
//     if (this.productFilters.familyFilter !== 'all') {
//       filtered = filtered.filter(product => 
//         product.familyId === this.productFilters.familyFilter
//       );
//     }

//     // Filtre par type (principal ou option)
//     if (this.productFilters.typeFilter !== 'all') {
//       if (this.productFilters.typeFilter === 'main') {
//         filtered = filtered.filter(product => !product.parentproductid);
//       } else if (this.productFilters.typeFilter === 'option') {
//         filtered = filtered.filter(product => !!product.parentproductid);
//       }
//     }

//     // NOUVEAU: Masquer les produits de la généalogie des produits sélectionnés
//     filtered = this.hideSelectedProductsGenealogy(filtered);

//     // Mettre à jour le total
//     this.pagination.totalItems = filtered.length;

//     // Appliquer la pagination
//     if (this.pagination.itemsPerPage > 0) {
//       const startIndex = (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;
//       const endIndex = startIndex + this.pagination.itemsPerPage;
//       this.filteredProducts = filtered.slice(startIndex, endIndex);
//     } else {
//       this.filteredProducts = filtered;
//     }
//   }

//   /**
//    * Gère les changements de filtres (unifié)
//    */
//   onFilterChange(): void {
//     this.pagination.currentPage = 1;
//     this.applyFiltersAndPagination();
//   }

//   /**
//    * Gère le changement de page
//    */
//   onPageChange(page: number): void {
//     this.pagination.currentPage = page;
//     this.applyFiltersAndPagination();
//   }

//   /**
//    * Obtient le nombre total de pages
//    */
//   getTotalPages(): number {
//     if (this.pagination.itemsPerPage === 0) return 1;
//     return Math.ceil(this.pagination.totalItems / this.pagination.itemsPerPage);
//   }

//   /**
//    * Obtient les numéros de pages à afficher
//    */
//   getPageNumbers(): number[] {
//     const totalPages = this.getTotalPages();
//     const current = this.pagination.currentPage;
//     const pages: number[] = [];

//     if (totalPages <= 7) {
//       for (let i = 1; i <= totalPages; i++) {
//         pages.push(i);
//       }
//     } else {
//       if (current <= 4) {
//         for (let i = 1; i <= 5; i++) pages.push(i);
//         pages.push(-1); // Ellipsis
//         pages.push(totalPages);
//       } else if (current >= totalPages - 3) {
//         pages.push(1);
//         pages.push(-1); // Ellipsis
//         for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);
//       } else {
//         pages.push(1);
//         pages.push(-1); // Ellipsis
//         for (let i = current - 1; i <= current + 1; i++) pages.push(i);
//         pages.push(-1); // Ellipsis
//         pages.push(totalPages);
//       }
//     }

//     return pages;
//   }

//   /**
//    * Efface tous les filtres
//    */
//   clearAllFilters(): void {
//     this.productFilters = {
//       searchTerm: '',
//       familyFilter: 'all',
//       typeFilter: 'all'
//     };
//     this.pagination.currentPage = 1;
//     this.applyFiltersAndPagination();
//   }

//   /**
//    * Obtient les familles disponibles pour le filtre
//    */
//   getAvailableFamiliesForFilter(): any[] {
//     const familyIds = new Set(this.availableProducts.map(p => p.familyId).filter(id => id));
//     return this.CAR_FAMILIES.filter(f => familyIds.has(f.id));
//   }

//   // ===== NOUVELLES MÉTHODES POUR LA GÉNÉALOGIE =====

//   /**
//    * Obtient tous les descendants d'un produit (enfants, petits-enfants, etc.)
//    */
//   getAllDescendants(productId: string): string[] {
//     const descendants: string[] = [];
//     const directChildren = this.allProducts.filter(p => p.parentproductid === productId);
    
//     directChildren.forEach(child => {
//       descendants.push(child.productid);
//       // Récursion pour obtenir les descendants des enfants
//       const childDescendants = this.getAllDescendants(child.productid);
//       descendants.push(...childDescendants);
//     });
    
//     return descendants;
//   }

//   /**
//    * Masque les produits de la généalogie des produits sélectionnés
//    */
//   hideSelectedProductsGenealogy(products: Product[]): Product[] {
//     const allSelectedProducts = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];
//     const productsToHide = new Set<string>();
    
//     // Pour chaque produit sélectionné, obtenir sa généalogie complète
//     allSelectedProducts.forEach(selection => {
//       const descendants = this.getAllDescendants(selection.product.productid);
//       descendants.forEach(descendantId => productsToHide.add(descendantId));
//     });
    
//     // Filtrer les produits pour masquer ceux de la généalogie
//     return products.filter(product => !productsToHide.has(product.productid));
//   }

//   /**
//    * Obtient le nombre total de produits dans la généalogie d'un produit
//    */
//   getGenealogyCount(productId: string): number {
//     return this.getAllDescendants(productId).length;
//   }

//   /**
//    * Obtient les informations de généalogie pour l'accordéon
//    */
//   getGenealogyInfo(productId: string): { count: number; products: Product[] } {
//     const descendantIds = this.getAllDescendants(productId);
//     const products = descendantIds.map(id => 
//       this.allProducts.find(p => p.productid === id)
//     ).filter(p => p !== undefined) as Product[];
    
//     return {
//       count: descendantIds.length,
//       products: products
//     };
//   }

//   /**
//    * Calcule le prix total des produits de la généalogie
//    */
//   getGenealogyTotalPrice(productId: string): number {
//     const genealogyInfo = this.getGenealogyInfo(productId);
//     return genealogyInfo.products.reduce((sum, p) => sum + (p.price || 0), 0);
//   }

//   // ===== MÉTHODES DE SÉLECTION (MISES À JOUR) =====

//   /**
//    * Obtient les enfants directs d'un produit spécifique
//    */
//   getDirectChildren(productId: string): Product[] {
//     return this.availableProducts.filter(p => p.parentproductid === productId);
//   }

//   /**
//    * Vérifie si un produit a des enfants directs
//    */
//   hasDirectChildren(productId: string): boolean {
//     return this.getDirectChildren(productId).length > 0;
//   }

//   /**
//    * Vérifie si un produit est sélectionné temporairement
//    */
//   isTempProductSelected(product: any): boolean {
//     return this.tempSelectedProducts.some(
//       selection => selection.product.productid === product.productid
//     );
//   }

//   /**
//    * Vérifie si un produit est confirmé
//    */
//   isProductConfirmed(product: any): boolean {
//     return this.confirmedSelectedProducts.some(
//       selection => selection.product.productid === product.productid
//     );
//   }
  
//   /**
//    * Vérifie si un produit est confirmé comme produit principal
//    */
//   isProductConfirmedAsMain(product: any): boolean {
//     return this.confirmedSelectedProducts.some(
//       selection => selection.product.productid === product.productid && 
//                   selection.selectionSource === 'MAIN_LIST'
//     );
//   }

//   /**
//    * Bascule la sélection temporaire d'un produit
//    */
//   toggleTempProductSelection(product: any): void {
//     const existingIndex = this.tempSelectedProducts.findIndex(
//       selection => selection.product.productid === product.productid
//     );

//     if (existingIndex >= 0) {
//       this.tempSelectedProducts.splice(existingIndex, 1);
//     } else {
//       // Vérifier si le produit a des enfants sélectionnés
//       if (this.hasChildrenSelected(product)) {
//         this.showParentChildConflictModal(product);
//         return;
//       }
      
//       const newSelection: ProductSelection = {
//         product: product,
//         quantity: 1,
//         selectionSource: 'MAIN_LIST'
//       };
//       this.tempSelectedProducts.push(newSelection);
//     }
    
//     this.applyFiltersAndPagination();
//   }



//   /**
//    * Confirme la sélection temporaire
//    */
//   confirmTempSelection(): void {
//     // Vérifier les conflits avant confirmation
//     for (const tempSelection of this.tempSelectedProducts) {
//       if (this.hasChildrenInConfirmed(tempSelection.product)) {
//         this.conflictProduct = tempSelection.product;
        
//         // Utiliser Bootstrap 5 pour afficher le modal
//         const modalElement = document.getElementById('parentChildConflictModal');
//         if (modalElement) {
//           // @ts-ignore: Ignorer l'erreur TS car nous savons que bootstrap est disponible
//           const modal = new bootstrap.Modal(modalElement);
//           modal.show();
//           this.modalRef = modal;
//           return; // Attendre la confirmation via le modal
//         } else {
//           // Fallback si le modal n'est pas trouvé
//           const confirmed = confirm(
//             `Le produit "${tempSelection.product.name}" que vous confirmez a des enfants déjà confirmés.\n\n` +
//             `Ces enfants seront automatiquement supprimés. Continuer ?`
//           );
          
//           if (confirmed) {
//             this.removeChildrenFromConfirmed(tempSelection.product);
//           } else {
//             return;
//           }
//         }
//       }
//     }
    
//     this.confirmedSelectedProducts = this.tempSelectedProducts.map(temp => ({
//       ...temp,
//       selectionSource: temp.selectionSource || 'MAIN_LIST'
//     }));
//     this.tempSelectedProducts = [];
//     this.updateTotalPrice();
//     this.applyFiltersAndPagination();
//   }

//   /**
//    * Vérifie si un produit a des enfants dans les confirmés
//    */
//   hasChildrenInConfirmed(product: any): boolean {
//     const descendants = this.getAllDescendants(product.productid);
//     return descendants.some(descendantId => 
//       this.confirmedSelectedProducts.some(sel => sel.product.productid === descendantId)
//     );
//   }

//   /**
//    * Supprime les enfants d'un produit des confirmés
//    */
//   removeChildrenFromConfirmed(product: any): void {
//     const descendants = this.getAllDescendants(product.productid);
//     this.confirmedSelectedProducts = this.confirmedSelectedProducts.filter(
//       sel => !descendants.includes(sel.product.productid)
//     );
//   }
  
//   /**
//    * Ferme le modal de conflit
//    */
//   closeConflictModal(): void {
//     if (this.modalRef) {
//       this.modalRef.hide();
//       this.modalRef = null;
//     }
//     this.conflictProduct = null;
//   }

//   /**
//    * Retourne à la sélection des produits
//    */
//   backToSelection(): void {
//     this.tempSelectedProducts = [...this.confirmedSelectedProducts];
//     this.confirmedSelectedProducts = [];
//     this.currentStep = 2;
//   }




//   /**
//    * Vérifie si la configuration est valide pour générer un devis
//    */
//   isConfigurationValid(): boolean {
//     return this.confirmedSelectedProducts.length > 0 && 
//            this.selectedFamilyIds.length > 0 && 
//            this.totalPrice > 0;
//   }






//   /**
//    * Supprime un produit confirmé
//    */
//   removeConfirmedProduct(product: any): void {
//     // Supprimer le produit principal
//     const mainIndex = this.confirmedSelectedProducts.findIndex(
//       selection => selection.product.productid === product.productid && 
//       selection.selectionSource === 'MAIN_LIST'
//     );
    
//     if (mainIndex >= 0) {
//       this.confirmedSelectedProducts.splice(mainIndex, 1);
//     }

//     // Supprimer aussi tous ses enfants inclus
//     this.confirmedSelectedProducts = this.confirmedSelectedProducts.filter(
//       selection => {
//         if (selection.selectionSource === 'TREE_INCLUSION') {
//           // Vérifier si c'est un enfant du produit supprimé
//           return !this.isChildOf(selection.product, product.productid);
//         }
//         return true;
//       }
//     );

//     this.updateTotalPrice();
//   }

//   /**
//    * Vérifie si un produit est enfant d'un autre
//    */
//   private isChildOf(product: Product, parentId: string): boolean {
//     if (product.parentproductid === parentId) {
//       return true;
//     }
    
//     // Vérification récursive
//     const parent = this.allProducts.find(p => p.productid === product.parentproductid);
//     if (parent) {
//       return this.isChildOf(parent, parentId);
//     }
    
//     return false;
//   }

//   /**
//    * Bascule l'inclusion d'un produit enfant via checkbox
//    */
//   toggleProductInclusion(product: any): void {
//     const existingIndex = this.confirmedSelectedProducts.findIndex(
//       selection => selection.product.productid === product.productid
//     );

//     if (existingIndex >= 0) {
//       // Retirer le produit de la sélection (seulement si pas ajouté depuis la liste principale)
//       if (this.confirmedSelectedProducts[existingIndex].selectionSource !== 'MAIN_LIST') {
//         this.confirmedSelectedProducts.splice(existingIndex, 1);
//       }
//     } else {
//       // Vérifier si le produit est déjà dans la liste principale
//       const isMainProduct = this.confirmedSelectedProducts.some(
//         selection => selection.product.productid === product.productid && 
//                     selection.selectionSource === 'MAIN_LIST'
//       );
      
//       // Ne pas ajouter si déjà présent comme produit principal
//       if (!isMainProduct) {
//         // Ajouter le produit comme "inclus" (pas depuis la liste principale)
//         const newSelection: ProductSelection = {
//           product: product,
//           quantity: 1,
//           selectionSource: 'TREE_INCLUSION'
//         };
//         this.confirmedSelectedProducts.push(newSelection);
//       }
//     }

//     this.updateTotalPrice();
//   }

//   /**
//    * Vérifie si un produit est inclus (coché) mais pas ajouté depuis la liste principale
//    */
//   isProductIncluded(product: any): boolean {
//     const selection = this.confirmedSelectedProducts.find(
//       s => s.product.productid === product.productid
//     );
//     return selection ? selection.selectionSource === 'TREE_INCLUSION' : false;
//   }

//   /**
//    * Obtient la quantité d'un produit sélectionné
//    */
//   getProductQuantity(productId: string): number {
//     const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);
//     return selection ? selection.quantity : 1;
//   }

//   /**
//    * Met à jour la quantité d'un produit
//    */
//   updateProductQuantity(productId: string, event: any): void {
//     let newQuantity = parseInt(event.target.value);
    
//     if (isNaN(newQuantity) || newQuantity < 1) newQuantity = 1;
//     if (newQuantity > 99) newQuantity = 99;
    
//     const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);
//     if (selection) {
//       selection.quantity = newQuantity;
//       this.updateTotalPrice();
//     }
//   }

//   /**
//    * Augmente la quantité d'un produit
//    */
//   increaseQuantity(productId: string): void {
//     const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);
//     if (selection && selection.quantity < 99) {
//       selection.quantity++;
//       this.updateTotalPrice();
//     }
//   }

//   /**
//    * Diminue la quantité d'un produit
//    */
//   decreaseQuantity(productId: string): void {
//     const selection = this.confirmedSelectedProducts.find(p => p.product.productid === productId);
//     if (selection && selection.quantity > 1) {
//       selection.quantity--;
//       this.updateTotalPrice();
//     }
//   }

//   /**
//    * Bascule l'affichage des enfants d'un produit
//    */
//   toggleProductExpansion(productId: string): void {
//     const isExpanded = this.expandedProducts.get(productId) || false;
//     this.expandedProducts.set(productId, !isExpanded);
//   }

//   /**
//    * Vérifie si un produit est étendu (enfants visibles)
//    */
//   isProductExpanded(productId: string): boolean {
//     return this.expandedProducts.get(productId) || false;
//   }

//   /**
//    * Met à jour le prix total
//    */
//   updateTotalPrice(): void {
//     const productsPrice = this.confirmedSelectedProducts.reduce((total, selection) => {
//       return total + ((selection.product.price || 0) * selection.quantity);
//     }, 0);
    
//     this.propertiesPrice = PropertyUtils.calculatePropertiesTotal(
//       this.selectedProperties,
//       this.allProperties
//     );
    
//     this.totalPrice = productsPrice + this.propertiesPrice;
//   }


//   /**
//    * Formate un prix en devise
//    */
//   formatPrice(price: number): string {
//     return new Intl.NumberFormat('fr-FR', {
//       style: 'currency',
//       currency: 'EUR'
//     }).format(price);
//   }




//   /**
//    * Met à jour l'interface après changement d'étape
//    */
//   private updateStepInterface(): void {
//     this.clearMessages();
//     if (this.currentStep === 2) {
//       this.loadAvailableProducts();
//       this.pagination.currentPage = 1;
//     }
//   }

//   /**
//    * Mise à jour de la méthode nextStep pour vérifier les propriétés requises
//    */
//   nextStep(): void {
//     switch (this.currentStep) {
//       case 1:
//         if (this.selectedFamilyIds.length === 0) {
//           this.setErrorMessage('Veuillez sélectionner au moins une famille de véhicules');
//           return;
//         }
//         this.currentStep = 2;
//         this.loadAvailableProducts();
//         break;
        
//       case 2:
//         if (this.confirmedSelectedProducts.length === 0) {
//           this.setErrorMessage('Veuillez sélectionner au moins un produit');
//           return;
//         }
//         this.currentStep = 3;
//         this.initializePropertiesSelection();
//         break;
        
//       case 3:
//         if (!this.validatePropertiesSelection()) {
//           const missingProperties = this.getMissingRequiredProperties();
//           const missingNames = missingProperties.map(prop => prop.name).join(', ');
//           this.setErrorMessage(`Veuillez sélectionner les propriétés requises suivantes : ${missingNames}`);
//           return;
//         }
//         this.currentStep = 4;
//         this.generateFinalConfiguration();
//         break;
//     }
    
//     this.clearMessages();
//   }


//   /**
//    * Revient à l'étape précédente
//    */
//   previousStep(): void {
//     if (this.currentStep > 1) {
//       this.currentStep--;
      
//       // Logique spéciale pour revenir de l'étape 3 à l'étape 2
//       if (this.currentStep === 2) {
//         this.backToSelection();
//         return;
//       }
      
//       this.updateStepInterface();
//     }
//   }

//   /**
//    * Redémarre le processus de configuration
//    */
//   restart(): void {
//     this.currentStep = 1;
//     this.selectedFamilyIds = [];
//     this.tempSelectedProducts = [];
//     this.confirmedSelectedProducts = [];
//     this.selectedProperties = [];
//     this.availableProducts = [];
//     this.filteredProducts = [];
//     this.expandedProducts.clear();
//     this.expandedPropertySections.clear();
//     this.totalPrice = 0;
//     this.propertiesPrice = 0;
//     this.finalConfiguration = null;
//     this.clearAllFilters();
//     this.clearMessages();
//   }


//   /**
//    * Génère la configuration finale
//    */
//   private generateFinalConfiguration(): void {
//     const mainProductsPrice = this.confirmedSelectedProducts
//       .filter(s => s.selectionSource === 'MAIN_LIST')
//       .reduce((sum, s) => sum + ((s.product.price || 0) * s.quantity), 0);

//     this.finalConfiguration = {
//       selectedCategory: this.selectedCategory,
//       selectedFamilies: this.selectedFamilyIds.map(id => 
//         this.CAR_FAMILIES.find(f => f.id === id)
//       ).filter(f => f !== undefined),
//       selectedProducts: this.confirmedSelectedProducts.map(s => ({
//         product: s.product,
//         quantity: s.quantity,
//         subtotal: (s.product.price || 0) * s.quantity,
//         selectionSource: s.selectionSource,
//         level: this.getProductLevel(s.product.productid),
//         hierarchyPath: this.getProductHierarchyPath(s.product.productid)
//       })),
//       selectedProperties: this.selectedProperties.filter(ps => ps.selected),
//       priceBreakdown: {
//         mainProducts: mainProductsPrice,
//         properties: this.propertiesPrice,
//         total: this.totalPrice
//       },
//       totalPrice: this.totalPrice,
//       timestamp: new Date().toISOString()
//     };
//   }

//   /**
//    * Prépare les données pour le composant product-tree récursif
//    */
//   getProductTreeData(parentProductId: string): any[] {
//     const children = this.getDirectChildren(parentProductId);
//     return children.map(child => {
//       // Vérifier si l'enfant est déjà sélectionné comme produit principal
//       const isMainProduct = this.confirmedSelectedProducts.some(
//         selection => selection.product.productid === child.productid && 
//                     selection.selectionSource === 'MAIN_LIST'
//       );
      
//       // Si c'est un produit principal, ne pas l'inclure dans l'arborescence
//       if (isMainProduct) {
//         return null;
//       }
      
//       return {
//         product: child,
//         isSelected: this.isProductConfirmed(child),
//         isExpanded: this.isProductExpanded(child.productid),
//         quantity: this.getProductQuantity(child.productid),
//         directChildren: this.getProductTreeData(child.productid),
//         hasChildren: this.hasDirectChildren(child.productid)
//       };
//     }).filter(item => item !== null); // Filtrer les éléments null
//   }

//   // ===== GESTION DES CONFLITS PARENT-ENFANT =====

//   /**
//    * Vérifie si un produit a des parents sélectionnés
//    */
//   hasParentProductSelected(product: any): boolean {
//     if (!product || !product.productid) return false;
//     const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];
//     return this.getProductAncestors(product.productid).some(ancestorId => 
//       allSelected.some(sel => sel.product.productid === ancestorId)
//     );
//   }

//   /**
//    * Vérifie si un produit a des enfants sélectionnés
//    */
//   hasChildrenSelected(product: any): boolean {
//     if (!product || !product.productid) return false;
//     const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];
//     const descendants = this.getAllDescendants(product.productid);
//     return descendants.some(descendantId => 
//       allSelected.some(sel => sel.product.productid === descendantId)
//     );
//   }

//   /**
//    * Obtient tous les ancêtres d'un produit
//    */
//   getProductAncestors(productId: string): string[] {
//     const ancestors: string[] = [];
//     let currentProduct = this.allProducts.find(p => p.productid === productId);
    
//     while (currentProduct?.parentproductid) {
//       ancestors.push(currentProduct.parentproductid);
//       currentProduct = this.allProducts.find(p => p.productid === currentProduct!.parentproductid);
//     }
    
//     return ancestors;
//   }

//   /**
//    * Affiche le modal de conflit parent-enfant
//    */
//   showParentChildConflictModal(product: any): void {
//     console.log('Conflit détecté pour le produit:', product.name);
//     this.conflictProduct = product;
    
//     // Obtenir la liste des produits enfants sélectionnés pour l'affichage dans le modal
//     const descendants = this.getAllDescendants(product.productid);
//     const allSelected = [...this.tempSelectedProducts, ...this.confirmedSelectedProducts];
//     this.selectedChildrenProducts = allSelected
//       .filter(sel => descendants.includes(sel.product.productid));
    
//     // Utiliser le modal Bootstrap
//     const modalElement = document.getElementById('parentChildConflictModal');
//     if (modalElement) {
//       // @ts-ignore: Ignorer l'erreur TS car nous savons que bootstrap est disponible
//       const modal = new bootstrap.Modal(modalElement);
//       modal.show();
//       this.modalRef = modal;
//     } else {
//       // Fallback si le modal n'est pas disponible
//       let message = `Attention ! Vous tentez de sélectionner "${product.name}" qui a des produits enfants déjà sélectionnés.\n\n`;
      
//       const selectedChildrenNames = this.selectedChildrenProducts.map(sel => sel.product.name);
//       if (selectedChildrenNames.length > 0) {
//         message += `Produits enfants qui seront désélectionnés :\n`;
//         selectedChildrenNames.forEach(name => {
//           message += `- ${name}\n`;
//         });
//         message += `\n`;
//       }
      
//       message += `En confirmant, tous ces produits seront désélectionnés et masqués.\n\nVoulez-vous continuer ?`;
      
//       const confirmed = confirm(message);
//       if (confirmed) {
//         this.resolveParentChildConflict(product);
//       }
//       this.conflictProduct = null;
//     }
//   }
  
//   /**
//    * Confirme le choix dans le modal de conflit
//    */
//   confirmParentChildConflict(): void {
//     if (this.conflictProduct) {
//       this.resolveParentChildConflict(this.conflictProduct);
//       this.conflictProduct = null;
//     }
//   }

//   /**
//    * Résout le conflit en sélectionnant le parent et désélectionnant les enfants
//    */
//   resolveParentChildConflict(product: any): void {
//     const descendants = this.getAllDescendants(product.productid);
    
//     // Supprimer tous les descendants des sélections
//     this.tempSelectedProducts = this.tempSelectedProducts.filter(
//       sel => !descendants.includes(sel.product.productid)
//     );
//     this.confirmedSelectedProducts = this.confirmedSelectedProducts.filter(
//       sel => !descendants.includes(sel.product.productid)
//     );
    
//     // Ajouter le produit parent
//     const newSelection: ProductSelection = {
//       product: product,
//       quantity: 1,
//       selectionSource: 'MAIN_LIST'
//     };
//     this.tempSelectedProducts.push(newSelection);
    
//     this.applyFiltersAndPagination();
//     this.updateTotalPrice();
//   }

//   // ===== MÉTHODES UTILITAIRES PRIVÉES =====

  
//   /**
//    * Obtient le niveau hiérarchique d'un produit (méthode existante améliorée)
//    */
//   getProductLevel(productId: string): number {
//     const product = this.allProducts.find(p => p.productid === productId);
//     if (!product) return 0;
    
//     let level = 0;
//     let currentProduct = product;
    
//     while (currentProduct.parentproductid) {
//       level++;
//       const parent = this.allProducts.find(p => p.productid === currentProduct.parentproductid);
//       if (!parent) break;
//       currentProduct = parent;
//     }
    
//     return level;
//   }

//   /**
//    * Obtient le chemin hiérarchique d'un produit
//    */
//   private getProductHierarchyPath(productId: string): Product[] {
//     const path: Product[] = [];
//     const product = this.allProducts.find(p => p.productid === productId);
    
//     if (!product) return path;
    
//     let currentProduct = product;
//     path.unshift(currentProduct);
    
//     while (currentProduct.parentproductid) {
//       const parent = this.allProducts.find(p => p.productid === currentProduct.parentproductid);
//       if (!parent) break;
//       path.unshift(parent);
//       currentProduct = parent;
//     }
    
//     return path;
//   }

//   /**
//    * Définit un message d'erreur
//    */
//   private setErrorMessage(message: string): void {
//     this.errorMessage = message;
//     this.successMessage = '';
//     setTimeout(() => this.clearMessages(), 5000);
//   }

//   /**
//    * Définit un message de succès
//    */
//   private setSuccessMessage(message: string): void {
//     this.successMessage = message;
//     this.errorMessage = '';
//     setTimeout(() => this.clearMessages(), 3000);
//   }

//   /**
//    * Efface tous les messages
//    */
//   private clearMessages(): void {
//     this.errorMessage = '';
//     this.successMessage = '';
//   }



//     // ===== MÉTHODES POUR LE RÉCAPITULATIF =====

//   /**
//    * Obtient les produits principaux confirmés
//    */
//   getMainProducts(): ProductSelection[] {
//     return this.confirmedSelectedProducts.filter(s => s.selectionSource === 'MAIN_LIST');
//   }

//   /**
//    * Obtient les options d'un produit principal
//    */
//   getProductOptions(mainProductId: string): ProductSelection[] {
//     return this.confirmedSelectedProducts.filter(s => 
//       s.selectionSource === 'TREE_INCLUSION' && 
//       this.isChildOf(s.product, mainProductId)
//     );
//   }

//   /**
//    * Calcule le total des produits principaux
//    */
//   getMainProductsTotal(): number {
//     return this.getMainProducts().reduce((total, selection) => {
//       return total + ((selection.product.price || 0) * selection.quantity);
//     }, 0);
//   }

//   /**
//    * Calcule le total des options
//    */
//   getOptionsTotal(): number {
//     return this.confirmedSelectedProducts
//       .filter(s => s.selectionSource === 'TREE_INCLUSION')
//       .reduce((total, selection) => {
//         return total + ((selection.product.price || 0) * selection.quantity);
//       }, 0);
//   }

//   /**
//    * Calcule la quantité totale
//    */
//   getQuantityTotal(): number {
//     return this.confirmedSelectedProducts.reduce((total, selection) => {
//       return total + selection.quantity;
//     }, 0);
//   }








//   /**
//  * Obtient les produits triés par hiérarchie pour le récapitulatif
//  */
// getSortedProductsForRecap(): ProductSelection[] {
//   const sortedProducts: ProductSelection[] = [];
//   const processedIds = new Set<string>();
  
//   // Fonction récursive pour ajouter un produit et ses enfants
//   const addProductAndChildren = (productId: string) => {
//     if (processedIds.has(productId)) return;
    
//     // Trouver le produit dans les sélections confirmées
//     const productSelection = this.confirmedSelectedProducts.find(
//       selection => selection.product.productid === productId
//     );
    
//     if (productSelection) {
//       sortedProducts.push(productSelection);
//       processedIds.add(productId);
//     }
    
//     // Trouver et ajouter tous les enfants directs de ce produit
//     const children = this.confirmedSelectedProducts
//       .filter(selection => selection.product.parentproductid === productId)
//       .sort((a, b) => a.product.name.localeCompare(b.product.name)); // Trier les enfants par nom
    
//     children.forEach(child => {
//       addProductAndChildren(child.product.productid);
//     });
//   };
  
//   // Commencer par tous les produits racines (sans parent)
//   const rootProducts = this.confirmedSelectedProducts
//     .filter(selection => !selection.product.parentproductid)
//     .sort((a, b) => a.product.name.localeCompare(b.product.name));
  
//   rootProducts.forEach(root => {
//     addProductAndChildren(root.product.productid);
//   });
  
//   // Ajouter les produits orphelins (au cas où il y aurait des références parent manquantes)
//   this.confirmedSelectedProducts.forEach(selection => {
//     if (!processedIds.has(selection.product.productid)) {
//       sortedProducts.push(selection);
//     }
//   });
  
//   return sortedProducts;
// }







//   // ===== NOUVELLES MÉTHODES POUR LES PROPRIÉTÉS =====

//   /**
//    * Initialise la sélection des propriétés
//    */
//   initializePropertiesSelection(): void {
//     this.selectedProperties = [];
    
//     // Pour chaque produit confirmé racine, initialiser ses propriétés
//     const rootProducts = this.confirmedSelectedProducts.filter(s => 
//       s.selectionSource === 'MAIN_LIST' && !s.product.parentproductid
//     );
    
//     rootProducts.forEach(selection => {
//       const productProperties = this.getAvailablePropertiesForProduct(selection.product.productid);
      
//       productProperties.forEach(property => {
//         const propertySelection: PropertySelection = {
//           productid: selection.product.productid,
//           propertyid: property.propertyid,
//           selected: this.isDefaultProperty(selection.product.productid, property.propertyid),
//           selectiondate: new Date(),
//           product: selection.product,
//           property: property
//         };
//         this.selectedProperties.push(propertySelection);
//       });
//     });
    
//     this.updateTotalPrice();
//   }

//   /**
//    * Obtient les propriétés disponibles pour un produit
//    */
//   getAvailablePropertiesForProduct(productId: string): ProductProperty[] {
//     const associations = PRODUCT_PROPERTY_ASSOCIATIONS.filter(a => a.productid === productId);
//     return associations.map(association => 
//       this.allProperties.find(p => p.propertyid === association.propertyid)
//     ).filter(p => p !== undefined) as ProductProperty[];
//   }

//   /**
//    * Vérifie si une propriété est par défaut pour un produit
//    */
//   isDefaultProperty(productId: string, propertyId: string): boolean {
//     const association = PRODUCT_PROPERTY_ASSOCIATIONS.find(
//       a => a.productid === productId && a.propertyid === propertyId
//     );
//     return association?.isdefault || false;
//   }

//   /**
//    * Obtient les produits confirmés racines uniquement
//    */
//   getSelectedRootProducts(): ProductSelection[] {
//     return this.confirmedSelectedProducts.filter(selection => 
//       selection.selectionSource === 'MAIN_LIST' && !selection.product.parentproductid
//     );
//   }

//   /**
//    * Obtient les propriétés pour un produit spécifique
//    */
//   getPropertiesForProduct(productId: string): PropertySelection[] {
//     return this.selectedProperties.filter(ps => ps.productid === productId);
//   }

//   /**
//    * Groupe les propriétés par catégorie pour un produit
//    */
//   getGroupedPropertiesForProduct(productId: string): {
//     free: PropertySelection[];
//     paid: PropertySelection[];
//   } {
//     const properties = this.getPropertiesForProduct(productId);
//     return {
//       free: properties.filter(ps => PropertyUtils.isFreeProperty(ps.property!)),
//       paid: properties.filter(ps => PropertyUtils.isPaidProperty(ps.property!))
//     };
//   }

//   /**
//    * Bascule la sélection d'une propriété
//    */
//   togglePropertySelection(productId: string, propertyId: string): void {
//     const propertySelection = this.selectedProperties.find(
//       ps => ps.productid === productId && ps.propertyid === propertyId
//     );
    
//     if (propertySelection) {
//       const property = propertySelection.property!;
      
//       // Si la propriété est exclusive, désélectionner les autres du même type
//       if (property.isexclusive) {
//         this.selectedProperties
//           .filter(ps => 
//             ps.productid === productId && 
//             ps.property!.propertytype === property.propertytype &&
//             ps.propertyid !== propertyId
//           )
//           .forEach(ps => ps.selected = false);
//       }
      
//       propertySelection.selected = !propertySelection.selected;
//       this.updateTotalPrice();
//     }
//   }

//   /**
//    * Vérifie si une propriété est sélectionnée
//    */
//   isPropertySelected(productId: string, propertyId: string): boolean {
//     const propertySelection = this.selectedProperties.find(
//       ps => ps.productid === productId && ps.propertyid === propertyId
//     );
//     return propertySelection?.selected || false;
//   }

//   /**
//    * Bascule l'expansion d'une section de propriétés
//    */
//   togglePropertySectionExpansion(sectionKey: string): void {
//     const isExpanded = this.expandedPropertySections.get(sectionKey) || false;
//     this.expandedPropertySections.set(sectionKey, !isExpanded);
//   }

//   /**
//    * Vérifie si une section de propriétés est étendue
//    */
//   isPropertySectionExpanded(sectionKey: string): boolean {
//     return this.expandedPropertySections.get(sectionKey) || false;
//   }

//   /**
//    * Valide la sélection des propriétés
//    */
//   validatePropertiesSelection(): boolean {
//     const selectedProducts = this.getSelectedRootProducts();
    
//     for (const productSelection of selectedProducts) {
//       const productProperties = this.getAvailablePropertiesForProduct(productSelection.product.productid);
//       const requiredProperties = productProperties.filter(p => p.isrequired);
      
//       for (const requiredProperty of requiredProperties) {
//         const isSelected = this.selectedProperties.some(
//           ps => ps.productid === productSelection.product.productid && 
//                 ps.propertyid === requiredProperty.propertyid && 
//                 ps.selected
//         );
        
//         if (!isSelected) {
//           return false;
//         }
//       }
//     }
    
//     return true;
//   }

//   /**
//    * Obtient le nombre de propriétés sélectionnées pour un produit
//    */
//   getSelectedPropertiesCountForProduct(productId: string): number {
//     return this.selectedProperties.filter(
//       ps => ps.productid === productId && ps.selected
//     ).length;
//   }

//   /**
//    * Obtient le prix total des propriétés pour un produit
//    */
//   getPropertiesPriceForProduct(productId: string): number {
//     return this.selectedProperties
//       .filter(ps => ps.productid === productId && ps.selected)
//       .reduce((total, ps) => total + (ps.property?.price || 0), 0);
//   }


//     /**
//    * Vide la sélection temporaire
//    */
//   clearTempSelection(): void {
//     this.tempSelectedProducts = [];
//     // Réappliquer les filtres pour réafficher les produits masqués
//     this.applyFiltersAndPagination();
//   }

//   /**
//    * Supprime un produit de la sélection temporaire
//    */
//   removeTempProduct(product: any): void {
//     const index = this.tempSelectedProducts.findIndex(
//       selection => selection.product.productid === product.productid
//     );
//     if (index >= 0) {
//       this.tempSelectedProducts.splice(index, 1);
//       // Réappliquer les filtres pour réafficher les produits de la généalogie
//       this.applyFiltersAndPagination();
//     }
//   }

//   /**
//    * Calcule le total de la sélection temporaire
//    */
//   getTempSelectionTotal(): number {
//     return this.tempSelectedProducts.reduce((total, selection) => {
//       return total + ((selection.product.price || 0) * selection.quantity);
//     }, 0);
//   }



//   /**
//    * Bascule l'expansion de tous les produits
//    */
//   toggleAllProductsExpansion(): void {
//     if (this.expandedProducts.size > 0) {
//       // Si des produits sont étendus, tout réduire
//       this.expandedProducts.clear();
//     } else {
//       // Si aucun produit n'est étendu, tout étendre
//       this.confirmedSelectedProducts.forEach(selection => {
//         if (this.hasDirectChildren(selection.product.productid)) {
//           this.expandedProducts.set(selection.product.productid, true);
//         }
//       });
//     }
//   }

//   /**
//    * Obtient la quantité totale confirmée
//    */
//   getTotalConfirmedQuantity(): number {
//     return this.confirmedSelectedProducts.reduce((total, selection) => {
//       return total + selection.quantity;
//     }, 0);
//   }

//   /**
//    * Supprime tous les produits confirmés
//    */
//   clearAllConfirmedProducts(): void {
//     this.confirmedSelectedProducts = [];
//     this.updateTotalPrice();
//     // Réappliquer les filtres pour réafficher tous les produits
//     this.applyFiltersAndPagination();
//   }






//     /**
//    * Vérifie si toutes les propriétés requises sont sélectionnées
//    */
//   areAllRequiredPropertiesSelected(): boolean {
//     const requiredProperties = this.allProperties.filter(prop => prop.isrequired);
//     const selectedRequiredProperties = this.selectedProperties.filter(prop => 
//       prop.selected && prop.property?.isrequired
//     );
    
//     return requiredProperties.length === selectedRequiredProperties.length;
//   }

//   /**
//    * Obtient la liste des propriétés requises non sélectionnées
//    */
//   getMissingRequiredProperties(): any[] {
//     const requiredProperties = this.allProperties.filter(prop => prop.isrequired);
//     const selectedRequiredIds = this.selectedProperties
//       .filter(prop => prop.selected && prop.property?.isrequired)
//       .map(prop => prop.property?.propertyid);
    
//     return requiredProperties.filter(prop => !selectedRequiredIds.includes(prop.propertyid));
//   }










// }

