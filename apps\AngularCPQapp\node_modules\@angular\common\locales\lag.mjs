/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (n === 0)
        return 0;
    if ((i === 0 || i === 1) && !(n === 0))
        return 1;
    return 5;
}
export default ["lag", [["TOO", "MUU"], u, u], u, [["P", "T", "E", "O", "A", "I", "M"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>atu", "<PERSON>ne", "<PERSON><PERSON><PERSON>", "<PERSON>h", "Ijm", "Móos<PERSON>"], ["<PERSON><PERSON>p<PERSON>iri", "<PERSON><PERSON><PERSON><PERSON>", "Jumaíne", "Jumatáano", "Alam<PERSON><PERSON>", "<PERSON>jum<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>h", "Ijm", "<PERSON><PERSON><PERSON><PERSON>"]], u, [["F", "N", "<PERSON>", "<PERSON>", "I", "I", "M", "V", "S", "I", "S", "S"], ["Fúngatɨ", "Naanɨ", "Keenda", "Ikúmi", "Inyambala", "Idwaata", "Mʉʉnchɨ", "Vɨɨrɨ", "Saatʉ", "Inyi", "Saano", "Sasatʉ"], ["Kʉfúngatɨ", "Kʉnaanɨ", "Kʉkeenda", "Kwiikumi", "Kwiinyambála", "Kwiidwaata", "Kʉmʉʉnchɨ", "Kʉvɨɨrɨ", "Kʉsaatʉ", "Kwiinyi", "Kʉsaano", "Kʉsasatʉ"]], u, [["KSA", "KA"], u, ["Kɨrɨsitʉ sɨ anavyaal", "Kɨrɨsitʉ akavyaalwe"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "TZS", "TSh", "Shilíingi ya Taansanía", { "JPY": ["JP¥", "¥"], "TZS": ["TSh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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