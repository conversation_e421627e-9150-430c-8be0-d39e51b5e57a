/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    BYN: (string | undefined)[];
    CSK: string[];
    CZK: string[];
    ILS: (string | undefined)[];
    INR: (string | undefined)[];
    JPY: string[];
    PHP: (string | undefined)[];
    RON: (string | undefined)[];
    RUR: (string | undefined)[];
    TWD: string[];
    USD: string[];
    VND: (string | undefined)[];
    XEU: string[];
    XXX: never[];
} | undefined)[];
export default _default;
