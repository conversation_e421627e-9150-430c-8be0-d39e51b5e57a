{"version": 3, "file": "utrie.es5.js", "sources": ["../../src/Util.ts", "../../src/Trie.ts", "../node_modules/src/index.ts", "../../src/TrieBuilder.ts"], "sourcesContent": ["const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\n\nexport const decode = (base64: string): ArrayBuffer | number[] => {\n    let bufferLength = base64.length * 0.75,\n        len = base64.length,\n        i,\n        p = 0,\n        encoded1,\n        encoded2,\n        encoded3,\n        encoded4;\n\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    const buffer =\n        typeof ArrayBuffer !== 'undefined' &&\n        typeof Uint8Array !== 'undefined' &&\n        typeof Uint8Array.prototype.slice !== 'undefined'\n            ? new ArrayBuffer(bufferLength)\n            : new Array(bufferLength);\n    const bytes = Array.isArray(buffer) ? buffer : new Uint8Array(buffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return buffer;\n};\n\nexport const polyUint16Array = (buffer: number[]): number[] => {\n    const length = buffer.length;\n    const bytes = [];\n    for (let i = 0; i < length; i += 2) {\n        bytes.push((buffer[i + 1] << 8) | buffer[i]);\n    }\n    return bytes;\n};\n\nexport const polyUint32Array = (buffer: number[]): number[] => {\n    const length = buffer.length;\n    const bytes = [];\n    for (let i = 0; i < length; i += 4) {\n        bytes.push((buffer[i + 3] << 24) | (buffer[i + 2] << 16) | (buffer[i + 1] << 8) | buffer[i]);\n    }\n    return bytes;\n};\n", "import {decode, polyUint16Array, polyUint32Array} from './Util';\n\nexport type int = number;\n\n/** Shift size for getting the index-2 table offset. */\nexport const UTRIE2_SHIFT_2 = 5;\n\n/** Shift size for getting the index-1 table offset. */\nexport const UTRIE2_SHIFT_1 = 6 + 5;\n\n/**\n * Shift size for shifting left the index array values.\n * Increases possible data size with 16-bit index values at the cost\n * of compactability.\n * This requires data blocks to be aligned by UTRIE2_DATA_GRANULARITY.\n */\nexport const UTRIE2_INDEX_SHIFT = 2;\n\n/**\n * Difference between the two shift sizes,\n * for getting an index-1 offset from an index-2 offset. 6=11-5\n */\nexport const UTRIE2_SHIFT_1_2 = UTRIE2_SHIFT_1 - UTRIE2_SHIFT_2;\n\n/**\n * The part of the index-2 table for U+D800..U+DBFF stores values for\n * lead surrogate code _units_ not code _points_.\n * Values for lead surrogate code _points_ are indexed with this portion of the table.\n * Length=32=0x20=0x400>>UTRIE2_SHIFT_2. (There are 1024=0x400 lead surrogates.)\n */\nexport const UTRIE2_LSCP_INDEX_2_OFFSET = 0x10000 >> UTRIE2_SHIFT_2;\n\n/** Number of entries in a data block. 32=0x20 */\nexport const UTRIE2_DATA_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_2;\n/** Mask for getting the lower bits for the in-data-block offset. */\nexport const UTRIE2_DATA_MASK = UTRIE2_DATA_BLOCK_LENGTH - 1;\n\nexport const UTRIE2_LSCP_INDEX_2_LENGTH = 0x400 >> UTRIE2_SHIFT_2;\n/** Count the lengths of both BMP pieces. 2080=0x820 */\nexport const UTRIE2_INDEX_2_BMP_LENGTH = UTRIE2_LSCP_INDEX_2_OFFSET + UTRIE2_LSCP_INDEX_2_LENGTH;\n/**\n * The 2-byte UTF-8 version of the index-2 table follows at offset 2080=0x820.\n * Length 32=0x20 for lead bytes C0..DF, regardless of UTRIE2_SHIFT_2.\n */\nexport const UTRIE2_UTF8_2B_INDEX_2_OFFSET = UTRIE2_INDEX_2_BMP_LENGTH;\nexport const UTRIE2_UTF8_2B_INDEX_2_LENGTH = 0x800 >> 6; /* U+0800 is the first code point after 2-byte UTF-8 */\n/**\n * The index-1 table, only used for supplementary code points, at offset 2112=0x840.\n * Variable length, for code points up to highStart, where the last single-value range starts.\n * Maximum length 512=0x200=0x100000>>UTRIE2_SHIFT_1.\n * (For 0x100000 supplementary code points U+10000..U+10ffff.)\n *\n * The part of the index-2 table for supplementary code points starts\n * after this index-1 table.\n *\n * Both the index-1 table and the following part of the index-2 table\n * are omitted completely if there is only BMP data.\n */\nexport const UTRIE2_INDEX_1_OFFSET = UTRIE2_UTF8_2B_INDEX_2_OFFSET + UTRIE2_UTF8_2B_INDEX_2_LENGTH;\n\n/**\n * Number of index-1 entries for the BMP. 32=0x20\n * This part of the index-1 table is omitted from the serialized form.\n */\nexport const UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = 0x10000 >> UTRIE2_SHIFT_1;\n\n/** Number of entries in an index-2 block. 64=0x40 */\nexport const UTRIE2_INDEX_2_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_1_2;\n/** Mask for getting the lower bits for the in-index-2-block offset. */\nexport const UTRIE2_INDEX_2_MASK = UTRIE2_INDEX_2_BLOCK_LENGTH - 1;\n\nconst slice16 = (view: number[] | Uint16Array, start: number, end?: number) => {\n    if (view.slice) {\n        return view.slice(start, end);\n    }\n\n    return new Uint16Array(Array.prototype.slice.call(view, start, end));\n};\n\nconst slice32 = (view: number[] | Uint32Array, start: number, end?: number) => {\n    if (view.slice) {\n        return view.slice(start, end);\n    }\n\n    return new Uint32Array(Array.prototype.slice.call(view, start, end));\n};\n\nexport const createTrieFromBase64 = (base64: string, _byteLength: number): Trie => {\n    const buffer = decode(base64);\n    const view32 = Array.isArray(buffer) ? polyUint32Array(buffer) : new Uint32Array(buffer);\n    const view16 = Array.isArray(buffer) ? polyUint16Array(buffer) : new Uint16Array(buffer);\n    const headerLength = 24;\n\n    const index = slice16(view16, headerLength / 2, view32[4] / 2);\n    const data =\n        view32[5] === 2\n            ? slice16(view16, (headerLength + view32[4]) / 2)\n            : slice32(view32, Math.ceil((headerLength + view32[4]) / 4));\n\n    return new Trie(view32[0], view32[1], view32[2], view32[3], index, data);\n};\n\nexport class Trie {\n    initialValue: int;\n    errorValue: int;\n    highStart: int;\n    highValueIndex: int;\n    index: Uint16Array | number[];\n    data: Uint32Array | Uint16Array | number[];\n\n    constructor(\n        initialValue: int,\n        errorValue: int,\n        highStart: int,\n        highValueIndex: int,\n        index: Uint16Array | number[],\n        data: Uint32Array | Uint16Array | number[]\n    ) {\n        this.initialValue = initialValue;\n        this.errorValue = errorValue;\n        this.highStart = highStart;\n        this.highValueIndex = highValueIndex;\n        this.index = index;\n        this.data = data;\n    }\n\n    /**\n     * Get the value for a code point as stored in the Trie.\n     *\n     * @param codePoint the code point\n     * @return the value\n     */\n    get(codePoint: number): number {\n        let ix;\n        if (codePoint >= 0) {\n            if (codePoint < 0x0d800 || (codePoint > 0x0dbff && codePoint <= 0x0ffff)) {\n                // Ordinary BMP code point, excluding leading surrogates.\n                // BMP uses a single level lookup.  BMP index starts at offset 0 in the Trie2 index.\n                // 16 bit data is stored in the index array itself.\n                ix = this.index[codePoint >> UTRIE2_SHIFT_2];\n                ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                return this.data[ix];\n            }\n\n            if (codePoint <= 0xffff) {\n                // Lead Surrogate Code Point.  A Separate index section is stored for\n                // lead surrogate code units and code points.\n                //   The main index has the code unit data.\n                //   For this function, we need the code point data.\n                // Note: this expression could be refactored for slightly improved efficiency, but\n                //       surrogate code points will be so rare in practice that it's not worth it.\n                ix = this.index[UTRIE2_LSCP_INDEX_2_OFFSET + ((codePoint - 0xd800) >> UTRIE2_SHIFT_2)];\n                ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                return this.data[ix];\n            }\n\n            if (codePoint < this.highStart) {\n                // Supplemental code point, use two-level lookup.\n                ix = UTRIE2_INDEX_1_OFFSET - UTRIE2_OMITTED_BMP_INDEX_1_LENGTH + (codePoint >> UTRIE2_SHIFT_1);\n                ix = this.index[ix];\n                ix += (codePoint >> UTRIE2_SHIFT_2) & UTRIE2_INDEX_2_MASK;\n                ix = this.index[ix];\n                ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                return this.data[ix];\n            }\n            if (codePoint <= 0x10ffff) {\n                return this.data[this.highValueIndex];\n            }\n        }\n\n        // Fall through.  The code point is outside of the legal range of 0..0x10ffff.\n        return this.errorValue;\n    }\n}\n", "const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\n\nexport const encode = (arraybuffer: ArrayBuffer): string => {\n    let bytes = new Uint8Array(arraybuffer),\n        i,\n        len = bytes.length,\n        base64 = '';\n\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    } else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n\n    return base64;\n};\n\nexport const decode = (base64: string): ArrayBuffer => {\n    let bufferLength = base64.length * 0.75,\n        len = base64.length,\n        i,\n        p = 0,\n        encoded1,\n        encoded2,\n        encoded3,\n        encoded4;\n\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    const arraybuffer = new ArrayBuffer(bufferLength),\n        bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n};\n", "import {\n    UTRIE2_SHIFT_2,\n    UTRIE2_INDEX_SHIFT,\n    UTRIE2_LSCP_INDEX_2_OFFSET,\n    UTRIE2_DATA_BLOCK_LENGTH,\n    UTRIE2_DATA_MASK,\n    UTRIE2_SHIFT_1,\n    UTRIE2_INDEX_1_OFFSET,\n    UTRIE2_UTF8_2B_INDEX_2_LENGTH,\n    UTRIE2_OMITTED_BMP_INDEX_1_LENGTH,\n    UTRIE2_INDEX_2_BMP_LENGTH,\n    UTRIE2_LSCP_INDEX_2_LENGTH,\n    UTRIE2_INDEX_2_BLOCK_LENGTH,\n    UTRIE2_INDEX_2_MASK,\n    UTRIE2_SHIFT_1_2,\n    Trie,\n    int,\n} from './Trie';\n\nimport {encode} from 'base64-arraybuffer';\n\n/**\n * Trie2 constants, defining shift widths, index array lengths, etc.\n *\n * These are needed for the runtime macros but users can treat these as\n * implementation details and skip to the actual public API further below.\n */\n// const UTRIE2_OPTIONS_VALUE_BITS_MASK = 0x000f;\n\n/** Number of code points per index-1 table entry. 2048=0x800 */\nconst UTRIE2_CP_PER_INDEX_1_ENTRY = 1 << UTRIE2_SHIFT_1;\n\n/** The alignment size of a data block. Also the granularity for compaction. */\nconst UTRIE2_DATA_GRANULARITY = 1 << UTRIE2_INDEX_SHIFT;\n/* Fixed layout of the first part of the index array. ------------------- */\n/**\n * The BMP part of the index-2 table is fixed and linear and starts at offset 0.\n * Length=2048=0x800=0x10000>>UTRIE2_SHIFT_2.\n */\nconst UTRIE2_INDEX_2_OFFSET = 0;\n\nconst UTRIE2_MAX_INDEX_1_LENGTH = 0x100000 >> UTRIE2_SHIFT_1;\n/*\n * Fixed layout of the first part of the data array. -----------------------\n * Starts with 4 blocks (128=0x80 entries) for ASCII.\n */\n/**\n * The illegal-UTF-8 data block follows the ASCII block, at offset 128=0x80.\n * Used with linear access for single bytes 0..0xbf for simple error handling.\n * Length 64=0x40, not UTRIE2_DATA_BLOCK_LENGTH.\n */\nconst UTRIE2_BAD_UTF8_DATA_OFFSET = 0x80;\n/** The start of non-linear-ASCII data blocks, at offset 192=0xc0. */\nconst UTRIE2_DATA_START_OFFSET = 0xc0;\n/* Building a Trie2 ---------------------------------------------------------- */\n/*\n * These definitions are mostly needed by utrie2_builder.c, but also by\n * utrie2_get32() and utrie2_enum().\n */\n/*\n * At build time, leave a gap in the index-2 table,\n * at least as long as the maximum lengths of the 2-byte UTF-8 index-2 table\n * and the supplementary index-1 table.\n * Round up to UTRIE2_INDEX_2_BLOCK_LENGTH for proper compacting.\n */\nconst UNEWTRIE2_INDEX_GAP_OFFSET = UTRIE2_INDEX_2_BMP_LENGTH;\nconst UNEWTRIE2_INDEX_GAP_LENGTH =\n    (UTRIE2_UTF8_2B_INDEX_2_LENGTH + UTRIE2_MAX_INDEX_1_LENGTH + UTRIE2_INDEX_2_MASK) & ~UTRIE2_INDEX_2_MASK;\n/**\n * Maximum length of the build-time index-2 array.\n * Maximum number of Unicode code points (0x110000) shifted right by UTRIE2_SHIFT_2,\n * plus the part of the index-2 table for lead surrogate code points,\n * plus the build-time index gap,\n * plus the null index-2 block.\n */\nconst UNEWTRIE2_MAX_INDEX_2_LENGTH =\n    (0x110000 >> UTRIE2_SHIFT_2) +\n    UTRIE2_LSCP_INDEX_2_LENGTH +\n    UNEWTRIE2_INDEX_GAP_LENGTH +\n    UTRIE2_INDEX_2_BLOCK_LENGTH;\nconst UNEWTRIE2_INDEX_1_LENGTH = 0x110000 >> UTRIE2_SHIFT_1;\n/**\n * Maximum length of the build-time data array.\n * One entry per 0x110000 code points, plus the illegal-UTF-8 block and the null block,\n * plus values for the 0x400 surrogate code units.\n */\nconst UNEWTRIE2_MAX_DATA_LENGTH = 0x110000 + 0x40 + 0x40 + 0x400;\n\n/* Start with allocation of 16k data entries. */\nconst UNEWTRIE2_INITIAL_DATA_LENGTH = 1 << 14;\n/* Grow about 8x each time. */\nconst UNEWTRIE2_MEDIUM_DATA_LENGTH = 1 << 17;\n\n/** The null index-2 block, following the gap in the index-2 table. */\nconst UNEWTRIE2_INDEX_2_NULL_OFFSET = UNEWTRIE2_INDEX_GAP_OFFSET + UNEWTRIE2_INDEX_GAP_LENGTH;\n/** The start of allocated index-2 blocks. */\nconst UNEWTRIE2_INDEX_2_START_OFFSET = UNEWTRIE2_INDEX_2_NULL_OFFSET + UTRIE2_INDEX_2_BLOCK_LENGTH;\n/**\n * The null data block.\n * Length 64=0x40 even if UTRIE2_DATA_BLOCK_LENGTH is smaller,\n * to work with 6-bit trail bytes from 2-byte UTF-8.\n */\nconst UNEWTRIE2_DATA_NULL_OFFSET = UTRIE2_DATA_START_OFFSET;\n/** The start of allocated data blocks. */\nconst UNEWTRIE2_DATA_START_OFFSET = UNEWTRIE2_DATA_NULL_OFFSET + 0x40;\n/**\n * The start of data blocks for U+0800 and above.\n * Below, compaction uses a block length of 64 for 2-byte UTF-8.\n * From here on, compaction uses UTRIE2_DATA_BLOCK_LENGTH.\n * Data values for 0x780 code points beyond ASCII.\n */\nconst UNEWTRIE2_DATA_0800_OFFSET = UNEWTRIE2_DATA_START_OFFSET + 0x780;\n\n/**\n * Maximum length of the runtime index array.\n * Limited by its own 16-bit index values, and by uint16_t UTrie2Header.indexLength.\n * (The actual maximum length is lower,\n * (0x110000>>UTRIE2_SHIFT_2)+UTRIE2_UTF8_2B_INDEX_2_LENGTH+UTRIE2_MAX_INDEX_1_LENGTH.)\n */\nconst UTRIE2_MAX_INDEX_LENGTH = 0xffff;\n/**\n * Maximum length of the runtime data array.\n * Limited by 16-bit index values that are left-shifted by UTRIE2_INDEX_SHIFT,\n * and by uint16_t UTrie2Header.shiftedDataLength.\n */\nconst UTRIE2_MAX_DATA_LENGTH = 0xffff << UTRIE2_INDEX_SHIFT;\n\nexport const BITS_16 = 16;\nexport const BITS_32 = 32;\n\nconst isHighSurrogate = (c: int): boolean => c >= 0xd800 && c <= 0xdbff;\n\nconst equalInt = (a: Uint32Array, s: int, t: int, length: int): boolean => {\n    for (let i = 0; i < length; i++) {\n        if (a[s + i] !== a[t + i]) {\n            return false;\n        }\n    }\n    return true;\n};\n\nexport class TrieBuilder {\n    index1: Uint32Array;\n    index2: Uint32Array;\n    map: Uint32Array;\n    data: Uint32Array;\n    dataCapacity: int;\n    initialValue: int;\n    errorValue: int;\n    highStart: int;\n    dataNullOffset: int;\n    dataLength: int;\n    index2NullOffset: int;\n    index2Length: int;\n    firstFreeBlock: int;\n    isCompacted: boolean;\n\n    constructor(initialValue: int = 0, errorValue: int = 0) {\n        this.initialValue = initialValue;\n        this.errorValue = errorValue;\n        this.highStart = 0x110000;\n        this.data = new Uint32Array(UNEWTRIE2_INITIAL_DATA_LENGTH);\n        this.dataCapacity = UNEWTRIE2_INITIAL_DATA_LENGTH;\n        this.highStart = 0x110000;\n        this.firstFreeBlock = 0; /* no free block in the list */\n        this.isCompacted = false;\n\n        this.index1 = new Uint32Array(UNEWTRIE2_INDEX_1_LENGTH);\n        this.index2 = new Uint32Array(UNEWTRIE2_MAX_INDEX_2_LENGTH);\n\n        /*\n         * Multi-purpose per-data-block table.\n         *\n         * Before compacting:\n         *\n         * Per-data-block reference counters/free-block list.\n         *  0: unused\n         * >0: reference counter (number of index-2 entries pointing here)\n         * <0: next free data block in free-block list\n         *\n         * While compacting:\n         *\n         * Map of adjusted indexes, used in compactData() and compactIndex2().\n         * Maps from original indexes to new ones.\n         */\n        this.map = new Uint32Array(UNEWTRIE2_MAX_DATA_LENGTH >> UTRIE2_SHIFT_2);\n        /*\n         * preallocate and reset\n         * - ASCII\n         * - the bad-UTF-8-data block\n         * - the null data block\n         */\n        let i, j;\n        for (i = 0; i < 0x80; ++i) {\n            this.data[i] = initialValue;\n        }\n        for (; i < 0xc0; ++i) {\n            this.data[i] = errorValue;\n        }\n        for (i = UNEWTRIE2_DATA_NULL_OFFSET; i < UNEWTRIE2_DATA_START_OFFSET; ++i) {\n            this.data[i] = initialValue;\n        }\n        this.dataNullOffset = UNEWTRIE2_DATA_NULL_OFFSET;\n        this.dataLength = UNEWTRIE2_DATA_START_OFFSET;\n        /* set the index-2 indexes for the 2=0x80>>UTRIE2_SHIFT_2 ASCII data blocks */\n        for (i = 0, j = 0; j < 0x80; ++i, j += UTRIE2_DATA_BLOCK_LENGTH) {\n            this.index2[i] = j;\n            this.map[i] = 1;\n        }\n\n        /* reference counts for the bad-UTF-8-data block */\n        for (; j < 0xc0; ++i, j += UTRIE2_DATA_BLOCK_LENGTH) {\n            this.map[i] = 0;\n        }\n\n        /*\n         * Reference counts for the null data block: all blocks except for the ASCII blocks.\n         * Plus 1 so that we don't drop this block during compaction.\n         * Plus as many as needed for lead surrogate code points.\n         */\n        /* i==newTrie->dataNullOffset */\n        this.map[i++] = (0x110000 >> UTRIE2_SHIFT_2) - (0x80 >> UTRIE2_SHIFT_2) + 1 + UTRIE2_LSCP_INDEX_2_LENGTH;\n        j += UTRIE2_DATA_BLOCK_LENGTH;\n        for (; j < UNEWTRIE2_DATA_START_OFFSET; ++i, j += UTRIE2_DATA_BLOCK_LENGTH) {\n            this.map[i] = 0;\n        }\n        /*\n         * set the remaining indexes in the BMP index-2 block\n         * to the null data block\n         */\n        for (i = 0x80 >> UTRIE2_SHIFT_2; i < UTRIE2_INDEX_2_BMP_LENGTH; ++i) {\n            this.index2[i] = UNEWTRIE2_DATA_NULL_OFFSET;\n        }\n        /*\n         * Fill the index gap with impossible values so that compaction\n         * does not overlap other index-2 blocks with the gap.\n         */\n        for (i = 0; i < UNEWTRIE2_INDEX_GAP_LENGTH; ++i) {\n            this.index2[UNEWTRIE2_INDEX_GAP_OFFSET + i] = -1;\n        }\n        /* set the indexes in the null index-2 block */\n        for (i = 0; i < UTRIE2_INDEX_2_BLOCK_LENGTH; ++i) {\n            this.index2[UNEWTRIE2_INDEX_2_NULL_OFFSET + i] = UNEWTRIE2_DATA_NULL_OFFSET;\n        }\n        this.index2NullOffset = UNEWTRIE2_INDEX_2_NULL_OFFSET;\n        this.index2Length = UNEWTRIE2_INDEX_2_START_OFFSET;\n        /* set the index-1 indexes for the linear index-2 block */\n        for (i = 0, j = 0; i < UTRIE2_OMITTED_BMP_INDEX_1_LENGTH; ++i, j += UTRIE2_INDEX_2_BLOCK_LENGTH) {\n            this.index1[i] = j;\n        }\n        /* set the remaining index-1 indexes to the null index-2 block */\n        for (; i < UNEWTRIE2_INDEX_1_LENGTH; ++i) {\n            this.index1[i] = UNEWTRIE2_INDEX_2_NULL_OFFSET;\n        }\n        /*\n         * Preallocate and reset data for U+0080..U+07ff,\n         * for 2-byte UTF-8 which will be compacted in 64-blocks\n         * even if UTRIE2_DATA_BLOCK_LENGTH is smaller.\n         */\n        for (i = 0x80; i < 0x800; i += UTRIE2_DATA_BLOCK_LENGTH) {\n            this.set(i, initialValue);\n        }\n    }\n\n    /**\n     * Set a value for a code point.\n     *\n     * @param c the code point\n     * @param value the value\n     */\n    set(c: int, value: int): TrieBuilder {\n        if (c < 0 || c > 0x10ffff) {\n            throw new Error('Invalid code point.');\n        }\n        this._set(c, true, value);\n        return this;\n    }\n\n    /**\n     * Set a value in a range of code points [start..end].\n     * All code points c with start<=c<=end will get the value if\n     * overwrite is TRUE or if the old value is the initial value.\n     *\n     * @param start the first code point to get the value\n     * @param end the last code point to get the value (inclusive)\n     * @param value the value\n     * @param overwrite flag for whether old non-initial values are to be overwritten\n     */\n    setRange(start: int, end: int, value: int, overwrite: boolean = false): TrieBuilder {\n        /*\n         * repeat value in [start..end]\n         * mark index values for repeat-data blocks by setting bit 31 of the index values\n         * fill around existing values if any, if(overwrite)\n         */\n        let block, rest, repeatBlock;\n        if (start > 0x10ffff || start < 0 || end > 0x10ffff || end < 0 || start > end) {\n            throw new Error('Invalid code point range.');\n        }\n        if (!overwrite && value === this.initialValue) {\n            return this; /* nothing to do */\n        }\n        if (this.isCompacted) {\n            throw new Error('Trie was already compacted');\n        }\n        let limit = end + 1;\n        if ((start & UTRIE2_DATA_MASK) !== 0) {\n            /* set partial block at [start..following block boundary[ */\n            block = this.getDataBlock(start, true);\n            const nextStart = (start + UTRIE2_DATA_BLOCK_LENGTH) & ~UTRIE2_DATA_MASK;\n            if (nextStart <= limit) {\n                this.fillBlock(\n                    block,\n                    start & UTRIE2_DATA_MASK,\n                    UTRIE2_DATA_BLOCK_LENGTH,\n                    value,\n                    this.initialValue,\n                    overwrite\n                );\n                start = nextStart;\n            } else {\n                this.fillBlock(\n                    block,\n                    start & UTRIE2_DATA_MASK,\n                    limit & UTRIE2_DATA_MASK,\n                    value,\n                    this.initialValue,\n                    overwrite\n                );\n                return this;\n            }\n        }\n        /* number of positions in the last, partial block */\n        rest = limit & UTRIE2_DATA_MASK;\n        /* round down limit to a block boundary */\n        limit &= ~UTRIE2_DATA_MASK;\n        /* iterate over all-value blocks */\n        repeatBlock = value === this.initialValue ? this.dataNullOffset : -1;\n\n        while (start < limit) {\n            let i2;\n            let setRepeatBlock = false;\n            if (value === this.initialValue && this.isInNullBlock(start, true)) {\n                start += UTRIE2_DATA_BLOCK_LENGTH; /* nothing to do */\n                continue;\n            }\n            /* get index value */\n            i2 = this.getIndex2Block(start, true);\n            i2 += (start >> UTRIE2_SHIFT_2) & UTRIE2_INDEX_2_MASK;\n            block = this.index2[i2];\n            if (this.isWritableBlock(block)) {\n                /* already allocated */\n                if (overwrite && block >= UNEWTRIE2_DATA_0800_OFFSET) {\n                    /*\n                     * We overwrite all values, and it's not a\n                     * protected (ASCII-linear or 2-byte UTF-8) block:\n                     * replace with the repeatBlock.\n                     */\n                    setRepeatBlock = true;\n                } else {\n                    /* !overwrite, or protected block: just write the values into this block */\n                    this.fillBlock(block, 0, UTRIE2_DATA_BLOCK_LENGTH, value, this.initialValue, overwrite);\n                }\n            } else if (this.data[block] !== value && (overwrite || block === this.dataNullOffset)) {\n                /*\n                 * Set the repeatBlock instead of the null block or previous repeat block:\n                 *\n                 * If !isWritableBlock() then all entries in the block have the same value\n                 * because it's the null block or a range block (the repeatBlock from a previous\n                 * call to utrie2_setRange32()).\n                 * No other blocks are used multiple times before compacting.\n                 *\n                 * The null block is the only non-writable block with the initialValue because\n                 * of the repeatBlock initialization above. (If value==initialValue, then\n                 * the repeatBlock will be the null data block.)\n                 *\n                 * We set our repeatBlock if the desired value differs from the block's value,\n                 * and if we overwrite any data or if the data is all initial values\n                 * (which is the same as the block being the null block, see above).\n                 */\n                setRepeatBlock = true;\n            }\n            if (setRepeatBlock) {\n                if (repeatBlock >= 0) {\n                    this.setIndex2Entry(i2, repeatBlock);\n                } else {\n                    /* create and set and fill the repeatBlock */\n                    repeatBlock = this.getDataBlock(start, true);\n                    this.writeBlock(repeatBlock, value);\n                }\n            }\n            start += UTRIE2_DATA_BLOCK_LENGTH;\n        }\n        if (rest > 0) {\n            /* set partial block at [last block boundary..limit[ */\n            block = this.getDataBlock(start, true);\n            this.fillBlock(block, 0, rest, value, this.initialValue, overwrite);\n        }\n        return this;\n    }\n\n    /**\n     * Get the value for a code point as stored in the Trie2.\n     *\n     * @param codePoint the code point\n     * @return the value\n     */\n\n    get(codePoint: int): int {\n        if (codePoint < 0 || codePoint > 0x10ffff) {\n            return this.errorValue;\n        } else {\n            return this._get(codePoint, true);\n        }\n    }\n\n    _get(c: int, fromLSCP: boolean): int {\n        let i2;\n        if (c >= this.highStart && (!(c >= 0xd800 && c < 0xdc00) || fromLSCP)) {\n            return this.data[this.dataLength - UTRIE2_DATA_GRANULARITY];\n        }\n        if (c >= 0xd800 && c < 0xdc00 && fromLSCP) {\n            i2 = UTRIE2_LSCP_INDEX_2_OFFSET - (0xd800 >> UTRIE2_SHIFT_2) + (c >> UTRIE2_SHIFT_2);\n        } else {\n            i2 = this.index1[c >> UTRIE2_SHIFT_1] + ((c >> UTRIE2_SHIFT_2) & UTRIE2_INDEX_2_MASK);\n        }\n        const block = this.index2[i2];\n        return this.data[block + (c & UTRIE2_DATA_MASK)];\n    }\n\n    freeze(valueBits: 16 | 32 = BITS_32): Trie {\n        let i;\n        let allIndexesLength;\n        let dataMove; /* >0 if the data is moved to the end of the index array */\n        /* compact if necessary */\n        if (!this.isCompacted) {\n            this.compactTrie();\n        }\n\n        allIndexesLength = this.highStart <= 0x10000 ? UTRIE2_INDEX_1_OFFSET : this.index2Length;\n\n        if (valueBits === BITS_16) {\n            // dataMove = allIndexesLength;\n            dataMove = 0;\n        } else {\n            dataMove = 0;\n        }\n        /* are indexLength and dataLength within limits? */\n        if (\n            /* for unshifted indexLength */\n            allIndexesLength > UTRIE2_MAX_INDEX_LENGTH ||\n            /* for unshifted dataNullOffset */\n            dataMove + this.dataNullOffset > 0xffff ||\n            /* for unshifted 2-byte UTF-8 index-2 values */\n            dataMove + UNEWTRIE2_DATA_0800_OFFSET > 0xffff ||\n            /* for shiftedDataLength */\n            dataMove + this.dataLength > UTRIE2_MAX_DATA_LENGTH\n        ) {\n            throw new Error('Trie data is too large.');\n        }\n\n        const index = new Uint16Array(allIndexesLength);\n\n        /* write the index-2 array values shifted right by UTRIE2_INDEX_SHIFT, after adding dataMove */\n        let destIdx = 0;\n        for (i = 0; i < UTRIE2_INDEX_2_BMP_LENGTH; i++) {\n            index[destIdx++] = (this.index2[i] + dataMove) >> UTRIE2_INDEX_SHIFT;\n        }\n        /* write UTF-8 2-byte index-2 values, not right-shifted */\n        for (i = 0; i < 0xc2 - 0xc0; ++i) {\n            /* C0..C1 */\n            index[destIdx++] = dataMove + UTRIE2_BAD_UTF8_DATA_OFFSET;\n        }\n        for (; i < 0xe0 - 0xc0; ++i) {\n            /* C2..DF */\n            index[destIdx++] = dataMove + this.index2[i << (6 - UTRIE2_SHIFT_2)];\n        }\n\n        if (this.highStart > 0x10000) {\n            const index1Length = (this.highStart - 0x10000) >> UTRIE2_SHIFT_1;\n            const index2Offset = UTRIE2_INDEX_2_BMP_LENGTH + UTRIE2_UTF8_2B_INDEX_2_LENGTH + index1Length;\n            /* write 16-bit index-1 values for supplementary code points */\n            for (i = 0; i < index1Length; i++) {\n                index[destIdx++] = UTRIE2_INDEX_2_OFFSET + this.index1[i + UTRIE2_OMITTED_BMP_INDEX_1_LENGTH];\n            }\n\n            /*\n             * write the index-2 array values for supplementary code points,\n             * shifted right by UTRIE2_INDEX_SHIFT, after adding dataMove\n             */\n            for (i = 0; i < this.index2Length - index2Offset; i++) {\n                index[destIdx++] = (dataMove + this.index2[index2Offset + i]) >> UTRIE2_INDEX_SHIFT;\n            }\n        }\n\n        /* write the 16/32-bit data array */\n        switch (valueBits) {\n            case BITS_16:\n                /* write 16-bit data values */\n                const data16 = new Uint16Array(this.dataLength);\n                for (i = 0; i < this.dataLength; i++) {\n                    data16[i] = this.data[i];\n                }\n\n                return new Trie(\n                    this.initialValue,\n                    this.errorValue,\n                    this.highStart,\n                    dataMove + this.dataLength - UTRIE2_DATA_GRANULARITY,\n                    index,\n                    data16\n                );\n            case BITS_32:\n                /* write 32-bit data values */\n                const data32 = new Uint32Array(this.dataLength);\n                for (i = 0; i < this.dataLength; i++) {\n                    data32[i] = this.data[i];\n                }\n                return new Trie(\n                    this.initialValue,\n                    this.errorValue,\n                    this.highStart,\n                    dataMove + this.dataLength - UTRIE2_DATA_GRANULARITY,\n                    index,\n                    data32\n                );\n            default:\n                throw new Error('Bits should be either 16 or 32');\n        }\n    }\n\n    /*\n     * Find the start of the last range in the trie by enumerating backward.\n     * Indexes for supplementary code points higher than this will be omitted.\n     */\n    findHighStart(highValue: int): int {\n        let value;\n        let i2, j, i2Block, prevI2Block, block, prevBlock;\n        /* set variables for previous range */\n        if (highValue === this.initialValue) {\n            prevI2Block = this.index2NullOffset;\n            prevBlock = this.dataNullOffset;\n        } else {\n            prevI2Block = -1;\n            prevBlock = -1;\n        }\n        let prev = 0x110000;\n        /* enumerate index-2 blocks */\n        let i1 = UNEWTRIE2_INDEX_1_LENGTH;\n        let c = prev;\n        while (c > 0) {\n            i2Block = this.index1[--i1];\n            if (i2Block === prevI2Block) {\n                /* the index-2 block is the same as the previous one, and filled with highValue */\n                c -= UTRIE2_CP_PER_INDEX_1_ENTRY;\n                continue;\n            }\n            prevI2Block = i2Block;\n            if (i2Block === this.index2NullOffset) {\n                /* this is the null index-2 block */\n                if (highValue !== this.initialValue) {\n                    return c;\n                }\n                c -= UTRIE2_CP_PER_INDEX_1_ENTRY;\n            } else {\n                /* enumerate data blocks for one index-2 block */\n                for (i2 = UTRIE2_INDEX_2_BLOCK_LENGTH; i2 > 0; ) {\n                    block = this.index2[i2Block + --i2];\n                    if (block === prevBlock) {\n                        /* the block is the same as the previous one, and filled with highValue */\n                        c -= UTRIE2_DATA_BLOCK_LENGTH;\n                        continue;\n                    }\n                    prevBlock = block;\n                    if (block === this.dataNullOffset) {\n                        /* this is the null data block */\n                        if (highValue !== this.initialValue) {\n                            return c;\n                        }\n                        c -= UTRIE2_DATA_BLOCK_LENGTH;\n                    } else {\n                        for (j = UTRIE2_DATA_BLOCK_LENGTH; j > 0; ) {\n                            value = this.data[block + --j];\n                            if (value !== highValue) {\n                                return c;\n                            }\n                            --c;\n                        }\n                    }\n                }\n            }\n        }\n        /* deliver last range */\n        return 0;\n    }\n\n    /*\n     * Compact a build-time trie.\n     *\n     * The compaction\n     * - removes blocks that are identical with earlier ones\n     * - overlaps adjacent blocks as much as possible (if overlap==TRUE)\n     * - moves blocks in steps of the data granularity\n     * - moves and overlaps blocks that overlap with multiple values in the overlap region\n     *\n     * It does not\n     * - try to move and overlap blocks that are not already adjacent\n     */\n    compactData() {\n        let start, movedStart;\n        let blockLength, overlap;\n        let i, mapIndex, blockCount;\n        /* do not compact linear-ASCII data */\n        let newStart = UTRIE2_DATA_START_OFFSET;\n        for (start = 0, i = 0; start < newStart; start += UTRIE2_DATA_BLOCK_LENGTH, ++i) {\n            this.map[i] = start;\n        }\n        /*\n         * Start with a block length of 64 for 2-byte UTF-8,\n         * then switch to UTRIE2_DATA_BLOCK_LENGTH.\n         */\n        blockLength = 64;\n        blockCount = blockLength >> UTRIE2_SHIFT_2;\n        for (start = newStart; start < this.dataLength; ) {\n            /*\n             * start: index of first entry of current block\n             * newStart: index where the current block is to be moved\n             *           (right after current end of already-compacted data)\n             */\n            if (start === UNEWTRIE2_DATA_0800_OFFSET) {\n                blockLength = UTRIE2_DATA_BLOCK_LENGTH;\n                blockCount = 1;\n            }\n            /* skip blocks that are not used */\n            if (this.map[start >> UTRIE2_SHIFT_2] <= 0) {\n                /* advance start to the next block */\n                start += blockLength;\n                /* leave newStart with the previous block! */\n                continue;\n            }\n            /* search for an identical block */\n            movedStart = this.findSameDataBlock(newStart, start, blockLength);\n            if (movedStart >= 0) {\n                /* found an identical block, set the other block's index value for the current block */\n                for (i = blockCount, mapIndex = start >> UTRIE2_SHIFT_2; i > 0; --i) {\n                    this.map[mapIndex++] = movedStart;\n                    movedStart += UTRIE2_DATA_BLOCK_LENGTH;\n                }\n                /* advance start to the next block */\n                start += blockLength;\n                /* leave newStart with the previous block! */\n                continue;\n            }\n            /* see if the beginning of this block can be overlapped with the end of the previous block */\n            /* look for maximum overlap (modulo granularity) with the previous, adjacent block */\n            for (\n                overlap = blockLength - UTRIE2_DATA_GRANULARITY;\n                overlap > 0 && !equalInt(this.data, newStart - overlap, start, overlap);\n                overlap -= UTRIE2_DATA_GRANULARITY\n            ) {}\n            if (overlap > 0 || newStart < start) {\n                /* some overlap, or just move the whole block */\n                movedStart = newStart - overlap;\n                for (i = blockCount, mapIndex = start >> UTRIE2_SHIFT_2; i > 0; --i) {\n                    this.map[mapIndex++] = movedStart;\n                    movedStart += UTRIE2_DATA_BLOCK_LENGTH;\n                }\n                /* move the non-overlapping indexes to their new positions */\n                start += overlap;\n                for (i = blockLength - overlap; i > 0; --i) {\n                    this.data[newStart++] = this.data[start++];\n                }\n            } else {\n                /* no overlap && newStart==start */\n                for (i = blockCount, mapIndex = start >> UTRIE2_SHIFT_2; i > 0; --i) {\n                    this.map[mapIndex++] = start;\n                    start += UTRIE2_DATA_BLOCK_LENGTH;\n                }\n                newStart = start;\n            }\n        }\n        /* now adjust the index-2 table */\n        for (i = 0; i < this.index2Length; ++i) {\n            if (i === UNEWTRIE2_INDEX_GAP_OFFSET) {\n                /* Gap indexes are invalid (-1). Skip over the gap. */\n                i += UNEWTRIE2_INDEX_GAP_LENGTH;\n            }\n            this.index2[i] = this.map[this.index2[i] >> UTRIE2_SHIFT_2];\n        }\n        this.dataNullOffset = this.map[this.dataNullOffset >> UTRIE2_SHIFT_2];\n        /* ensure dataLength alignment */\n        while ((newStart & (UTRIE2_DATA_GRANULARITY - 1)) !== 0) {\n            this.data[newStart++] = this.initialValue;\n        }\n\n        this.dataLength = newStart;\n    }\n\n    findSameDataBlock(dataLength: int, otherBlock: int, blockLength: int): int {\n        let block = 0;\n        /* ensure that we do not even partially get past dataLength */\n        dataLength -= blockLength;\n        for (; block <= dataLength; block += UTRIE2_DATA_GRANULARITY) {\n            if (equalInt(this.data, block, otherBlock, blockLength)) {\n                return block;\n            }\n        }\n        return -1;\n    }\n\n    compactTrie() {\n        let highValue = this.get(0x10ffff);\n        /* find highStart and round it up */\n        let localHighStart = this.findHighStart(highValue);\n        localHighStart = (localHighStart + (UTRIE2_CP_PER_INDEX_1_ENTRY - 1)) & ~(UTRIE2_CP_PER_INDEX_1_ENTRY - 1);\n        if (localHighStart === 0x110000) {\n            highValue = this.errorValue;\n        }\n        /*\n         * Set trie->highStart only after utrie2_get32(trie, highStart).\n         * Otherwise utrie2_get32(trie, highStart) would try to read the highValue.\n         */\n        this.highStart = localHighStart;\n\n        if (this.highStart < 0x110000) {\n            /* Blank out [highStart..10ffff] to release associated data blocks. */\n            const suppHighStart = this.highStart <= 0x10000 ? 0x10000 : this.highStart;\n            this.setRange(suppHighStart, 0x10ffff, this.initialValue, true);\n        }\n        this.compactData();\n        if (this.highStart > 0x10000) {\n            this.compactIndex2();\n        }\n        /*\n         * Store the highValue in the data array and round up the dataLength.\n         * Must be done after compactData() because that assumes that dataLength\n         * is a multiple of UTRIE2_DATA_BLOCK_LENGTH.\n         */\n        this.data[this.dataLength++] = highValue;\n        while ((this.dataLength & (UTRIE2_DATA_GRANULARITY - 1)) !== 0) {\n            this.data[this.dataLength++] = this.initialValue;\n        }\n        this.isCompacted = true;\n    }\n\n    compactIndex2(): void {\n        let i, start, movedStart, overlap;\n        /* do not compact linear-BMP index-2 blocks */\n        let newStart = UTRIE2_INDEX_2_BMP_LENGTH;\n        for (start = 0, i = 0; start < newStart; start += UTRIE2_INDEX_2_BLOCK_LENGTH, ++i) {\n            this.map[i] = start;\n        }\n        /* Reduce the index table gap to what will be needed at runtime. */\n        newStart += UTRIE2_UTF8_2B_INDEX_2_LENGTH + ((this.highStart - 0x10000) >> UTRIE2_SHIFT_1);\n        for (start = UNEWTRIE2_INDEX_2_NULL_OFFSET; start < this.index2Length; ) {\n            /*\n             * start: index of first entry of current block\n             * newStart: index where the current block is to be moved\n             *           (right after current end of already-compacted data)\n             */\n            /* search for an identical block */\n            if ((movedStart = this.findSameIndex2Block(newStart, start)) >= 0) {\n                /* found an identical block, set the other block's index value for the current block */\n                this.map[start >> UTRIE2_SHIFT_1_2] = movedStart;\n                /* advance start to the next block */\n                start += UTRIE2_INDEX_2_BLOCK_LENGTH;\n                /* leave newStart with the previous block! */\n                continue;\n            }\n            /* see if the beginning of this block can be overlapped with the end of the previous block */\n            /* look for maximum overlap with the previous, adjacent block */\n            for (\n                overlap = UTRIE2_INDEX_2_BLOCK_LENGTH - 1;\n                overlap > 0 && !equalInt(this.index2, newStart - overlap, start, overlap);\n                --overlap\n            ) {}\n            if (overlap > 0 || newStart < start) {\n                /* some overlap, or just move the whole block */\n                this.map[start >> UTRIE2_SHIFT_1_2] = newStart - overlap;\n                /* move the non-overlapping indexes to their new positions */\n                start += overlap;\n                for (i = UTRIE2_INDEX_2_BLOCK_LENGTH - overlap; i > 0; --i) {\n                    this.index2[newStart++] = this.index2[start++];\n                }\n            } else {\n                /* no overlap && newStart==start */ this.map[start >> UTRIE2_SHIFT_1_2] = start;\n                start += UTRIE2_INDEX_2_BLOCK_LENGTH;\n                newStart = start;\n            }\n        }\n        /* now adjust the index-1 table */\n        for (i = 0; i < UNEWTRIE2_INDEX_1_LENGTH; ++i) {\n            this.index1[i] = this.map[this.index1[i] >> UTRIE2_SHIFT_1_2];\n        }\n        this.index2NullOffset = this.map[this.index2NullOffset >> UTRIE2_SHIFT_1_2];\n        /*\n         * Ensure data table alignment:\n         * Needs to be granularity-aligned for 16-bit trie\n         * (so that dataMove will be down-shiftable),\n         * and 2-aligned for uint32_t data.\n         */\n        while ((newStart & ((UTRIE2_DATA_GRANULARITY - 1) | 1)) !== 0) {\n            /* Arbitrary value: 0x3fffc not possible for real data. */\n            this.index2[newStart++] = 0x0000ffff << UTRIE2_INDEX_SHIFT;\n        }\n\n        this.index2Length = newStart;\n    }\n\n    findSameIndex2Block(index2Length: int, otherBlock: int): int {\n        /* ensure that we do not even partially get past index2Length */\n        index2Length -= UTRIE2_INDEX_2_BLOCK_LENGTH;\n        for (let block = 0; block <= index2Length; ++block) {\n            if (equalInt(this.index2, block, otherBlock, UTRIE2_INDEX_2_BLOCK_LENGTH)) {\n                return block;\n            }\n        }\n        return -1;\n    }\n\n    _set(c: int, forLSCP: boolean, value: int) {\n        if (this.isCompacted) {\n            throw new Error('Trie was already compacted');\n        }\n        const block = this.getDataBlock(c, forLSCP);\n        this.data[block + (c & UTRIE2_DATA_MASK)] = value;\n        return this;\n    }\n\n    writeBlock(block: int, value: int) {\n        const limit = block + UTRIE2_DATA_BLOCK_LENGTH;\n        while (block < limit) {\n            this.data[block++] = value;\n        }\n    }\n\n    isInNullBlock(c: int, forLSCP: boolean): boolean {\n        const i2 =\n            isHighSurrogate(c) && forLSCP\n                ? UTRIE2_LSCP_INDEX_2_OFFSET - (0xd800 >> UTRIE2_SHIFT_2) + (c >> UTRIE2_SHIFT_2)\n                : this.index1[c >> UTRIE2_SHIFT_1] + ((c >> UTRIE2_SHIFT_2) & UTRIE2_INDEX_2_MASK);\n        const block = this.index2[i2];\n        return block === this.dataNullOffset;\n    }\n\n    fillBlock(block: int, start: int, limit: int, value: int, initialValue: int, overwrite: boolean) {\n        const pLimit = block + limit;\n        if (overwrite) {\n            for (let i = block + start; i < pLimit; i++) {\n                this.data[i] = value;\n            }\n        } else {\n            for (let i = block + start; i < pLimit; i++) {\n                if (this.data[i] === initialValue) {\n                    this.data[i] = value;\n                }\n            }\n        }\n    }\n\n    setIndex2Entry(i2: int, block: int) {\n        ++this.map[block >> UTRIE2_SHIFT_2]; /* increment first, in case block==oldBlock! */\n        const oldBlock = this.index2[i2];\n        if (0 === --this.map[oldBlock >> UTRIE2_SHIFT_2]) {\n            this.releaseDataBlock(oldBlock);\n        }\n        this.index2[i2] = block;\n    }\n\n    releaseDataBlock(block: int) {\n        /* put this block at the front of the free-block chain */\n        this.map[block >> UTRIE2_SHIFT_2] = -this.firstFreeBlock;\n        this.firstFreeBlock = block;\n    }\n\n    getDataBlock(c: int, forLSCP: boolean): int {\n        let i2 = this.getIndex2Block(c, forLSCP);\n\n        i2 += (c >> UTRIE2_SHIFT_2) & UTRIE2_INDEX_2_MASK;\n        const oldBlock = this.index2[i2];\n        if (this.isWritableBlock(oldBlock)) {\n            return oldBlock;\n        }\n        /* allocate a new data block */\n        const newBlock = this.allocDataBlock(oldBlock);\n        this.setIndex2Entry(i2, newBlock);\n        return newBlock;\n    }\n\n    isWritableBlock(block: int): boolean {\n        return block !== this.dataNullOffset && 1 === this.map[block >> UTRIE2_SHIFT_2];\n    }\n\n    getIndex2Block(c: int, forLSCP: boolean): int {\n        if (c >= 0xd800 && c < 0xdc00 && forLSCP) {\n            return UTRIE2_LSCP_INDEX_2_OFFSET;\n        }\n        const i1 = c >> UTRIE2_SHIFT_1;\n        let i2 = this.index1[i1];\n        if (i2 === this.index2NullOffset) {\n            i2 = this.allocIndex2Block();\n            this.index1[i1] = i2;\n        }\n        return i2;\n    }\n\n    allocDataBlock(copyBlock: int): int {\n        let newBlock;\n\n        if (this.firstFreeBlock !== 0) {\n            /* get the first free block */\n            newBlock = this.firstFreeBlock;\n            this.firstFreeBlock = -this.map[newBlock >> UTRIE2_SHIFT_2];\n        } else {\n            /* get a new block from the high end */\n            newBlock = this.dataLength;\n            const newTop = newBlock + UTRIE2_DATA_BLOCK_LENGTH;\n            if (newTop > this.dataCapacity) {\n                let capacity: int;\n                /* out of memory in the data array */\n                if (this.dataCapacity < UNEWTRIE2_MEDIUM_DATA_LENGTH) {\n                    capacity = UNEWTRIE2_MEDIUM_DATA_LENGTH;\n                } else if (this.dataCapacity < UNEWTRIE2_MAX_DATA_LENGTH) {\n                    capacity = UNEWTRIE2_MAX_DATA_LENGTH;\n                } else {\n                    /*\n                     * Should never occur.\n                     * Either UNEWTRIE2_MAX_DATA_LENGTH is incorrect,\n                     * or the code writes more values than should be possible.\n                     */\n                    throw new Error('Internal error in Trie creation.');\n                }\n\n                const newData = new Uint32Array(capacity);\n                newData.set(this.data.subarray(0, this.dataLength));\n                this.data = newData;\n                this.dataCapacity = capacity;\n            }\n            this.dataLength = newTop;\n        }\n\n        this.data.set(this.data.subarray(copyBlock, copyBlock + UTRIE2_DATA_BLOCK_LENGTH), newBlock);\n        this.map[newBlock >> UTRIE2_SHIFT_2] = 0;\n        return newBlock;\n    }\n\n    allocIndex2Block(): int {\n        const newBlock = this.index2Length;\n        const newTop = newBlock + UTRIE2_INDEX_2_BLOCK_LENGTH;\n        if (newTop > this.index2.length) {\n            throw new Error('Internal error in Trie creation.');\n            /*\n             * Should never occur.\n             * Either UTRIE2_MAX_BUILD_TIME_INDEX_LENGTH is incorrect,\n             * or the code writes more values than should be possible.\n             */\n        }\n        this.index2Length = newTop;\n        this.index2.set(\n            this.index2.subarray(this.index2NullOffset, this.index2NullOffset + UTRIE2_INDEX_2_BLOCK_LENGTH),\n            newBlock\n        );\n        return newBlock;\n    }\n}\n\nexport const serializeBase64 = (trie: Trie): [string, number] => {\n    const index = trie.index;\n    const data = trie.data;\n    if (!(index instanceof Uint16Array) || !(data instanceof Uint16Array || data instanceof Uint32Array)) {\n        throw new Error('TrieBuilder serializer only support TypedArrays');\n    }\n    const headerLength = Uint32Array.BYTES_PER_ELEMENT * 6;\n    const bufferLength = headerLength + index.byteLength + data.byteLength;\n    const buffer = new ArrayBuffer(Math.ceil(bufferLength / 4) * 4);\n    const view32 = new Uint32Array(buffer);\n    const view16 = new Uint16Array(buffer);\n    view32[0] = trie.initialValue;\n    view32[1] = trie.errorValue;\n    view32[2] = trie.highStart;\n    view32[3] = trie.highValueIndex;\n    view32[4] = index.byteLength;\n    // $FlowFixMe\n    view32[5] = data.BYTES_PER_ELEMENT;\n\n    view16.set(index, headerLength / Uint16Array.BYTES_PER_ELEMENT);\n    if (data.BYTES_PER_ELEMENT === Uint16Array.BYTES_PER_ELEMENT) {\n        view16.set(data, (headerLength + index.byteLength) / Uint16Array.BYTES_PER_ELEMENT);\n    } else {\n        view32.set(data, Math.ceil((headerLength + index.byteLength) / Uint32Array.BYTES_PER_ELEMENT));\n    }\n\n    return [encode(new Uint8Array(buffer)), buffer.byteLength];\n};\n"], "names": ["chars", "lookup", "i"], "mappings": ";;;;;AAAA,IAAMA,OAAK,GAAG,kEAAkE,CAAC;AAEjF;AACA,IAAMC,QAAM,GAAG,OAAO,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAC5E,KAAK,IAAIC,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGF,OAAK,CAAC,MAAM,EAAEE,GAAC,EAAE,EAAE;IACnCD,QAAM,CAACD,OAAK,CAAC,UAAU,CAACE,GAAC,CAAC,CAAC,GAAGA,GAAC,CAAC;CACnC;AAEM,IAAM,MAAM,GAAG,UAAC,MAAc;IACjC,IAAI,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,EACnC,GAAG,GAAG,MAAM,CAAC,MAAM,EACnB,CAAC,EACD,CAAC,GAAG,CAAC,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,CAAC;IAEb,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QACnC,YAAY,EAAE,CAAC;QACf,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YACnC,YAAY,EAAE,CAAC;SAClB;KACJ;IAED,IAAM,MAAM,GACR,OAAO,WAAW,KAAK,WAAW;QAClC,OAAO,UAAU,KAAK,WAAW;QACjC,OAAO,UAAU,CAAC,SAAS,CAAC,KAAK,KAAK,WAAW;UAC3C,IAAI,WAAW,CAAC,YAAY,CAAC;UAC7B,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;IAClC,IAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAEtE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QACzB,QAAQ,GAAGD,QAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,QAAQ,GAAGA,QAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5C,QAAQ,GAAGA,QAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5C,QAAQ,GAAGA,QAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAE5C,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC;QAC/C,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtD,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,KAAK,QAAQ,GAAG,EAAE,CAAC,CAAC;KACxD;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEK,IAAM,eAAe,GAAG,UAAC,MAAgB;IAC5C,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC7B,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAChC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KAChD;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEK,IAAM,eAAe,GAAG,UAAC,MAAgB;IAC5C,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC7B,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAChC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KAChG;IACD,OAAO,KAAK,CAAC;AACjB,CAAC;;AC3DD;AACO,IAAM,cAAc,GAAG,CAAC,CAAC;AAEhC;AACO,IAAM,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;AAEpC;;;;;;AAMO,IAAM,kBAAkB,GAAG,CAAC,CAAC;AAEpC;;;;AAIO,IAAM,gBAAgB,GAAG,cAAc,GAAG,cAAc,CAAC;AAEhE;;;;;;AAMO,IAAM,0BAA0B,GAAG,OAAO,IAAI,cAAc,CAAC;AAEpE;AACO,IAAM,wBAAwB,GAAG,CAAC,IAAI,cAAc,CAAC;AAC5D;AACO,IAAM,gBAAgB,GAAG,wBAAwB,GAAG,CAAC,CAAC;AAEtD,IAAM,0BAA0B,GAAG,KAAK,IAAI,cAAc,CAAC;AAClE;AACO,IAAM,yBAAyB,GAAG,0BAA0B,GAAG,0BAA0B,CAAC;AACjG;;;;AAIO,IAAM,6BAA6B,GAAG,yBAAyB,CAAC;AAChE,IAAM,6BAA6B,GAAG,KAAK,IAAI,CAAC,CAAC;AACxD;;;;;;;;;;;;AAYO,IAAM,qBAAqB,GAAG,6BAA6B,GAAG,6BAA6B,CAAC;AAEnG;;;;AAIO,IAAM,iCAAiC,GAAG,OAAO,IAAI,cAAc,CAAC;AAE3E;AACO,IAAM,2BAA2B,GAAG,CAAC,IAAI,gBAAgB,CAAC;AACjE;AACO,IAAM,mBAAmB,GAAG,2BAA2B,GAAG,CAAC,CAAC;AAEnE,IAAM,OAAO,GAAG,UAAC,IAA4B,EAAE,KAAa,EAAE,GAAY;IACtE,IAAI,IAAI,CAAC,KAAK,EAAE;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACjC;IAED,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC;AAEF,IAAM,OAAO,GAAG,UAAC,IAA4B,EAAE,KAAa,EAAE,GAAY;IACtE,IAAI,IAAI,CAAC,KAAK,EAAE;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACjC;IAED,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC;IAEW,oBAAoB,GAAG,UAAC,MAAc,EAAE,WAAmB;IACpE,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAC9B,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IACzF,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IACzF,IAAM,YAAY,GAAG,EAAE,CAAC;IAExB,IAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,YAAY,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/D,IAAM,IAAI,GACN,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;UACT,OAAO,CAAC,MAAM,EAAE,CAAC,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UAC/C,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAErE,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7E,EAAE;;IAUE,cACI,YAAiB,EACjB,UAAe,EACf,SAAc,EACd,cAAmB,EACnB,KAA6B,EAC7B,IAA0C;QAE1C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB;;;;;;;IAQD,kBAAG,GAAH,UAAI,SAAiB;QACjB,IAAI,EAAE,CAAC;QACP,IAAI,SAAS,IAAI,CAAC,EAAE;YAChB,IAAI,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS,IAAI,OAAO,CAAC,EAAE;;;;gBAItE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,cAAc,CAAC,CAAC;gBAC7C,EAAE,GAAG,CAAC,EAAE,IAAI,kBAAkB,KAAK,SAAS,GAAG,gBAAgB,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACxB;YAED,IAAI,SAAS,IAAI,MAAM,EAAE;;;;;;;gBAOrB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,0BAA0B,IAAI,CAAC,SAAS,GAAG,MAAM,KAAK,cAAc,CAAC,CAAC,CAAC;gBACvF,EAAE,GAAG,CAAC,EAAE,IAAI,kBAAkB,KAAK,SAAS,GAAG,gBAAgB,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACxB;YAED,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE;;gBAE5B,EAAE,GAAG,qBAAqB,GAAG,iCAAiC,IAAI,SAAS,IAAI,cAAc,CAAC,CAAC;gBAC/F,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACpB,EAAE,IAAI,CAAC,SAAS,IAAI,cAAc,IAAI,mBAAmB,CAAC;gBAC1D,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACpB,EAAE,GAAG,CAAC,EAAE,IAAI,kBAAkB,KAAK,SAAS,GAAG,gBAAgB,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACxB;YACD,IAAI,SAAS,IAAI,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACzC;SACJ;;QAGD,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;IACL,WAAC;AAAD,CAAC;;;;;;;AC7KD,IAAM,KAAK,GAAG,kEAAkE,CAAC;AAEjF;AACA,IAAM,MAAM,GAAG,OAAO,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAC5E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACnC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACnC;IAEY,MAAM,GAAG,UAAC,WAAwB;IAC3C,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EACnC,CAAC,EACD,GAAG,GAAG,KAAK,CAAC,MAAM,EAClB,MAAM,GAAG,EAAE,CAAC;IAEhB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;KACtC;IAED,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;QACf,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;KACzD;SAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;QACtB,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;KAC1D;IAED,OAAO,MAAM,CAAC;AAClB;;ACPA;;;;;;AAMA;AAEA;AACA,IAAM,2BAA2B,GAAG,CAAC,IAAI,cAAc,CAAC;AAExD;AACA,IAAM,uBAAuB,GAAG,CAAC,IAAI,kBAAkB,CAAC;AACxD;AACA;;;;AAIA,IAAM,qBAAqB,GAAG,CAAC,CAAC;AAEhC,IAAM,yBAAyB,GAAG,QAAQ,IAAI,cAAc,CAAC;AAC7D;;;;AAIA;;;;;AAKA,IAAM,2BAA2B,GAAG,IAAI,CAAC;AACzC;AACA,IAAM,wBAAwB,GAAG,IAAI,CAAC;AACtC;AACA;;;;AAIA;;;;;;AAMA,IAAM,0BAA0B,GAAG,yBAAyB,CAAC;AAC7D,IAAM,0BAA0B,GAC5B,CAAC,6BAA6B,GAAG,yBAAyB,GAAG,mBAAmB,IAAI,CAAC,mBAAmB,CAAC;AAC7G;;;;;;;AAOA,IAAM,4BAA4B,GAC9B,CAAC,QAAQ,IAAI,cAAc;IAC3B,0BAA0B;IAC1B,0BAA0B;IAC1B,2BAA2B,CAAC;AAChC,IAAM,wBAAwB,GAAG,QAAQ,IAAI,cAAc,CAAC;AAC5D;;;;;AAKA,IAAM,yBAAyB,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;AAEjE;AACA,IAAM,6BAA6B,GAAG,CAAC,IAAI,EAAE,CAAC;AAC9C;AACA,IAAM,4BAA4B,GAAG,CAAC,IAAI,EAAE,CAAC;AAE7C;AACA,IAAM,6BAA6B,GAAG,0BAA0B,GAAG,0BAA0B,CAAC;AAC9F;AACA,IAAM,8BAA8B,GAAG,6BAA6B,GAAG,2BAA2B,CAAC;AACnG;;;;;AAKA,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D;AACA,IAAM,2BAA2B,GAAG,0BAA0B,GAAG,IAAI,CAAC;AACtE;;;;;;AAMA,IAAM,0BAA0B,GAAG,2BAA2B,GAAG,KAAK,CAAC;AAEvE;;;;;;AAMA,IAAM,uBAAuB,GAAG,MAAM,CAAC;AACvC;;;;;AAKA,IAAM,sBAAsB,GAAG,MAAM,IAAI,kBAAkB,CAAC;AAErD,IAAM,OAAO,GAAG,EAAE,CAAC;AACnB,IAAM,OAAO,GAAG,EAAE,CAAC;AAE1B,IAAM,eAAe,GAAG,UAAC,CAAM,IAAc,OAAA,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,GAAA,CAAC;AAExE,IAAM,QAAQ,GAAG,UAAC,CAAc,EAAE,CAAM,EAAE,CAAM,EAAE,MAAW;IACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;YACvB,OAAO,KAAK,CAAC;SAChB;KACJ;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;;IAkBE,qBAAY,YAAqB,EAAE,UAAmB;QAA1C,6BAAA,EAAA,gBAAqB;QAAE,2BAAA,EAAA,cAAmB;QAClD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,6BAA6B,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,GAAG,6BAA6B,CAAC;QAClD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,IAAI,CAAC,MAAM,GAAG,IAAI,WAAW,CAAC,wBAAwB,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,GAAG,IAAI,WAAW,CAAC,4BAA4B,CAAC,CAAC;;;;;;;;;;;;;;;;QAiB5D,IAAI,CAAC,GAAG,GAAG,IAAI,WAAW,CAAC,yBAAyB,IAAI,cAAc,CAAC,CAAC;;;;;;;QAOxE,IAAI,CAAC,EAAE,CAAC,CAAC;QACT,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;SAC/B;QACD,OAAO,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;SAC7B;QACD,KAAK,CAAC,GAAG,0BAA0B,EAAE,CAAC,GAAG,2BAA2B,EAAE,EAAE,CAAC,EAAE;YACvE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;SAC/B;QACD,IAAI,CAAC,cAAc,GAAG,0BAA0B,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,2BAA2B,CAAC;;QAE9C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,wBAAwB,EAAE;YAC7D,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACnB;;QAGD,OAAO,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,wBAAwB,EAAE;YACjD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACnB;;;;;;;QAQD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,cAAc,KAAK,IAAI,IAAI,cAAc,CAAC,GAAG,CAAC,GAAG,0BAA0B,CAAC;QACzG,CAAC,IAAI,wBAAwB,CAAC;QAC9B,OAAO,CAAC,GAAG,2BAA2B,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,wBAAwB,EAAE;YACxE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACnB;;;;;QAKD,KAAK,CAAC,GAAG,IAAI,IAAI,cAAc,EAAE,CAAC,GAAG,yBAAyB,EAAE,EAAE,CAAC,EAAE;YACjE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,0BAA0B,CAAC;SAC/C;;;;;QAKD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,0BAA0B,EAAE,EAAE,CAAC,EAAE;YAC7C,IAAI,CAAC,MAAM,CAAC,0BAA0B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACpD;;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,2BAA2B,EAAE,EAAE,CAAC,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,6BAA6B,GAAG,CAAC,CAAC,GAAG,0BAA0B,CAAC;SAC/E;QACD,IAAI,CAAC,gBAAgB,GAAG,6BAA6B,CAAC;QACtD,IAAI,CAAC,YAAY,GAAG,8BAA8B,CAAC;;QAEnD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iCAAiC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,2BAA2B,EAAE;YAC7F,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACtB;;QAED,OAAO,CAAC,GAAG,wBAAwB,EAAE,EAAE,CAAC,EAAE;YACtC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,6BAA6B,CAAC;SAClD;;;;;;QAMD,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,wBAAwB,EAAE;YACrD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;SAC7B;KACJ;;;;;;;IAQD,yBAAG,GAAH,UAAI,CAAM,EAAE,KAAU;QAClB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SAC1C;QACD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC;KACf;;;;;;;;;;;IAYD,8BAAQ,GAAR,UAAS,KAAU,EAAE,GAAQ,EAAE,KAAU,EAAE,SAA0B;QAA1B,0BAAA,EAAA,iBAA0B;;;;;;QAMjE,IAAI,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC;QAC7B,IAAI,KAAK,GAAG,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,QAAQ,IAAI,GAAG,GAAG,CAAC,IAAI,KAAK,GAAG,GAAG,EAAE;YAC3E,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE;YAC3C,OAAO,IAAI,CAAC;SACf;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SACjD;QACD,IAAI,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,gBAAgB,MAAM,CAAC,EAAE;;YAElC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACvC,IAAM,SAAS,GAAG,CAAC,KAAK,GAAG,wBAAwB,IAAI,CAAC,gBAAgB,CAAC;YACzE,IAAI,SAAS,IAAI,KAAK,EAAE;gBACpB,IAAI,CAAC,SAAS,CACV,KAAK,EACL,KAAK,GAAG,gBAAgB,EACxB,wBAAwB,EACxB,KAAK,EACL,IAAI,CAAC,YAAY,EACjB,SAAS,CACZ,CAAC;gBACF,KAAK,GAAG,SAAS,CAAC;aACrB;iBAAM;gBACH,IAAI,CAAC,SAAS,CACV,KAAK,EACL,KAAK,GAAG,gBAAgB,EACxB,KAAK,GAAG,gBAAgB,EACxB,KAAK,EACL,IAAI,CAAC,YAAY,EACjB,SAAS,CACZ,CAAC;gBACF,OAAO,IAAI,CAAC;aACf;SACJ;;QAED,IAAI,GAAG,KAAK,GAAG,gBAAgB,CAAC;;QAEhC,KAAK,IAAI,CAAC,gBAAgB,CAAC;;QAE3B,WAAW,GAAG,KAAK,KAAK,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QAErE,OAAO,KAAK,GAAG,KAAK,EAAE;YAClB,IAAI,EAAE,SAAA,CAAC;YACP,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;gBAChE,KAAK,IAAI,wBAAwB,CAAC;gBAClC,SAAS;aACZ;;YAED,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACtC,EAAE,IAAI,CAAC,KAAK,IAAI,cAAc,IAAI,mBAAmB,CAAC;YACtD,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;;gBAE7B,IAAI,SAAS,IAAI,KAAK,IAAI,0BAA0B,EAAE;;;;;;oBAMlD,cAAc,GAAG,IAAI,CAAC;iBACzB;qBAAM;;oBAEH,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,wBAAwB,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;iBAC3F;aACJ;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC,EAAE;;;;;;;;;;;;;;;;;gBAiBnF,cAAc,GAAG,IAAI,CAAC;aACzB;YACD,IAAI,cAAc,EAAE;gBAChB,IAAI,WAAW,IAAI,CAAC,EAAE;oBAClB,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;iBACxC;qBAAM;;oBAEH,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;oBAC7C,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;iBACvC;aACJ;YACD,KAAK,IAAI,wBAAwB,CAAC;SACrC;QACD,IAAI,IAAI,GAAG,CAAC,EAAE;;YAEV,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;SACvE;QACD,OAAO,IAAI,CAAC;KACf;;;;;;;IASD,yBAAG,GAAH,UAAI,SAAc;QACd,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,QAAQ,EAAE;YACvC,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;aAAM;YACH,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SACrC;KACJ;IAED,0BAAI,GAAJ,UAAK,CAAM,EAAE,QAAiB;QAC1B,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,QAAQ,CAAC,EAAE;YACnE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,uBAAuB,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,IAAI,QAAQ,EAAE;YACvC,EAAE,GAAG,0BAA0B,IAAI,MAAM,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,CAAC;SACxF;aAAM;YACH,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,cAAc,IAAI,mBAAmB,CAAC,CAAC;SACzF;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;KACpD;IAED,4BAAM,GAAN,UAAO,SAA4B;QAA5B,0BAAA,EAAA,mBAA4B;QAC/B,IAAI,CAAC,CAAC;QACN,IAAI,gBAAgB,CAAC;QACrB,IAAI,QAAQ,CAAC;;QAEb,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QAED,gBAAgB,GAAG,IAAI,CAAC,SAAS,IAAI,OAAO,GAAG,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC;QAEzF,IAAI,SAAS,KAAK,OAAO,EAAE;;YAEvB,QAAQ,GAAG,CAAC,CAAC;SAChB;aAAM;YACH,QAAQ,GAAG,CAAC,CAAC;SAChB;;QAED;;QAEI,gBAAgB,GAAG,uBAAuB;;YAE1C,QAAQ,GAAG,IAAI,CAAC,cAAc,GAAG,MAAM;;YAEvC,QAAQ,GAAG,0BAA0B,GAAG,MAAM;;YAE9C,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,sBAAsB,EACrD;YACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC9C;QAED,IAAM,KAAK,GAAG,IAAI,WAAW,CAAC,gBAAgB,CAAC,CAAC;;QAGhD,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,yBAAyB,EAAE,CAAC,EAAE,EAAE;YAC5C,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,KAAK,kBAAkB,CAAC;SACxE;;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;;YAE9B,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,QAAQ,GAAG,2BAA2B,CAAC;SAC7D;QACD,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;;YAEzB,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;SACxE;QAED,IAAI,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE;YAC1B,IAAM,YAAY,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,KAAK,cAAc,CAAC;YAClE,IAAM,YAAY,GAAG,yBAAyB,GAAG,6BAA6B,GAAG,YAAY,CAAC;;YAE9F,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBAC/B,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,iCAAiC,CAAC,CAAC;aACjG;;;;;YAMD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBACnD,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,KAAK,kBAAkB,CAAC;aACvF;SACJ;;QAGD,QAAQ,SAAS;YACb,KAAK,OAAO;;gBAER,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;oBAClC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC5B;gBAED,OAAO,IAAI,IAAI,CACX,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,SAAS,EACd,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,uBAAuB,EACpD,KAAK,EACL,MAAM,CACT,CAAC;YACN,KAAK,OAAO;;gBAER,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAChD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;oBAClC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC5B;gBACD,OAAO,IAAI,IAAI,CACX,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,SAAS,EACd,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,uBAAuB,EACpD,KAAK,EACL,MAAM,CACT,CAAC;YACN;gBACI,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACzD;KACJ;;;;;IAMD,mCAAa,GAAb,UAAc,SAAc;QACxB,IAAI,KAAK,CAAC;QACV,IAAI,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC;;QAElD,IAAI,SAAS,KAAK,IAAI,CAAC,YAAY,EAAE;YACjC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACpC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;SACnC;aAAM;YACH,WAAW,GAAG,CAAC,CAAC,CAAC;YACjB,SAAS,GAAG,CAAC,CAAC,CAAC;SAClB;QACD,IAAI,IAAI,GAAG,QAAQ,CAAC;;QAEpB,IAAI,EAAE,GAAG,wBAAwB,CAAC;QAClC,IAAI,CAAC,GAAG,IAAI,CAAC;QACb,OAAO,CAAC,GAAG,CAAC,EAAE;YACV,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5B,IAAI,OAAO,KAAK,WAAW,EAAE;;gBAEzB,CAAC,IAAI,2BAA2B,CAAC;gBACjC,SAAS;aACZ;YACD,WAAW,GAAG,OAAO,CAAC;YACtB,IAAI,OAAO,KAAK,IAAI,CAAC,gBAAgB,EAAE;;gBAEnC,IAAI,SAAS,KAAK,IAAI,CAAC,YAAY,EAAE;oBACjC,OAAO,CAAC,CAAC;iBACZ;gBACD,CAAC,IAAI,2BAA2B,CAAC;aACpC;iBAAM;;gBAEH,KAAK,EAAE,GAAG,2BAA2B,EAAE,EAAE,GAAG,CAAC,GAAI;oBAC7C,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC,CAAC;oBACpC,IAAI,KAAK,KAAK,SAAS,EAAE;;wBAErB,CAAC,IAAI,wBAAwB,CAAC;wBAC9B,SAAS;qBACZ;oBACD,SAAS,GAAG,KAAK,CAAC;oBAClB,IAAI,KAAK,KAAK,IAAI,CAAC,cAAc,EAAE;;wBAE/B,IAAI,SAAS,KAAK,IAAI,CAAC,YAAY,EAAE;4BACjC,OAAO,CAAC,CAAC;yBACZ;wBACD,CAAC,IAAI,wBAAwB,CAAC;qBACjC;yBAAM;wBACH,KAAK,CAAC,GAAG,wBAAwB,EAAE,CAAC,GAAG,CAAC,GAAI;4BACxC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;4BAC/B,IAAI,KAAK,KAAK,SAAS,EAAE;gCACrB,OAAO,CAAC,CAAC;6BACZ;4BACD,EAAE,CAAC,CAAC;yBACP;qBACJ;iBACJ;aACJ;SACJ;;QAED,OAAO,CAAC,CAAC;KACZ;;;;;;;;;;;;;IAcD,iCAAW,GAAX;QACI,IAAI,KAAK,EAAE,UAAU,CAAC;QACtB,IAAI,WAAW,EAAE,OAAO,CAAC;QACzB,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC;;QAE5B,IAAI,QAAQ,GAAG,wBAAwB,CAAC;QACxC,KAAK,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI,wBAAwB,EAAE,EAAE,CAAC,EAAE;YAC7E,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;SACvB;;;;;QAKD,WAAW,GAAG,EAAE,CAAC;QACjB,UAAU,GAAG,WAAW,IAAI,cAAc,CAAC;QAC3C,KAAK,KAAK,GAAG,QAAQ,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,GAAI;;;;;;YAM9C,IAAI,KAAK,KAAK,0BAA0B,EAAE;gBACtC,WAAW,GAAG,wBAAwB,CAAC;gBACvC,UAAU,GAAG,CAAC,CAAC;aAClB;;YAED,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;;gBAExC,KAAK,IAAI,WAAW,CAAC;;gBAErB,SAAS;aACZ;;YAED,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;YAClE,IAAI,UAAU,IAAI,CAAC,EAAE;;gBAEjB,KAAK,CAAC,GAAG,UAAU,EAAE,QAAQ,GAAG,KAAK,IAAI,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;oBACjE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,UAAU,CAAC;oBAClC,UAAU,IAAI,wBAAwB,CAAC;iBAC1C;;gBAED,KAAK,IAAI,WAAW,CAAC;;gBAErB,SAAS;aACZ;;;YAGD,KACI,OAAO,GAAG,WAAW,GAAG,uBAAuB,EAC/C,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,GAAG,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,EACvE,OAAO,IAAI,uBAAuB,EACpC,GAAE;YACJ,IAAI,OAAO,GAAG,CAAC,IAAI,QAAQ,GAAG,KAAK,EAAE;;gBAEjC,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;gBAChC,KAAK,CAAC,GAAG,UAAU,EAAE,QAAQ,GAAG,KAAK,IAAI,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;oBACjE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,UAAU,CAAC;oBAClC,UAAU,IAAI,wBAAwB,CAAC;iBAC1C;;gBAED,KAAK,IAAI,OAAO,CAAC;gBACjB,KAAK,CAAC,GAAG,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;oBACxC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;iBAC9C;aACJ;iBAAM;;gBAEH,KAAK,CAAC,GAAG,UAAU,EAAE,QAAQ,GAAG,KAAK,IAAI,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;oBACjE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC;oBAC7B,KAAK,IAAI,wBAAwB,CAAC;iBACrC;gBACD,QAAQ,GAAG,KAAK,CAAC;aACpB;SACJ;;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE;YACpC,IAAI,CAAC,KAAK,0BAA0B,EAAE;;gBAElC,CAAC,IAAI,0BAA0B,CAAC;aACnC;YACD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,CAAC;;QAEtE,OAAO,CAAC,QAAQ,IAAI,uBAAuB,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE;YACrD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;SAC7C;QAED,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;KAC9B;IAED,uCAAiB,GAAjB,UAAkB,UAAe,EAAE,UAAe,EAAE,WAAgB;QAChE,IAAI,KAAK,GAAG,CAAC,CAAC;;QAEd,UAAU,IAAI,WAAW,CAAC;QAC1B,OAAO,KAAK,IAAI,UAAU,EAAE,KAAK,IAAI,uBAAuB,EAAE;YAC1D,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,CAAC,EAAE;gBACrD,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO,CAAC,CAAC,CAAC;KACb;IAED,iCAAW,GAAX;QACI,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;;QAEnC,IAAI,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACnD,cAAc,GAAG,CAAC,cAAc,IAAI,2BAA2B,GAAG,CAAC,CAAC,IAAI,EAAE,2BAA2B,GAAG,CAAC,CAAC,CAAC;QAC3G,IAAI,cAAc,KAAK,QAAQ,EAAE;YAC7B,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;SAC/B;;;;;QAKD,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC;QAEhC,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,EAAE;;YAE3B,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,IAAI,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;YAC3E,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SACnE;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE;YAC1B,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;;;;;;QAMD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,SAAS,CAAC;QACzC,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,uBAAuB,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE;YAC5D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;SACpD;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;IAED,mCAAa,GAAb;QACI,IAAI,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC;;QAElC,IAAI,QAAQ,GAAG,yBAAyB,CAAC;QACzC,KAAK,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI,2BAA2B,EAAE,EAAE,CAAC,EAAE;YAChF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;SACvB;;QAED,QAAQ,IAAI,6BAA6B,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,KAAK,cAAc,CAAC,CAAC;QAC3F,KAAK,KAAK,GAAG,6BAA6B,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,GAAI;;;;;;;YAOrE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;;gBAE/D,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,gBAAgB,CAAC,GAAG,UAAU,CAAC;;gBAEjD,KAAK,IAAI,2BAA2B,CAAC;;gBAErC,SAAS;aACZ;;;YAGD,KACI,OAAO,GAAG,2BAA2B,GAAG,CAAC,EACzC,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,EACzE,EAAE,OAAO,EACX,GAAE;YACJ,IAAI,OAAO,GAAG,CAAC,IAAI,QAAQ,GAAG,KAAK,EAAE;;gBAEjC,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,gBAAgB,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;;gBAEzD,KAAK,IAAI,OAAO,CAAC;gBACjB,KAAK,CAAC,GAAG,2BAA2B,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;oBACxD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;iBAClD;aACJ;iBAAM;oDACiC,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,gBAAgB,CAAC,GAAG,KAAK,CAAC;gBAChF,KAAK,IAAI,2BAA2B,CAAC;gBACrC,QAAQ,GAAG,KAAK,CAAC;aACpB;SACJ;;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,wBAAwB,EAAE,EAAE,CAAC,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,CAAC;;;;;;;QAO5E,OAAO,CAAC,QAAQ,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE;;YAE3D,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,UAAU,IAAI,kBAAkB,CAAC;SAC9D;QAED,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;KAChC;IAED,yCAAmB,GAAnB,UAAoB,YAAiB,EAAE,UAAe;;QAElD,YAAY,IAAI,2BAA2B,CAAC;QAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,YAAY,EAAE,EAAE,KAAK,EAAE;YAChD,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,2BAA2B,CAAC,EAAE;gBACvE,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO,CAAC,CAAC,CAAC;KACb;IAED,0BAAI,GAAJ,UAAK,CAAM,EAAE,OAAgB,EAAE,KAAU;QACrC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SACjD;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,KAAK,CAAC;QAClD,OAAO,IAAI,CAAC;KACf;IAED,gCAAU,GAAV,UAAW,KAAU,EAAE,KAAU;QAC7B,IAAM,KAAK,GAAG,KAAK,GAAG,wBAAwB,CAAC;QAC/C,OAAO,KAAK,GAAG,KAAK,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;SAC9B;KACJ;IAED,mCAAa,GAAb,UAAc,CAAM,EAAE,OAAgB;QAClC,IAAM,EAAE,GACJ,eAAe,CAAC,CAAC,CAAC,IAAI,OAAO;cACvB,0BAA0B,IAAI,MAAM,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC;cAC/E,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,cAAc,IAAI,mBAAmB,CAAC,CAAC;QAC3F,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC9B,OAAO,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC;KACxC;IAED,+BAAS,GAAT,UAAU,KAAU,EAAE,KAAU,EAAE,KAAU,EAAE,KAAU,EAAE,YAAiB,EAAE,SAAkB;QAC3F,IAAM,MAAM,GAAG,KAAK,GAAG,KAAK,CAAC;QAC7B,IAAI,SAAS,EAAE;YACX,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;aACxB;SACJ;aAAM;YACH,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;oBAC/B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;iBACxB;aACJ;SACJ;KACJ;IAED,oCAAc,GAAd,UAAe,EAAO,EAAE,KAAU;QAC9B,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,cAAc,CAAC,CAAC;QACpC,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,cAAc,CAAC,EAAE;YAC9C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;SACnC;QACD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;KAC3B;IAED,sCAAgB,GAAhB,UAAiB,KAAU;;QAEvB,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC;QACzD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;KAC/B;IAED,kCAAY,GAAZ,UAAa,CAAM,EAAE,OAAgB;QACjC,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAEzC,EAAE,IAAI,CAAC,CAAC,IAAI,cAAc,IAAI,mBAAmB,CAAC;QAClD,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE;YAChC,OAAO,QAAQ,CAAC;SACnB;;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAClC,OAAO,QAAQ,CAAC;KACnB;IAED,qCAAe,GAAf,UAAgB,KAAU;QACtB,OAAO,KAAK,KAAK,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,cAAc,CAAC,CAAC;KACnF;IAED,oCAAc,GAAd,UAAe,CAAM,EAAE,OAAgB;QACnC,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,IAAI,OAAO,EAAE;YACtC,OAAO,0BAA0B,CAAC;SACrC;QACD,IAAM,EAAE,GAAG,CAAC,IAAI,cAAc,CAAC;QAC/B,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACzB,IAAI,EAAE,KAAK,IAAI,CAAC,gBAAgB,EAAE;YAC9B,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;SACxB;QACD,OAAO,EAAE,CAAC;KACb;IAED,oCAAc,GAAd,UAAe,SAAc;QACzB,IAAI,QAAQ,CAAC;QAEb,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,EAAE;;YAE3B,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC;YAC/B,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,cAAc,CAAC,CAAC;SAC/D;aAAM;;YAEH,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;YAC3B,IAAM,MAAM,GAAG,QAAQ,GAAG,wBAAwB,CAAC;YACnD,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE;gBAC5B,IAAI,QAAQ,SAAK,CAAC;;gBAElB,IAAI,IAAI,CAAC,YAAY,GAAG,4BAA4B,EAAE;oBAClD,QAAQ,GAAG,4BAA4B,CAAC;iBAC3C;qBAAM,IAAI,IAAI,CAAC,YAAY,GAAG,yBAAyB,EAAE;oBACtD,QAAQ,GAAG,yBAAyB,CAAC;iBACxC;qBAAM;;;;;;oBAMH,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;iBACvD;gBAED,IAAM,OAAO,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;gBACpB,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;aAChC;YACD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;SAC5B;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,GAAG,wBAAwB,CAAC,EAAE,QAAQ,CAAC,CAAC;QAC7F,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO,QAAQ,CAAC;KACnB;IAED,sCAAgB,GAAhB;QACI,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;QACnC,IAAM,MAAM,GAAG,QAAQ,GAAG,2BAA2B,CAAC;QACtD,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;;;;;;SAMvD;QACD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CACX,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,GAAG,2BAA2B,CAAC,EAChG,QAAQ,CACX,CAAC;QACF,OAAO,QAAQ,CAAC;KACnB;IACL,kBAAC;AAAD,CAAC,IAAA;IAEY,eAAe,GAAG,UAAC,IAAU;IACtC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACvB,IAAI,EAAE,KAAK,YAAY,WAAW,CAAC,IAAI,EAAE,IAAI,YAAY,WAAW,IAAI,IAAI,YAAY,WAAW,CAAC,EAAE;QAClG,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;KACtE;IACD,IAAM,YAAY,GAAG,WAAW,CAAC,iBAAiB,GAAG,CAAC,CAAC;IACvD,IAAM,YAAY,GAAG,YAAY,GAAG,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACvE,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChE,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IACvC,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IACvC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;IAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;IAC5B,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;IAC3B,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;IAChC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;;IAE7B,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;IAEnC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;IAChE,IAAI,IAAI,CAAC,iBAAiB,KAAK,WAAW,CAAC,iBAAiB,EAAE;QAC1D,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,YAAY,GAAG,KAAK,CAAC,UAAU,IAAI,WAAW,CAAC,iBAAiB,CAAC,CAAC;KACvF;SAAM;QACH,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,UAAU,IAAI,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC;KAClG;IAED,OAAO,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AAC/D;;;;"}