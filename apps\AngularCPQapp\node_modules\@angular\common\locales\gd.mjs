/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1 || n === 11)
        return 1;
    if (n === 2 || n === 12)
        return 2;
    if (n === Math.floor(n) && (n >= 3 && n <= 10 || n >= 13 && n <= 19))
        return 3;
    return 5;
}
export default ["gd", [["m", "f"], u, u], u, [["D", "L", "M", "C", "A", "H", "S"], ["DiD", "DiL", "DiM", "DiC", "Dia", "Dih", "DiS"], ["DiDòmhn<PERSON>h", "DiLuain", "DiMàirt", "DiCiadain", "Diar<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>athairne"], ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "hA", "Sa"]], u, [["F", "G", "<PERSON>", "<PERSON>", "<PERSON>", "Ò", "I", "L", "S", "D", "S", "<PERSON>"], ["<PERSON>aoi", "<PERSON>r", "M<PERSON>rt", "Gibl", "<PERSON>èit", "Ògmh", "Iuch", "<PERSON>ùna", "<PERSON>t", "Dàmh", "<PERSON>h", "<PERSON>ùbh"], ["dhen Fhaoilleach", "dhen <PERSON>hearran", "dhen Mhàrt", "dhen Ghiblean", "dhen Chèitean", "dhen Ògmhios", "dhen Iuchar", "dhen Lùnastal", "dhen t-Sultain", "dhen Dàmhair", "dhen t-Samhain", "dhen Dùbhlachd"]], [["F", "G", "M", "G", "C", "Ò", "I", "L", "S", "D", "S", "D"], ["Faoi", "Gearr", "Màrt", "Gibl", "Cèit", "Ògmh", "Iuch", "Lùna", "Sult", "Dàmh", "Samh", "Dùbh"], ["Am Faoilleach", "An Gearran", "Am Màrt", "An Giblean", "An Cèitean", "An t-Ògmhios", "An t-Iuchar", "An Lùnastal", "An t-Sultain", "An Dàmhair", "An t-Samhain", "An Dùbhlachd"]], [["R", "A"], ["RC", "AD"], ["Ro Chrìosta", "An dèidh Chrìosta"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d'mh' MMMM y", "EEEE, d'mh' MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "GBP", "£", "Punnd Sasannach", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "RON": [u, "leu"], "RUR": [u, "р."], "THB": ["฿"], "TWD": ["NT$"], "XXX": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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