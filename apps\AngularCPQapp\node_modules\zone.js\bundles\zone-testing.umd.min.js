"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},__assign.apply(this,arguments)},__spreadArray=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,i=0,s=t.length;i<s;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */
!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e,t=globalThis;function n(e){return(t.__Zone_symbol_prefix||"__zone_symbol__")+e}var r,i="undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global,s=function(){function e(e,t,r){this.finishCallback=e,this.failCallback=t,this._pendingMicroTasks=!1,this._pendingMacroTasks=!1,this._alreadyErrored=!1,this._isSync=!1,this._existingFinishTimer=null,this.entryFunction=null,this.runZone=Zone.current,this.unresolvedChainedPromiseCount=0,this.supportWaitUnresolvedChainedPromise=!1,this.name="asyncTestZone for "+r,this.properties={AsyncTestZoneSpec:this},this.supportWaitUnresolvedChainedPromise=!0===i[n("supportWaitUnResolvedChainedPromise")]}return Object.defineProperty(e,"symbolParentUnresolved",{get:function(){return n("parentUnresolved")},enumerable:!1,configurable:!0}),e.prototype.isUnresolvedChainedPromisePending=function(){return this.unresolvedChainedPromiseCount>0},e.prototype._finishCallbackIfDone=function(){var e=this;null!==this._existingFinishTimer&&(clearTimeout(this._existingFinishTimer),this._existingFinishTimer=null),this._pendingMicroTasks||this._pendingMacroTasks||this.supportWaitUnresolvedChainedPromise&&this.isUnresolvedChainedPromisePending()||this.runZone.run((function(){e._existingFinishTimer=setTimeout((function(){e._alreadyErrored||e._pendingMicroTasks||e._pendingMacroTasks||e.finishCallback()}),0)}))},e.prototype.patchPromiseForTest=function(){if(this.supportWaitUnresolvedChainedPromise){var e=Promise[Zone.__symbol__("patchPromiseForTest")];e&&e()}},e.prototype.unPatchPromiseForTest=function(){if(this.supportWaitUnresolvedChainedPromise){var e=Promise[Zone.__symbol__("unPatchPromiseForTest")];e&&e()}},e.prototype.onScheduleTask=function(t,n,r,i){return"eventTask"!==i.type&&(this._isSync=!1),"microTask"===i.type&&i.data&&i.data instanceof Promise&&!0===i.data[e.symbolParentUnresolved]&&this.unresolvedChainedPromiseCount--,t.scheduleTask(r,i)},e.prototype.onInvokeTask=function(e,t,n,r,i,s){return"eventTask"!==r.type&&(this._isSync=!1),e.invokeTask(n,r,i,s)},e.prototype.onCancelTask=function(e,t,n,r){return"eventTask"!==r.type&&(this._isSync=!1),e.cancelTask(n,r)},e.prototype.onInvoke=function(e,t,n,r,i,s,o){this.entryFunction||(this.entryFunction=r);try{return this._isSync=!0,e.invoke(n,r,i,s,o)}finally{this._isSync&&this.entryFunction===r&&this._finishCallbackIfDone()}},e.prototype.onHandleError=function(e,t,n,r){return e.handleError(n,r)&&(this.failCallback(r),this._alreadyErrored=!0),!1},e.prototype.onHasTask=function(e,t,n,r){e.hasTask(n,r),t===n&&("microTask"==r.change?(this._pendingMicroTasks=r.microTask,this._finishCallbackIfDone()):"macroTask"==r.change&&(this._pendingMacroTasks=r.macroTask,this._finishCallbackIfDone()))},e}(),o="object"==typeof window&&window||"object"==typeof self&&self||globalThis.global,a=o.Date;function c(){if(0===arguments.length){var e=new a;return e.setTime(c.now()),e}var t=Array.prototype.slice.call(arguments);return new(a.bind.apply(a,__spreadArray([void 0],t,!1)))}c.now=function(){var e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():a.now.apply(this,arguments)},c.UTC=a.UTC,c.parse=a.parse;var u=function(){},l=function(){function t(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=a.now(),this._currentTickRequeuePeriodicEntries=[]}return t.getNextId=function(){var t=r.nativeSetTimeout.call(o,u,0);return r.nativeClearTimeout.call(o,t),"number"==typeof t?t:e.nextNodeJSId++},t.prototype.getCurrentTickTime=function(){return this._currentTickTime},t.prototype.getFakeSystemTime=function(){return this._currentFakeBaseSystemTime+this._currentTickTime},t.prototype.setFakeBaseSystemTime=function(e){this._currentFakeBaseSystemTime=e},t.prototype.getRealSystemTime=function(){return a.now()},t.prototype.scheduleFunction=function(t,n,r){var i=(r=__assign({args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1},r)).id<0?e.nextId:r.id;e.nextId=e.getNextId();var s={endTime:this._currentTickTime+n,id:i,func:t,args:r.args,delay:n,isPeriodic:r.isPeriodic,isRequestAnimationFrame:r.isRequestAnimationFrame};r.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(s);for(var o=0;o<this._schedulerQueue.length&&!(s.endTime<this._schedulerQueue[o].endTime);o++);return this._schedulerQueue.splice(o,0,s),i},t.prototype.removeScheduledFunctionWithId=function(e){for(var t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}},t.prototype.removeAll=function(){this._schedulerQueue=[]},t.prototype.getTimerCount=function(){return this._schedulerQueue.length},t.prototype.tickToNext=function(e,t,n){void 0===e&&(e=1),this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,n)},t.prototype.tick=function(e,t,n){void 0===e&&(e=0);var r=this._currentTickTime+e,i=0,s=(n=Object.assign({processNewMacroTasksSynchronously:!0},n)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===s.length&&t)t(e);else{for(;s.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(r<s[0].endTime));){var a=s.shift();if(!n.processNewMacroTasksSynchronously){var c=this._schedulerQueue.indexOf(a);c>=0&&this._schedulerQueue.splice(c,1)}if(i=this._currentTickTime,this._currentTickTime=a.endTime,t&&t(this._currentTickTime-i),!a.func.apply(o,a.isRequestAnimationFrame?[this._currentTickTime]:a.args))break;n.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((function(e){for(var t=0;t<s.length&&!(e.endTime<s[t].endTime);t++);s.splice(t,0,e)}))}i=this._currentTickTime,this._currentTickTime=r,t&&t(this._currentTickTime-i)}},t.prototype.flushOnlyPendingTimers=function(e){if(0===this._schedulerQueue.length)return 0;var t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t},t.prototype.flush=function(e,t,n){return void 0===e&&(e=20),void 0===t&&(t=!1),t?this.flushPeriodic(n):this.flushNonPeriodic(e,n)},t.prototype.flushPeriodic=function(e){if(0===this._schedulerQueue.length)return 0;var t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t},t.prototype.flushNonPeriodic=function(e,t){for(var n=this._currentTickTime,r=0,i=0;this._schedulerQueue.length>0;){if(++i>e)throw new Error("flush failed after reaching the limit of "+e+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((function(e){return!e.isPeriodic&&!e.isRequestAnimationFrame})).length)break;var s=this._schedulerQueue.shift();if(r=this._currentTickTime,this._currentTickTime=s.endTime,t&&t(this._currentTickTime-r),!s.func.apply(o,s.args))break}return this._currentTickTime-n},t}();(e=l).nextNodeJSId=1,e.nextId=-1;var h=function(){function e(e,t,n){void 0===t&&(t=!1),this.trackPendingRequestAnimationFrame=t,this.macroTaskOptions=n,this._scheduler=new l,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.name="fakeAsyncTestZone for "+e,this.macroTaskOptions||(this.macroTaskOptions=o[Zone.__symbol__("FakeAsyncTestMacroTask")])}return e.assertInZone=function(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")},e.prototype._fnAndFlush=function(e,t){var n=this;return function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];return e.apply(o,r),null===n._lastError?(null!=t.onSuccess&&t.onSuccess.apply(o),n.flushMicrotasks()):null!=t.onError&&t.onError.apply(o),null===n._lastError}},e._removeTimer=function(e,t){var n=e.indexOf(t);n>-1&&e.splice(n,1)},e.prototype._dequeueTimer=function(t){var n=this;return function(){e._removeTimer(n.pendingTimers,t)}},e.prototype._requeuePeriodicTimer=function(e,t,n,r){var i=this;return function(){-1!==i.pendingPeriodicTimers.indexOf(r)&&i._scheduler.scheduleFunction(e,t,{args:n,isPeriodic:!0,id:r,isRequeuePeriodic:!0})}},e.prototype._dequeuePeriodicTimer=function(t){var n=this;return function(){e._removeTimer(n.pendingPeriodicTimers,t)}},e.prototype._setTimeout=function(e,t,n,r){void 0===r&&(r=!0);var i=this._dequeueTimer(l.nextId),s=this._fnAndFlush(e,{onSuccess:i,onError:i}),o=this._scheduler.scheduleFunction(s,t,{args:n,isRequestAnimationFrame:!r});return r&&this.pendingTimers.push(o),o},e.prototype._clearTimeout=function(t){e._removeTimer(this.pendingTimers,t),this._scheduler.removeScheduledFunctionWithId(t)},e.prototype._setInterval=function(e,t,n){var r=l.nextId,i={onSuccess:null,onError:this._dequeuePeriodicTimer(r)},s=this._fnAndFlush(e,i);return i.onSuccess=this._requeuePeriodicTimer(s,t,n,r),this._scheduler.scheduleFunction(s,t,{args:n,isPeriodic:!0}),this.pendingPeriodicTimers.push(r),r},e.prototype._clearInterval=function(t){e._removeTimer(this.pendingPeriodicTimers,t),this._scheduler.removeScheduledFunctionWithId(t)},e.prototype._resetLastErrorAndThrow=function(){var e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e},e.prototype.getCurrentTickTime=function(){return this._scheduler.getCurrentTickTime()},e.prototype.getFakeSystemTime=function(){return this._scheduler.getFakeSystemTime()},e.prototype.setFakeBaseSystemTime=function(e){this._scheduler.setFakeBaseSystemTime(e)},e.prototype.getRealSystemTime=function(){return this._scheduler.getRealSystemTime()},e.patchDate=function(){o[Zone.__symbol__("disableDatePatching")]||o.Date!==c&&(o.Date=c,c.prototype=a.prototype,e.checkTimerPatch())},e.resetDate=function(){o.Date===c&&(o.Date=a)},e.checkTimerPatch=function(){if(!r)throw new Error("Expected timers to have been patched.");o.setTimeout!==r.setTimeout&&(o.setTimeout=r.setTimeout,o.clearTimeout=r.clearTimeout),o.setInterval!==r.setInterval&&(o.setInterval=r.setInterval,o.clearInterval=r.clearInterval)},e.prototype.lockDatePatch=function(){this.patchDateLocked=!0,e.patchDate()},e.prototype.unlockDatePatch=function(){this.patchDateLocked=!1,e.resetDate()},e.prototype.tickToNext=function(t,n,r){void 0===t&&(t=1),void 0===r&&(r={processNewMacroTasksSynchronously:!0}),t<=0||(e.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(t,n,r),null!==this._lastError&&this._resetLastErrorAndThrow())},e.prototype.tick=function(t,n,r){void 0===t&&(t=0),void 0===r&&(r={processNewMacroTasksSynchronously:!0}),e.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(t,n,r),null!==this._lastError&&this._resetLastErrorAndThrow()},e.prototype.flushMicrotasks=function(){var t=this;for(e.assertInZone();this._microtasks.length>0;){var n=this._microtasks.shift();n.func.apply(n.target,n.args)}(null!==t._lastError||t._uncaughtPromiseErrors.length)&&t._resetLastErrorAndThrow()},e.prototype.flush=function(t,n,r){e.assertInZone(),this.flushMicrotasks();var i=this._scheduler.flush(t,n,r);return null!==this._lastError&&this._resetLastErrorAndThrow(),i},e.prototype.flushOnlyPendingTimers=function(t){e.assertInZone(),this.flushMicrotasks();var n=this._scheduler.flushOnlyPendingTimers(t);return null!==this._lastError&&this._resetLastErrorAndThrow(),n},e.prototype.removeAllTimers=function(){e.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]},e.prototype.getTimerCount=function(){return this._scheduler.getTimerCount()+this._microtasks.length},e.prototype.onScheduleTask=function(e,t,n,r){switch(r.type){case"microTask":var i=r.data&&r.data.args,s=void 0;if(i){var o=r.data.cbIdx;"number"==typeof i.length&&i.length>o+1&&(s=Array.prototype.slice.call(i,o+1))}this._microtasks.push({func:r.invoke,args:s,target:r.data&&r.data.target});break;case"macroTask":switch(r.source){case"setTimeout":r.data.handleId=this._setTimeout(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"setImmediate":r.data.handleId=this._setTimeout(r.invoke,0,Array.prototype.slice.call(r.data.args,1));break;case"setInterval":r.data.handleId=this._setInterval(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+r.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":r.data.handleId=this._setTimeout(r.invoke,16,r.data.args,this.trackPendingRequestAnimationFrame);break;default:var a=this.findMacroTaskOption(r);if(a){var c=r.data&&r.data.args,u=c&&c.length>1?c[1]:0,l=a.callbackArgs?a.callbackArgs:c;a.isPeriodic?(r.data.handleId=this._setInterval(r.invoke,u,l),r.data.isPeriodic=!0):r.data.handleId=this._setTimeout(r.invoke,u,l);break}throw new Error("Unknown macroTask scheduled in fake async test: "+r.source)}break;case"eventTask":r=e.scheduleTask(n,r)}return r},e.prototype.onCancelTask=function(e,t,n,r){switch(r.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(r.data.handleId);case"setInterval":return this._clearInterval(r.data.handleId);default:var i=this.findMacroTaskOption(r);if(i){var s=r.data.handleId;return i.isPeriodic?this._clearInterval(s):this._clearTimeout(s)}return e.cancelTask(n,r)}},e.prototype.onInvoke=function(t,n,r,i,s,o,a){try{return e.patchDate(),t.invoke(r,i,s,o,a)}finally{this.patchDateLocked||e.resetDate()}},e.prototype.findMacroTaskOption=function(e){if(!this.macroTaskOptions)return null;for(var t=0;t<this.macroTaskOptions.length;t++){var n=this.macroTaskOptions[t];if(n.source===e.source)return n}return null},e.prototype.onHandleError=function(e,t,n,r){return this._lastError=r,!1},e}(),p=null;function f(){return Zone&&Zone.ProxyZoneSpec}function d(){p&&p.unlockDatePatch(),p=null,f()&&f().assertPresent().resetDelegate()}function y(e,t){void 0===t&&(t={});var n=t.flush,r=void 0!==n&&n,i=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var i=f();if(!i)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");var s=i.assertPresent();if(Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!p){var o=Zone&&Zone.FakeAsyncTestZoneSpec;if(s.getDelegate()instanceof o)throw new Error("fakeAsync() calls can not be nested");p=new o}var a=void 0,c=s.getDelegate();s.setDelegate(p),p.lockDatePatch();try{a=e.apply(this,t),r?p.flush(20,!0):g()}finally{s.setDelegate(c)}if(!r){if(p.pendingPeriodicTimers.length>0)throw new Error("".concat(p.pendingPeriodicTimers.length," ")+"periodic timer(s) still in the queue.");if(p.pendingTimers.length>0)throw new Error("".concat(p.pendingTimers.length," timer(s) still in the queue."))}return a}finally{d()}};return i.isFakeAsync=!0,i}function m(){if(null==p&&null==(p=Zone.current.get("FakeAsyncTestZoneSpec")))throw new Error("The code should be running in the fakeAsync zone to call this function");return p}function T(e,t){void 0===e&&(e=0),void 0===t&&(t=!1),m().tick(e,null,t)}function _(e){return m().flush(e)}function k(){m().pendingPeriodicTimers.length=0}function g(){m().flushMicrotasks()}function v(e){var t="\n",n={},r="__creationTrace__",i="STACKTRACE TRACKING",s="__SEP_TAG__",o=s+"@[native]",a=function a(){this.error=p(),this.timestamp=new Date};function c(){return new Error(i)}function u(){try{throw c()}catch(e){return e}}var l=c(),h=u(),p=l.stack?c:h.stack?u:c;function f(e){return e.stack?e.stack.split(t):[]}function d(e,t){for(var r=f(t),i=0;i<r.length;i++)n.hasOwnProperty(r[i])||e.push(r[i])}function y(e,n){var r=[n?n.trim():""];if(e)for(var i=(new Date).getTime(),a=0;a<e.length;a++){var c=e[a],u=c.timestamp,l="____________________Elapsed ".concat(i-u.getTime()," ms; At: ").concat(u);l=l.replace(/[^\w\d]/g,"_"),r.push(o.replace(s,l)),d(r,c.error),i=u.getTime()}return r.join(t)}function m(){return Error.stackTraceLimit>0}function T(e,t){t>0&&(e.push(f((new a).error)),T(e,t-1))}e.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(t){if(t){var n=t[e.__symbol__("currentTaskTrace")];return n?y(n,t.stack):t.stack}},onScheduleTask:function(t,n,i,s){if(m()){var o=e.currentTask,c=o&&o.data&&o.data[r]||[];(c=[new a].concat(c)).length>this.longStackTraceLimit&&(c.length=this.longStackTraceLimit),s.data||(s.data={}),"eventTask"===s.type&&(s.data=__assign({},s.data)),s.data[r]=c}return t.scheduleTask(i,s)},onHandleError:function(t,n,i,s){if(m()){var o=e.currentTask||s.task;if(s instanceof Error&&o){var a=y(o.data&&o.data[r],s.stack);try{s.stack=s.longStack=a}catch(e){}}}return t.handleError(i,s)}},function _(){if(m()){var e=[];T(e,2);for(var t=e[0],r=e[1],a=0;a<t.length;a++)if(-1==(u=t[a]).indexOf(i)){var c=u.match(/^\s*at\s+/);if(c){o=c[0]+s+" (http://localhost)";break}}for(a=0;a<t.length;a++){var u;if((u=t[a])!==r[a])break;n[u]=!0}}}()}var S=function(){function e(e){void 0===e&&(e=null),this.defaultSpecDelegate=e,this.name="ProxyZone",this._delegateSpec=null,this.properties={ProxyZoneSpec:this},this.propertyKeys=null,this.lastTaskState=null,this.isNeedToTriggerHasTask=!1,this.tasks=[],this.setDelegate(e)}return e.get=function(){return Zone.current.get("ProxyZoneSpec")},e.isLoaded=function(){return e.get()instanceof e},e.assertPresent=function(){if(!e.isLoaded())throw new Error("Expected to be running in 'ProxyZone', but it was not found.");return e.get()},e.prototype.setDelegate=function(e){var t=this,n=this._delegateSpec!==e;this._delegateSpec=e,this.propertyKeys&&this.propertyKeys.forEach((function(e){return delete t.properties[e]})),this.propertyKeys=null,e&&e.properties&&(this.propertyKeys=Object.keys(e.properties),this.propertyKeys.forEach((function(n){return t.properties[n]=e.properties[n]}))),n&&this.lastTaskState&&(this.lastTaskState.macroTask||this.lastTaskState.microTask)&&(this.isNeedToTriggerHasTask=!0)},e.prototype.getDelegate=function(){return this._delegateSpec},e.prototype.resetDelegate=function(){this.getDelegate(),this.setDelegate(this.defaultSpecDelegate)},e.prototype.tryTriggerHasTask=function(e,t,n){this.isNeedToTriggerHasTask&&this.lastTaskState&&(this.isNeedToTriggerHasTask=!1,this.onHasTask(e,t,n,this.lastTaskState))},e.prototype.removeFromTasks=function(e){if(this.tasks)for(var t=0;t<this.tasks.length;t++)if(this.tasks[t]===e)return void this.tasks.splice(t,1)},e.prototype.getAndClearPendingTasksInfo=function(){if(0===this.tasks.length)return"";var e="--Pending async tasks are: ["+this.tasks.map((function(e){var t=e.data&&Object.keys(e.data).map((function(t){return t+":"+e.data[t]})).join(",");return"type: ".concat(e.type,", source: ").concat(e.source,", args: {").concat(t,"}")}))+"]";return this.tasks=[],e},e.prototype.onFork=function(e,t,n,r){return this._delegateSpec&&this._delegateSpec.onFork?this._delegateSpec.onFork(e,t,n,r):e.fork(n,r)},e.prototype.onIntercept=function(e,t,n,r,i){return this._delegateSpec&&this._delegateSpec.onIntercept?this._delegateSpec.onIntercept(e,t,n,r,i):e.intercept(n,r,i)},e.prototype.onInvoke=function(e,t,n,r,i,s,o){return this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onInvoke?this._delegateSpec.onInvoke(e,t,n,r,i,s,o):e.invoke(n,r,i,s,o)},e.prototype.onHandleError=function(e,t,n,r){return this._delegateSpec&&this._delegateSpec.onHandleError?this._delegateSpec.onHandleError(e,t,n,r):e.handleError(n,r)},e.prototype.onScheduleTask=function(e,t,n,r){return"eventTask"!==r.type&&this.tasks.push(r),this._delegateSpec&&this._delegateSpec.onScheduleTask?this._delegateSpec.onScheduleTask(e,t,n,r):e.scheduleTask(n,r)},e.prototype.onInvokeTask=function(e,t,n,r,i,s){return"eventTask"!==r.type&&this.removeFromTasks(r),this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onInvokeTask?this._delegateSpec.onInvokeTask(e,t,n,r,i,s):e.invokeTask(n,r,i,s)},e.prototype.onCancelTask=function(e,t,n,r){return"eventTask"!==r.type&&this.removeFromTasks(r),this.tryTriggerHasTask(e,t,n),this._delegateSpec&&this._delegateSpec.onCancelTask?this._delegateSpec.onCancelTask(e,t,n,r):e.cancelTask(n,r)},e.prototype.onHasTask=function(e,t,n,r){this.lastTaskState=r,this._delegateSpec&&this._delegateSpec.onHasTask?this._delegateSpec.onHasTask(e,t,n,r):e.hasTask(n,r)},e}();!function b(e){v(e),function t(e){e.ProxyZoneSpec=S}(e),function n(e){var t=function(){function t(t){this.runZone=e.current,this.name="syncTestZone for "+t}return t.prototype.onScheduleTask=function(e,t,n,r){switch(r.type){case"microTask":case"macroTask":throw new Error("Cannot call ".concat(r.source," from within a sync test (").concat(this.name,")."));case"eventTask":r=e.scheduleTask(n,r)}return r},t}();e.SyncTestZoneSpec=t}(e),function i(e){e.__load_patch("jasmine",(function(e,t,n){if(!t)throw new Error("Missing: zone.js");if("undefined"==typeof jest&&"undefined"!=typeof jasmine&&!jasmine.__zone_patch__){jasmine.__zone_patch__=!0;var r=t.SyncTestZoneSpec,i=t.ProxyZoneSpec;if(!r)throw new Error("Missing: SyncTestZoneSpec");if(!i)throw new Error("Missing: ProxyZoneSpec");var s=t.current,o=t.__symbol__,a=!0===e[o("fakeAsyncDisablePatchingClock")],c=!a&&(!0===e[o("fakeAsyncPatchLock")]||!0===e[o("fakeAsyncAutoFakeAsyncWhenClockPatched")]);if(!0!==e[o("ignoreUnhandledRejection")]){var u=jasmine.GlobalErrors;u&&!jasmine[o("GlobalErrors")]&&(jasmine[o("GlobalErrors")]=u,jasmine.GlobalErrors=function(){var t=new u,n=t.install;return n&&!t[o("install")]&&(t[o("install")]=n,t.install=function(){var t="undefined"!=typeof process&&!!process.on,r=t?process.listeners("unhandledRejection"):e.eventListeners("unhandledrejection"),i=n.apply(this,arguments);return t?process.removeAllListeners("unhandledRejection"):e.removeAllListeners("unhandledrejection"),r&&r.forEach((function(n){t?process.on("unhandledRejection",n):e.addEventListener("unhandledrejection",n)})),i}),t})}var l=jasmine.getEnv();if(["describe","xdescribe","fdescribe"].forEach((function(e){var t=l[e];l[e]=function(e,n){return t.call(this,e,function i(e,t){return function(){return s.fork(new r("jasmine.describe#".concat(e))).run(t,this,arguments)}}(e,n))}})),["it","xit","fit"].forEach((function(e){var t=l[e];l[o(e)]=t,l[e]=function(e,n,r){return arguments[1]=y(n),t.apply(this,arguments)}})),["beforeEach","afterEach","beforeAll","afterAll"].forEach((function(e){var t=l[e];l[o(e)]=t,l[e]=function(e,n){return arguments[0]=y(e),t.apply(this,arguments)}})),!a){var h=jasmine[o("clock")]=jasmine.clock;jasmine.clock=function(){var e=h.apply(this,arguments);if(!e[o("patched")]){e[o("patched")]=o("patched");var n=e[o("tick")]=e.tick;e.tick=function(){var e=t.current.get("FakeAsyncTestZoneSpec");return e?e.tick.apply(e,arguments):n.apply(this,arguments)};var r=e[o("mockDate")]=e.mockDate;e.mockDate=function(){var e=t.current.get("FakeAsyncTestZoneSpec");if(e){var n=arguments.length>0?arguments[0]:new Date;return e.setFakeBaseSystemTime.apply(e,n&&"function"==typeof n.getTime?[n.getTime()]:arguments)}return r.apply(this,arguments)},c&&["install","uninstall"].forEach((function(n){var r=e[o(n)]=e[n];e[n]=function(){if(!t.FakeAsyncTestZoneSpec)return r.apply(this,arguments);jasmine[o("clockInstalled")]="install"===n}}))}return e}}if(!jasmine[t.__symbol__("createSpyObj")]){var p=jasmine.createSpyObj;jasmine[t.__symbol__("createSpyObj")]=p,jasmine.createSpyObj=function(){var e,t=Array.prototype.slice.call(arguments);if(t.length>=3&&t[2]){var n=Object.defineProperty;Object.defineProperty=function(e,t,r){return n.call(this,e,t,__assign(__assign({},r),{configurable:!0,enumerable:!0}))};try{e=p.apply(this,t)}finally{Object.defineProperty=n}}else e=p.apply(this,t);return e}}var f=jasmine.QueueRunner;jasmine.QueueRunner=function(n){function r(r){var i,o=this;r.onComplete&&(r.onComplete=(i=r.onComplete,function(){o.testProxyZone=null,o.testProxyZoneSpec=null,s.scheduleMicroTask("jasmine.onComplete",i)}));var a=e[t.__symbol__("setTimeout")],c=e[t.__symbol__("clearTimeout")];a&&(r.timeout={setTimeout:a||e.setTimeout,clearTimeout:c||e.clearTimeout}),jasmine.UserContext?(r.userContext||(r.userContext=new jasmine.UserContext),r.userContext.queueRunner=this):(r.userContext||(r.userContext={}),r.userContext.queueRunner=this);var u=r.onException;r.onException=function(e){if(e&&"Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL."===e.message){var t=this&&this.testProxyZoneSpec;if(t){var n=t.getAndClearPendingTasksInfo();try{e.message+=n}catch(e){}}}u&&u.call(this,e)},n.call(this,r)}return function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);function r(){this.constructor=e}e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(r,n),r.prototype.execute=function(){for(var e=this,r=t.current,o=!1;r;){if(r===s){o=!0;break}r=r.parent}if(!o)throw new Error("Unexpected Zone: "+t.current.name);this.testProxyZoneSpec=new i,this.testProxyZone=s.fork(this.testProxyZoneSpec),t.currentTask?n.prototype.execute.call(this):t.current.scheduleMicroTask("jasmine.execute().forceTask",(function(){return f.prototype.execute.call(e)}))},r}(f)}function d(e,n,r,i){var s=!!jasmine[o("clockInstalled")],a=r.testProxyZone;if(s&&c){var u=t[t.__symbol__("fakeAsyncTest")];u&&"function"==typeof u.fakeAsync&&(e=u.fakeAsync(e))}return i?a.run(e,n,[i]):a.run(e,n)}function y(e){return e&&(e.length?function(t){return d(e,this,this.queueRunner,t)}:function(){return d(e,this,this.queueRunner)})}}))}(e),function a(e){e.__load_patch("jest",(function(e,t,n){if("undefined"!=typeof jest&&!jest.__zone_patch__){t[n.symbol("ignoreConsoleErrorUncaughtError")]=!0,jest.__zone_patch__=!0;var r=t.ProxyZoneSpec,i=t.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");var s=t.current,o=s.fork(new i("jest.describe")),a=new r,c=s.fork(a);["describe","xdescribe","fdescribe"].forEach((function(n){var r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e[1]=u(e[1]),r.apply(this,e)},e[n].each=function i(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=e.apply(this,t);return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e[1]=u(e[1]),r.apply(this,e)}}}(r.each))})),e.describe.only=e.fdescribe,e.describe.skip=e.xdescribe,["it","xit","fit","test","xtest"].forEach((function(n){var r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e[1]=l(e[1],!0),r.apply(this,e)},e[n].each=function i(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return n[1]=l(n[1]),e.apply(this,t).apply(this,n)}}}(r.each),e[n].todo=r.todo)})),e.it.only=e.fit,e.it.skip=e.xit,e.test.only=e.fit,e.test.skip=e.xit,["beforeEach","afterEach","beforeAll","afterAll"].forEach((function(n){var r=e[n];e[t.__symbol__(n)]||(e[t.__symbol__(n)]=r,e[n]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e[0]=l(e[0]),r.apply(this,e)})})),t.patchJestObject=function e(r,i){function s(){return!!t.current.get("FakeAsyncTestZoneSpec")}function o(){var e=t.current.get("ProxyZoneSpec");return e&&e.isTestFunc}void 0===i&&(i=!1),r[n.symbol("fakeTimers")]||(r[n.symbol("fakeTimers")]=!0,n.patchMethod(r,"_checkFakeTimers",(function(e){return function(t,n){return!!s()||e.apply(t,n)}})),n.patchMethod(r,"useFakeTimers",(function(e){return function(r,s){return t[n.symbol("useFakeTimersCalled")]=!0,i||o()?e.apply(r,s):r}})),n.patchMethod(r,"useRealTimers",(function(e){return function(r,s){return t[n.symbol("useFakeTimersCalled")]=!1,i||o()?e.apply(r,s):r}})),n.patchMethod(r,"setSystemTime",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");if(!i||!s())return e.apply(n,r);i.setFakeBaseSystemTime(r[0])}})),n.patchMethod(r,"getRealSystemTime",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");return i&&s()?i.getRealSystemTime():e.apply(n,r)}})),n.patchMethod(r,"runAllTicks",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");if(!i)return e.apply(n,r);i.flushMicrotasks()}})),n.patchMethod(r,"runAllTimers",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");if(!i)return e.apply(n,r);i.flush(100,!0)}})),n.patchMethod(r,"advanceTimersByTime",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");if(!i)return e.apply(n,r);i.tick(r[0])}})),n.patchMethod(r,"runOnlyPendingTimers",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");if(!i)return e.apply(n,r);i.flushOnlyPendingTimers()}})),n.patchMethod(r,"advanceTimersToNextTimer",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");if(!i)return e.apply(n,r);i.tickToNext(r[0])}})),n.patchMethod(r,"clearAllTimers",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");if(!i)return e.apply(n,r);i.removeAllTimers()}})),n.patchMethod(r,"getTimerCount",(function(e){return function(n,r){var i=t.current.get("FakeAsyncTestZoneSpec");return i?i.getTimerCount():e.apply(n,r)}})))}}function u(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return o.run(e,this,t)}}function l(e,r){if(void 0===r&&(r=!1),"function"!=typeof e)return e;var i=function(){if(!0===t[n.symbol("useFakeTimersCalled")]&&e&&!e.isFakeAsync){var i=t[t.__symbol__("fakeAsyncTest")];i&&"function"==typeof i.fakeAsync&&(e=i.fakeAsync(e))}return a.isTestFunc=r,c.run(e,null,arguments)};return Object.defineProperty(i,"length",{configurable:!0,writable:!0,enumerable:!1}),i.length=e.length,i}}))}(e),function c(e){e.__load_patch("mocha",(function(e,t){var n=e.Mocha;if(void 0!==n){if(void 0===t)throw new Error("Missing Zone.js");var r=t.ProxyZoneSpec,i=t.SyncTestZoneSpec;if(!r)throw new Error("Missing ProxyZoneSpec");if(n.__zone_patch__)throw new Error('"Mocha" has already been patched with "Zone".');n.__zone_patch__=!0;var s,o,a=t.current,c=a.fork(new i("Mocha.describe")),u=null,l=a.fork(new r),h={after:e.after,afterEach:e.afterEach,before:e.before,beforeEach:e.beforeEach,describe:e.describe,it:e.it};e.describe=e.suite=function(){return h.describe.apply(this,f(arguments))},e.xdescribe=e.suite.skip=e.describe.skip=function(){return h.describe.skip.apply(this,f(arguments))},e.describe.only=e.suite.only=function(){return h.describe.only.apply(this,f(arguments))},e.it=e.specify=e.test=function(){return h.it.apply(this,d(arguments))},e.xit=e.xspecify=e.it.skip=function(){return h.it.skip.apply(this,d(arguments))},e.it.only=e.test.only=function(){return h.it.only.apply(this,d(arguments))},e.after=e.suiteTeardown=function(){return h.after.apply(this,y(arguments))},e.afterEach=e.teardown=function(){return h.afterEach.apply(this,d(arguments))},e.before=e.suiteSetup=function(){return h.before.apply(this,y(arguments))},e.beforeEach=e.setup=function(){return h.beforeEach.apply(this,d(arguments))},s=n.Runner.prototype.runTest,o=n.Runner.prototype.run,n.Runner.prototype.runTest=function(e){var n=this;t.current.scheduleMicroTask("mocha.forceTask",(function(){s.call(n,e)}))},n.Runner.prototype.run=function(e){return this.on("test",(function(e){u=a.fork(new r)})),this.on("fail",(function(e,t){var n=u&&u.get("ProxyZoneSpec");if(n&&t)try{t.message+=n.getAndClearPendingTasksInfo()}catch(e){}})),o.call(this,e)}}function p(e,t,n){for(var r=function(r){var i=e[r];"function"==typeof i&&(e[r]=0===i.length?t(i):n(i),e[r].toString=function(){return i.toString()})},i=0;i<e.length;i++)r(i);return e}function f(e){return p(e,(function(e){return function(){return c.run(e,this,arguments)}}))}function d(e){return p(e,(function(e){return function(){return u.run(e,this)}}),(function(e){return function(t){return u.run(e,this,[t])}}))}function y(e){return p(e,(function(e){return function(){return l.run(e,this)}}),(function(e){return function(t){return l.run(e,this,[t])}}))}}))}(e),function u(e){e.AsyncTestZoneSpec=s,e.__load_patch("asynctest",(function(e,t,n){function r(e,n,r,i){var s=t.current,o=t.AsyncTestZoneSpec;if(void 0===o)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/async-test");var a=t.ProxyZoneSpec;if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");var c=a.get();a.assertPresent();var u=t.current.getZoneWith("ProxyZoneSpec"),l=c.getDelegate();return u.parent.run((function(){var e=new o((function(){c.getDelegate()==e&&c.setDelegate(l),e.unPatchPromiseForTest(),s.run((function(){r()}))}),(function(t){c.getDelegate()==e&&c.setDelegate(l),e.unPatchPromiseForTest(),s.run((function(){i(t)}))}),"test");c.setDelegate(e),e.patchPromiseForTest()})),t.current.runGuarded(e,n)}t[n.symbol("asyncTest")]=function t(n){return e.jasmine?function(e){e||((e=function(){}).fail=function(e){throw e}),r(n,this,e,(function(t){if("string"==typeof t)return e.fail(new Error(t));e.fail(t)}))}:function(){var e=this;return new Promise((function(t,i){r(n,e,t,i)}))}}}))}(e),function p(e){e.FakeAsyncTestZoneSpec=h,e.__load_patch("fakeasync",(function(e,t,n){t[n.symbol("fakeAsyncTest")]={resetFakeAsyncZone:d,flushMicrotasks:g,discardPeriodicTasks:k,tick:T,flush:_,fakeAsync:y}}),!0),r={setTimeout:o.setTimeout,setInterval:o.setInterval,clearTimeout:o.clearTimeout,clearInterval:o.clearInterval,nativeSetTimeout:o[e.__symbol__("setTimeout")],nativeClearTimeout:o[e.__symbol__("clearTimeout")]},l.nextId=l.getNextId()}(e),function f(e){e.__load_patch("promisefortest",(function(e,t,n){var r=n.symbol("state"),i=n.symbol("parentUnresolved");Promise[n.symbol("patchPromiseForTest")]=function e(){var n=Promise[t.__symbol__("ZonePromiseThen")];n||(n=Promise[t.__symbol__("ZonePromiseThen")]=Promise.prototype.then,Promise.prototype.then=function(){var e=n.apply(this,arguments);if(null===this[r]){var s=t.current.get("AsyncTestZoneSpec");s&&(s.unresolvedChainedPromiseCount++,e[i]=!0)}return e})},Promise[n.symbol("unPatchPromiseForTest")]=function e(){var n=Promise[t.__symbol__("ZonePromiseThen")];n&&(Promise.prototype.then=n,Promise[t.__symbol__("ZonePromiseThen")]=void 0)}}))}(e)}(Zone)}));