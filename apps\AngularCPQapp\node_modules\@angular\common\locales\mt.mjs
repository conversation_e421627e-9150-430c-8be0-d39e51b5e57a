/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    if (n === 0 || n % 100 === Math.floor(n % 100) && (n % 100 >= 2 && n % 100 <= 10))
        return 3;
    if (n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 19))
        return 4;
    return 5;
}
export default ["mt", [["am", "pm"], ["AM", "PM"], u], u, [["Ħd", "T", "Tl", "Er", "Ħm", "Ġm", "Sb"], ["Ħad", "Tne", "Tli", "Erb", "Ħam", "Ġim", "Sib"], ["Il-Ħadd", "It-Tnejn", "It-Tlieta", "L-Erbgħa", "Il-Ħamis", "Il-Ġimgħa", "Is-Sibt"], ["Ħad", "Tne", "Tli", "Erb", "Ħam", "Ġim", "Sib"]], [["Ħd", "Tn", "Tl", "Er", "Ħm", "Ġm", "Sb"], ["Ħad", "Tne", "Tli", "Erb", "Ħam", "Ġim", "Sib"], ["Il-Ħadd", "It-Tnejn", "It-Tlieta", "L-Erbgħa", "Il-Ħamis", "Il-Ġimgħa", "Is-Sibt"], ["Ħad", "Tne", "Tli", "Erb", "Ħam", "Ġim", "Sib"]], [["J", "F", "M", "A", "M", "Ġ", "L", "A", "S", "O", "N", "D"], ["Jan", "Fra", "Mar", "Apr", "Mej", "Ġun", "Lul", "Aww", "Set", "Ott", "Nov", "Diċ"], ["Jannar", "Frar", "Marzu", "April", "Mejju", "Ġunju", "Lulju", "Awwissu", "Settembru", "Ottubru", "Novembru", "Diċembru"]], [["Jn", "Fr", "Mz", "Ap", "Mj", "Ġn", "Lj", "Aw", "St", "Ob", "Nv", "Dċ"], ["Jan", "Fra", "Mar", "Apr", "Mej", "Ġun", "Lul", "Aww", "Set", "Ott", "Nov", "Diċ"], ["Jannar", "Frar", "Marzu", "April", "Mejju", "Ġunju", "Lulju", "Awwissu", "Settembru", "Ottubru", "Novembru", "Diċembru"]], [["QK", "WK"], u, ["Qabel Kristu", "Wara Kristu"]], 0, [6, 0], ["dd/MM/y", "dd MMM y", "d 'ta'’ MMMM y", "EEEE, d 'ta'’ MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "EUR", "€", "ewro", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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