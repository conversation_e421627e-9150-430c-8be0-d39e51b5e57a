/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["luo", [["OD", "OT"], u, u], u, [["J", "W", "T", "T", "T", "T", "N"], ["JMP", "WUT", "TAR", "TAD", "TAN", "TAB", "NGS"], ["Ju<PERSON>pi<PERSON>", "Wuok Tich", "Tich Ariyo", "Tich Adek", "Tich Ang’wen", "Tich Abich", "Ngeso"], ["JMP", "WUT", "TAR", "TAD", "TAN", "TAB", "NGS"]], u, [["C", "R", "D", "N", "B", "U", "B", "B", "C", "P", "C", "P"], ["DAC", "DAR", "DAD", "DAN", "DAH", "DAU", "DAO", "DAB", "DOC", "DAP", "DGI", "DAG"], ["Dwe mar Achiel", "Dwe mar Ariyo", "Dwe mar Adek", "Dwe mar Ang’wen", "Dwe mar Abich", "Dwe mar Auchiel", "Dwe mar Abiriyo", "Dwe mar Aboro", "Dwe mar Ochiko", "Dwe mar Apar", "Dwe mar gi achiel", "Dwe mar Apar gi ariyo"]], u, [["BC", "AD"], u, ["Kapok Kristo obiro", "Ka Kristo osebiro"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "KES", "Ksh", "Siling mar Kenya", { "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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