/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["dz", [["སྔ་ཆ་", "ཕྱི་ཆ་"], u, u], u, [["ཟླ", "མིར", "ལྷག", "ཕུར", "སངྶ", "སྤེན", "ཉི"], ["ཟླ་", "མིར་", "ལྷག་", "ཕུར་", "སངས་", "སྤེན་", "ཉི་"], ["གཟའ་ཟླ་བ་", "གཟའ་མིག་དམར་", "གཟའ་ལྷག་པ་", "གཟའ་ཕུར་བུ་", "གཟའ་པ་སངས་", "གཟའ་སྤེན་པ་", "གཟའ་ཉི་མ་"], ["ཟླ་", "མིར་", "ལྷག་", "ཕུར་", "སངས་", "སྤེན་", "ཉི་"]], u, [["༡", "༢", "༣", "4", "༥", "༦", "༧", "༨", "9", "༡༠", "༡༡", "༡༢"], ["༡", "༢", "༣", "༤", "༥", "༦", "༧", "༨", "༩", "༡༠", "༡༡", "12"], ["ཟླ་དངཔ་", "ཟླ་གཉིས་པ་", "ཟླ་གསུམ་པ་", "ཟླ་བཞི་པ་", "ཟླ་ལྔ་པ་", "ཟླ་དྲུག་པ", "ཟླ་བདུན་པ་", "ཟླ་བརྒྱད་པ་", "ཟླ་དགུ་པ་", "ཟླ་བཅུ་པ་", "ཟླ་བཅུ་གཅིག་པ་", "ཟླ་བཅུ་གཉིས་པ་"]], [["༡", "༢", "༣", "༤", "༥", "༦", "༧", "༨", "༩", "༡༠", "༡༡", "༡༢"], ["ཟླ་༡", "ཟླ་༢", "ཟླ་༣", "ཟླ་༤", "ཟླ་༥", "ཟླ་༦", "ཟླ་༧", "ཟླ་༨", "ཟླ་༩", "ཟླ་༡༠", "ཟླ་༡༡", "ཟླ་༡༢"], ["སྤྱི་ཟླ་དངཔ་", "སྤྱི་ཟླ་གཉིས་པ་", "སྤྱི་ཟླ་གསུམ་པ་", "སྤྱི་ཟླ་བཞི་པ", "སྤྱི་ཟླ་ལྔ་པ་", "སྤྱི་ཟླ་དྲུག་པ", "སྤྱི་ཟླ་བདུན་པ་", "སྤྱི་ཟླ་བརྒྱད་པ་", "སྤྱི་ཟླ་དགུ་པ་", "སྤྱི་ཟླ་བཅུ་པ་", "སྤྱི་ཟླ་བཅུ་གཅིག་པ་", "སྤྱི་ཟླ་བཅུ་གཉིས་པ་"]], [["BCE", "CE"], u, u], 0, [6, 0], ["y-MM-dd", "སྤྱི་ལོ་y ཟླ་MMM ཚེས་dd", "སྤྱི་ལོ་y MMMM ཚེས་ dd", "EEEE, སྤྱི་ལོ་y MMMM ཚེས་dd"], ["ཆུ་ཚོད་ h སྐར་མ་ mm a", "ཆུ་ཚོད་h:mm:ss a", "ཆུ་ཚོད་ h སྐར་མ་ mm:ss a z", "ཆུ་ཚོད་ h སྐར་མ་ mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0 %", "¤#,##,##0.00", "#E0"], "INR", "₹", "རྒྱ་གར་གྱི་དངུལ་ རུ་པི", { "AUD": ["AU$", "$"], "BTN": ["Nu."], "ILS": [u, "₪"], "JPY": ["JP¥", "¥"], "KRW": ["KR₩", "₩"], "THB": ["TH฿", "฿"], "USD": ["US$", "$"], "XAF": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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