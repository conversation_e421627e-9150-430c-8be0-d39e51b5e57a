/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["tk", [["öň", "soň"], ["go.öň", "go.soň"], ["günortadan <PERSON>", "günortadan soň"]], [["öň", "soň"], ["g.öň", "g.soň"], ["günortadan ö<PERSON>", "günortadan soň"]], [["Ý", "D", "S", "Ç", "P", "A", "Ş"], ["ýek", "duş", "siş", "çar", "pen", "ann", "şen"], ["ýekşenbe", "duşenbe", "sişenbe", "çarşenbe", "penşenbe", "anna", "şenbe"], ["ýb", "db", "sb", "çb", "pb", "an", "şb"]], [["Ý", "D", "S", "Ç", "P", "A", "Ş"], ["Ýek", "Duş", "<PERSON>ş", "Çar", "Pen", "Ann", "Şen"], ["Ýekşenbe", "Duşenbe", "Sişenbe", "Çarşenbe", "Penşenbe", "Anna", "Şenbe"], ["Ýb", "Db", "Sb", "Çb", "Pb", "An", "Şb"]], [["Ý", "F", "M", "A", "M", "I", "I", "A", "S", "O", "N", "D"], ["ýan", "few", "mart", "apr", "maý", "iýun", "iýul", "awg", "sen", "okt", "noý", "dek"], ["ýanwar", "fewral", "mart", "aprel", "maý", "iýun", "iýul", "awgust", "sentýabr", "oktýabr", "noýabr", "dekabr"]], [["Ý", "F", "M", "A", "M", "I", "I", "A", "S", "O", "N", "D"], ["Ýan", "Few", "Mar", "Apr", "Maý", "Iýun", "Iýul", "Awg", "Sen", "Okt", "Noý", "Dek"], ["Ýanwar", "Fewral", "Mart", "Aprel", "Maý", "Iýun", "Iýul", "Awgust", "Sentýabr", "Oktýabr", "Noýabr", "Dekabr"]], [["B.e.öň", "B.e."], u, ["Isadan öň", "Isadan soň"]], 1, [6, 0], ["dd.MM.y", "d MMM y", "d MMMM y", "d MMMM y EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "san däl", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "TMT", "TMT", "Türkmen manady", { "BYN": [u, "р."], "EUR": [u, "€"], "GBP": [u, "£"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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