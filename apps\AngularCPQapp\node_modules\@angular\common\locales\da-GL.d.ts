/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    BYN: (string | undefined)[];
    DKK: string[];
    ISK: (string | undefined)[];
    JPY: string[];
    NOK: (string | undefined)[];
    PHP: (string | undefined)[];
    RON: (string | undefined)[];
    SEK: (string | undefined)[];
    THB: string[];
    TWD: string[];
    USD: string[];
} | undefined)[];
export default _default;
