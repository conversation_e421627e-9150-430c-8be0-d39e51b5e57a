"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(n){"function"==typeof define&&define.amd?define(n):n()}((function(){var n="object"==typeof window&&window||"object"==typeof self&&self||global;!function e(o){var c,t,a=null,r=null,i=!(!(t=n.wtf)||!(a=t.trace)||(r=a.events,0)),s=function(){function n(){this.name="WTF"}return n.prototype.onFork=function(n,e,o,t){var a=n.fork(o,t);return c.forkInstance(f(o),a.name),a},n.prototype.onInvoke=function(n,e,o,t,i,s,u){var l=u||"unknown",p=c.invokeScope[l];return p||(p=c.invokeScope[l]=r.createScope("Zone:invoke:".concat(u,"(ascii zone)"))),a.leaveScope(p(f(o)),n.invoke(o,t,i,s,u))},n.prototype.onHandleError=function(n,e,o,c){return n.handleError(o,c)},n.prototype.onScheduleTask=function(n,e,o,t){var a=t.type+":"+t.source,i=c.scheduleInstance[a];i||(i=c.scheduleInstance[a]=r.createInstance("Zone:schedule:".concat(a,"(ascii zone, any data)")));var s=n.scheduleTask(o,t);return i(f(o),u(t.data,2)),s},n.prototype.onInvokeTask=function(n,e,o,t,i,s){var u=t.source,l=c.invokeTaskScope[u];return l||(l=c.invokeTaskScope[u]=r.createScope("Zone:invokeTask:".concat(u,"(ascii zone)"))),a.leaveScope(l(f(o)),n.invokeTask(o,t,i,s))},n.prototype.onCancelTask=function(n,e,o,t){var a=t.source,i=c.cancelInstance[a];i||(i=c.cancelInstance[a]=r.createInstance("Zone:cancel:".concat(a,"(ascii zone, any options)")));var s=n.cancelTask(o,t);return i(f(o),u(t.data,2)),s},n}();function u(n,e){if(!n||!e)return null;var o={};for(var c in n)if(n.hasOwnProperty(c)){var t=n[c];switch(typeof t){case"object":var a=t&&t.constructor&&t.constructor.name;t=a==Object.name?u(t,e-1):a;break;case"function":t=t.name||void 0}o[c]=t}return o}function f(n){for(var e=n.name,o=n.parent;null!=o;)e=o.name+"::"+e,o=o.parent;return e}(c=s).forkInstance=i?r.createInstance("Zone:fork(ascii zone, ascii newZone)"):null,c.scheduleInstance={},c.cancelInstance={},c.invokeScope={},c.invokeTaskScope={},o.wtfZoneSpec=i?new s:null}(Zone)}));