/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === Math.floor(n) && (n >= 0 && n <= 1) || n === Math.floor(n) && (n >= 11 && n <= 99))
        return 1;
    return 5;
}
export default ["tzm", [["Zdat azal", "Ḍeffir aza"], u, u], u, [["A", "A", "A", "A", "A", "A", "A"], ["Asa", "<PERSON>yn", "Asn", "Akr", "Akw", "Asm", "Asḍ"], ["<PERSON>amas", "<PERSON>ynas", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>kwas", "<PERSON><PERSON>was", "<PERSON>i<PERSON><PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>n", "Akr", "Akw", "Asm", "Asḍ"]], u, [["Y", "Y", "M", "I", "M", "Y", "Y", "Ɣ", "C", "K", "<PERSON>", "<PERSON>"], ["<PERSON>n", "<PERSON>b", "Mar", "Ibr", "May", "<PERSON>", "<PERSON>l", "Ɣuc", "<PERSON>", "K<PERSON>u", "Nwa", "<PERSON>j"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ibrir", "Mayyu", "<PERSON>yu", "<PERSON>lyuz", "Ɣuct", "<PERSON>an<PERSON>", "<PERSON>ṭuber", "Nwanbir", "Dujanbir"]], u, [["ZƐ", "ḌƐ"], u, ["Zdat Ɛisa (TAƔ)", "Ḍeffir Ɛisa (TAƔ)"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "MAD", "MAD", "Derhem Umeṛṛuki", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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