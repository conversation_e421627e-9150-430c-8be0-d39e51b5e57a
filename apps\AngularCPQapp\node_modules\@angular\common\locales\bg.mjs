/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["bg", [["am", "pm"], u, ["пр.об.", "сл.об."]], [["am", "pm"], u, u], [["н", "п", "в", "с", "ч", "п", "с"], ["нд", "пн", "вт", "ср", "чт", "пт", "сб"], ["неделя", "понеделник", "вторник", "сряда", "четвъртък", "петък", "събота"], ["нд", "пн", "вт", "ср", "чт", "пт", "сб"]], u, [["я", "ф", "м", "а", "м", "ю", "ю", "а", "с", "о", "н", "д"], ["яну", "фев", "март", "апр", "май", "юни", "юли", "авг", "сеп", "окт", "ное", "дек"], ["януари", "февруари", "март", "април", "май", "юни", "юли", "август", "септември", "октомври", "ноември", "декември"]], u, [["пр.Хр.", "сл.Хр."], u, ["преди Христа", "след Христа"]], 1, [6, 0], ["d.MM.yy 'г'.", "d.MM.y 'г'.", "d MMMM y 'г'.", "EEEE, d MMMM y 'г'."], ["H:mm 'ч'.", "H:mm:ss 'ч'.", "H:mm:ss 'ч'. z", "H:mm:ss 'ч'. zzzz"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "0.00 ¤", "#E0"], "BGN", "лв.", "Български лев", { "AFN": [u, "Af"], "AMD": [], "ARS": [], "AUD": [], "AZN": [], "BBD": [], "BDT": [], "BGN": ["лв."], "BMD": [], "BND": [], "BRL": [], "BSD": [], "BZD": [], "CAD": [], "CLP": [], "CNY": [], "COP": [], "CRC": [], "CUP": [], "DOP": [], "FJD": [], "FKP": [], "GBP": [u, "£"], "GHS": [], "GIP": [], "GYD": [], "HKD": [], "ILS": [], "INR": [], "JMD": [], "JPY": [u, "¥"], "KHR": [], "KRW": [], "KYD": [], "KZT": [], "LAK": [], "LRD": [], "MNT": [], "MXN": [], "NAD": [], "NGN": [], "NZD": [], "PHP": [], "PYG": [], "RON": [], "SBD": [], "SGD": [], "SRD": [], "SSP": [], "TRY": [], "TTD": [], "TWD": [], "UAH": [], "USD": ["щ.д.", "$"], "UYU": [], "VND": [], "XCD": [u, "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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