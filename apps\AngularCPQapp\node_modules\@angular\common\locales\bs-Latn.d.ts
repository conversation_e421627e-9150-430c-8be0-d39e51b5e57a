/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | string[][] | {
    AUD: (string | undefined)[];
    BAM: string[];
    BRL: (string | undefined)[];
    BYN: (string | undefined)[];
    CAD: (string | undefined)[];
    CNY: (string | undefined)[];
    GBP: (string | undefined)[];
    HKD: (string | undefined)[];
    HRK: string[];
    ILS: (string | undefined)[];
    MXN: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    RSD: string[];
    THB: string[];
    TWD: string[];
    USD: (string | undefined)[];
    XCD: (string | undefined)[];
    XPF: never[];
} | undefined)[];
export default _default;
