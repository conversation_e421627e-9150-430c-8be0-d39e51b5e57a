/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    return 5;
}
export default ["gl", [["a.m.", "p.m."], u, u], u, [["d.", "l.", "m.", "m.", "x.", "v.", "s."], ["dom.", "luns", "mar.", "mér.", "xov.", "ven.", "sáb."], ["domingo", "luns", "martes", "mércores", "xoves", "venres", "s<PERSON>bado"], ["do.", "lu.", "ma.", "mé.", "xo.", "ve.", "sá."]], [["D", "L", "M", "M", "X", "V", "S"], ["Dom.", "Luns", "Mar.", "Mér.", "Xov.", "Ven.", "S<PERSON>b."], ["<PERSON>", "Luns", "<PERSON>es", "M<PERSON>rcores", "<PERSON>oves", "Venres", "S<PERSON>bado"], ["Do", "Lu", "Ma", "Mé", "Xo", "Ve", "Sá"]], [["x.", "f.", "m.", "a.", "m.", "x.", "x.", "a.", "s.", "o.", "n.", "d."], ["xan.", "feb.", "mar.", "abr.", "maio", "xuño", "xul.", "ago.", "set.", "out.", "nov.", "dec."], ["xaneiro", "febreiro", "marzo", "abril", "maio", "xuño", "xullo", "agosto", "setembro", "outubro", "novembro", "decembro"]], [["X", "F", "M", "A", "M", "X", "X", "A", "S", "O", "N", "D"], ["Xan.", "Feb.", "Mar.", "Abr.", "Maio", "Xuño", "Xul.", "Ago.", "Set.", "Out.", "Nov.", "Dec."], ["Xaneiro", "Febreiro", "Marzo", "Abril", "Maio", "Xuño", "Xullo", "Agosto", "Setembro", "Outubro", "Novembro", "Decembro"]], [["a.C.", "d.C."], u, ["antes de Cristo", "despois de Cristo"]], 1, [6, 0], ["dd/MM/yy", "d 'de' MMM 'de' y", "d 'de' MMMM 'de' y", "EEEE, d 'de' MMMM 'de' y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{0}, {1}", u, "{0} 'do' {1}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "EUR", "€", "euro", { "BYN": [u, "Br"], "ESP": ["₧"], "JPY": ["JP¥", "¥"], "KMF": [u, "FC"], "MXN": ["$MX", "$"], "PHP": [u, "₱"], "RUB": [u, "руб"], "THB": ["฿"], "TWD": ["NT$"], "XCD": [u, "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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