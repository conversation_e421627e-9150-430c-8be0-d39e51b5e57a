/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["uz-Cyrl", [["ТО", "ТК"], u, u], u, [["Я", "Д", "С", "Ч", "П", "Ж", "Ш"], ["якш", "душ", "сеш", "чор", "пай", "жум", "шан"], ["якшанба", "душанба", "сешанба", "чоршанба", "пайшанба", "жума", "шанба"], ["як", "ду", "се", "чо", "па", "жу", "ша"]], u, [["Я", "Ф", "М", "А", "М", "И", "И", "А", "С", "О", "Н", "Д"], ["янв", "фев", "мар", "апр", "май", "июн", "июл", "авг", "сен", "окт", "ноя", "дек"], ["январ", "феврал", "март", "апрел", "май", "июн", "июл", "август", "сентябр", "октябр", "ноябр", "декабр"]], u, [["м.а.", "милодий"], u, ["милоддан аввалги", "милодий"]], 1, [6, 0], ["dd/MM/yy", "d MMM, y", "d MMMM, y", "EEEE, dd MMMM, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss (z)", "HH:mm:ss (zzzz)"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "ҳақиқий сон эмас", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "UZS", "сўм", "Ўзбекистон сўм", { "JPY": ["JP¥", "¥"], "THB": ["฿"], "USD": ["US$", "$"], "UZS": ["сўм"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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