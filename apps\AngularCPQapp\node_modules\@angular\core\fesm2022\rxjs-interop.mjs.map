{"version": 3, "file": "rxjs-interop.mjs", "sources": ["../../../../../../packages/core/rxjs-interop/src/take_until_destroyed.ts", "../../../../../../packages/core/rxjs-interop/src/output_from_observable.ts", "../../../../../../packages/core/rxjs-interop/src/output_to_observable.ts", "../../../../../../packages/core/rxjs-interop/src/to_observable.ts", "../../../../../../packages/core/rxjs-interop/src/to_signal.ts", "../../../../../../packages/core/rxjs-interop/rxjs-interop.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {assertInInjectionContext, DestroyRef, inject} from '@angular/core';\nimport {MonoTypeOperatorFunction, Observable} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nexport function takeUntilDestroyed<T>(destroyRef?: DestroyRef): MonoTypeOperatorFunction<T> {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n\n  const destroyed$ = new Observable<void>(observer => {\n    const unregisterFn = destroyRef!.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n\n  return <T>(source: Observable<T>) => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {assertInInjectionContext, DestroyRef, inject, OutputOptions, OutputRef, OutputRefSubscription, ɵRuntimeError, ɵRuntimeErrorCode} from '@angular/core';\nimport {Observable} from 'rxjs';\n\nimport {takeUntilDestroyed} from './take_until_destroyed';\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef<T> implements OutputRef<T> {\n  private destroyed = false;\n\n  destroyRef = inject(DestroyRef);\n\n  constructor(private source: Observable<T>) {\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n\n  subscribe(callbackFn: (value: T) => void): OutputRefSubscription {\n    if (this.destroyed) {\n      throw new ɵRuntimeError(\n          ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED,\n          ngDevMode &&\n              'Unexpected subscription to destroyed `OutputRef`. ' +\n                  'The owning directive/component is destroyed.');\n    }\n\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value),\n    });\n\n    return {\n      unsubscribe: () => subscription.unsubscribe(),\n    };\n  }\n}\n\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @developerPreview\n */\nexport function outputFromObservable<T>(\n    observable: Observable<T>, opts?: OutputOptions): OutputRef<T> {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef<T>(observable);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OutputRef, ɵgetOutputDestroyRef} from '@angular/core';\nimport {Observable} from 'rxjs';\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @developerPreview\n */\nexport function outputToObservable<T>(ref: OutputRef<T>): Observable<T> {\n  const destroyRef = ɵgetOutputDestroyRef(ref);\n\n  return new Observable<T>(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {assertInInjectionContext, DestroyRef, effect, inject, Injector, Signal, untracked} from '@angular/core';\nimport {Observable, ReplaySubject} from 'rxjs';\n\n/**\n * Options for `toObservable`.\n *\n * @developerPreview\n */\nexport interface ToObservableOptions {\n  /**\n   * The `Injector` to use when creating the underlying `effect` which watches the signal.\n   *\n   * If this isn't specified, the current [injection context](guide/dependency-injection-context)\n   * will be used.\n   */\n  injector?: Injector;\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nexport function toObservable<T>(\n    source: Signal<T>,\n    options?: ToObservableOptions,\n    ): Observable<T> {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject<T>(1);\n\n  const watcher = effect(() => {\n    let value: T;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {injector, manualCleanup: true});\n\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n\n  return subject.asObservable();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {assertInInjectionContext, assertNotInReactiveContext, computed, DestroyRef, inject, Injector, signal, Signal, WritableSignal, ɵRuntimeError, ɵRuntimeErrorCode} from '@angular/core';\nimport {Observable, Subscribable} from 'rxjs';\n\n/**\n * Options for `toSignal`.\n *\n * @publicApi\n */\nexport interface ToSignalOptions {\n  /**\n   * Initial value for the signal produced by `toSignal`.\n   *\n   * This will be the value of the signal until the observable emits its first value.\n   */\n  initialValue?: unknown;\n\n  /**\n   * Whether to require that the observable emits synchronously when `toSignal` subscribes.\n   *\n   * If this is `true`, `toSignal` will assert that the observable produces a value immediately upon\n   * subscription. Setting this option removes the need to either deal with `undefined` in the\n   * signal type or provide an `initialValue`, at the cost of a runtime error if this requirement is\n   * not met.\n   */\n  requireSync?: boolean;\n\n  /**\n   * `Injector` which will provide the `DestroyRef` used to clean up the Observable subscription.\n   *\n   * If this is not provided, a `DestroyRef` will be retrieved from the current [injection\n   * context](/guide/dependency-injection-context), unless manual cleanup is requested.\n   */\n  injector?: Injector;\n\n  /**\n   * Whether the subscription should be automatically cleaned up (via `DestroyRef`) when\n   * `toSignal`'s creation context is destroyed.\n   *\n   * If manual cleanup is enabled, then `DestroyRef` is not used, and the subscription will persist\n   * until the `Observable` itself completes.\n   */\n  manualCleanup?: boolean;\n\n  /**\n   * Whether `toSignal` should throw errors from the Observable error channel back to RxJS, where\n   * they'll be processed as uncaught exceptions.\n   *\n   * In practice, this means that the signal returned by `toSignal` will keep returning the last\n   * good value forever, as Observables which error produce no further values. This option emulates\n   * the behavior of the `async` pipe.\n   */\n  rejectErrors?: boolean;\n}\n\n// Base case: no options -> `undefined` in the result type.\nexport function toSignal<T>(source: Observable<T>|Subscribable<T>): Signal<T|undefined>;\n// Options with `undefined` initial value and no `requiredSync` -> `undefined`.\nexport function toSignal<T>(\n    source: Observable<T>|Subscribable<T>,\n    options: ToSignalOptions&{initialValue?: undefined, requireSync?: false}): Signal<T|undefined>;\n// Options with `null` initial value -> `null`.\nexport function toSignal<T>(\n    source: Observable<T>|Subscribable<T>,\n    options: ToSignalOptions&{initialValue?: null, requireSync?: false}): Signal<T|null>;\n// Options with `undefined` initial value and `requiredSync` -> strict result type.\nexport function toSignal<T>(\n    source: Observable<T>|Subscribable<T>,\n    options: ToSignalOptions&{initialValue?: undefined, requireSync: true}): Signal<T>;\n// Options with a more specific initial value type.\nexport function toSignal<T, const U extends T>(\n    source: Observable<T>|Subscribable<T>,\n    options: ToSignalOptions&{initialValue: U, requireSync?: false}): Signal<T|U>;\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](/guide/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nexport function toSignal<T, U = undefined>(\n    source: Observable<T>|Subscribable<T>,\n    options?: ToSignalOptions&{initialValue?: U}): Signal<T|U> {\n  ngDevMode &&\n      assertNotInReactiveContext(\n          toSignal,\n          'Invoking `toSignal` causes new subscriptions every time. ' +\n              'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef =\n      requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state: WritableSignal<State<T|U>>;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({kind: StateKind.NoValue});\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal<State<T|U>>({kind: StateKind.Value, value: options?.initialValue as U});\n  }\n\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({kind: StateKind.Value, value}),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({kind: StateKind.Error, error});\n    },\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n\n  if (ngDevMode && options?.requireSync && state().kind === StateKind.NoValue) {\n    throw new ɵRuntimeError(\n        ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT,\n        '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case StateKind.Value:\n        return current.value;\n      case StateKind.Error:\n        throw current.error;\n      case StateKind.NoValue:\n        // This shouldn't really happen because the error is thrown on creation.\n        // TODO(alxhub): use a RuntimeError when we finalize the error semantics\n        throw new ɵRuntimeError(\n            ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT,\n            '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  });\n}\n\nconst enum StateKind {\n  NoValue,\n  Value,\n  Error,\n}\n\ninterface NoValueState {\n  kind: StateKind.NoValue;\n}\n\ninterface ValueState<T> {\n  kind: StateKind.Value;\n  value: T;\n}\n\ninterface ErrorState {\n  kind: StateKind.Error;\n  error: unknown;\n}\n\ntype State<T> = NoValueState|ValueState<T>|ErrorState;\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;AAYA;;;;;;;;;AASG;AACG,SAAU,kBAAkB,CAAI,UAAuB,EAAA;IAC3D,IAAI,CAAC,UAAU,EAAE;QACf,wBAAwB,CAAC,kBAAkB,CAAC,CAAC;AAC7C,QAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;KACjC;AAED,IAAA,MAAM,UAAU,GAAG,IAAI,UAAU,CAAO,QAAQ,IAAG;AACjD,QAAA,MAAM,YAAY,GAAG,UAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AACzE,QAAA,OAAO,YAAY,CAAC;AACtB,KAAC,CAAC,CAAC;IAEH,OAAO,CAAI,MAAqB,KAAI;QAClC,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC5C,KAAC,CAAC;AACJ;;ACvBA;;;;;AAKG;AACH,MAAM,uBAAuB,CAAA;AAK3B,IAAA,WAAA,CAAoB,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAJjC,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AAE1B,QAAA,IAAA,CAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AAG9B,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAK;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACxB,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,SAAS,CAAC,UAA8B,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,aAAa,CAAA,GAAA,+CAEnB,SAAS;gBACL,oDAAoD;AAChD,oBAAA,8CAA8C,CAAC,CAAC;SAC7D;;AAGD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;YACnF,IAAI,EAAE,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC;AACjC,SAAA,CAAC,CAAC;QAEH,OAAO;AACL,YAAA,WAAW,EAAE,MAAM,YAAY,CAAC,WAAW,EAAE;SAC9C,CAAC;KACH;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACa,SAAA,oBAAoB,CAChC,UAAyB,EAAE,IAAoB,EAAA;AACjD,IAAA,SAAS,IAAI,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;AAC5D,IAAA,OAAO,IAAI,uBAAuB,CAAI,UAAU,CAAC,CAAC;AACpD;;ACnEA;;;;;;;AAOG;AACG,SAAU,kBAAkB,CAAI,GAAiB,EAAA;AACrD,IAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAE7C,IAAA,OAAO,IAAI,UAAU,CAAI,QAAQ,IAAG;;;;QAIlC,UAAU,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;AAEjD,QAAA,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,QAAA,OAAO,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC;AAC1C,KAAC,CAAC,CAAC;AACL;;ACLA;;;;;;;;AAQG;AACa,SAAA,YAAY,CACxB,MAAiB,EACjB,OAA6B,EAAA;IAE/B,CAAC,OAAO,EAAE,QAAQ,IAAI,wBAAwB,CAAC,YAAY,CAAC,CAAC;IAC7D,MAAM,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;AACvD,IAAA,MAAM,OAAO,GAAG,IAAI,aAAa,CAAI,CAAC,CAAC,CAAC;AAExC,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAK;AAC1B,QAAA,IAAI,KAAQ,CAAC;AACb,QAAA,IAAI;YACF,KAAK,GAAG,MAAM,EAAE,CAAC;SAClB;QAAC,OAAO,GAAG,EAAE;YACZ,SAAS,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACpC,OAAO;SACR;QACD,SAAS,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACtC,EAAE,EAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAC,CAAC,CAAC;IAEpC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,MAAK;QACtC,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,OAAO,CAAC,YAAY,EAAE,CAAC;AAChC;;ACqBA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACa,SAAA,QAAQ,CACpB,MAAqC,EACrC,OAA4C,EAAA;IAC9C,SAAS;QACL,0BAA0B,CACtB,QAAQ,EACR,2DAA2D;AACvD,YAAA,oGAAoG,CAAC,CAAC;AAElH,IAAA,MAAM,eAAe,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;IAChD,eAAe,IAAI,CAAC,OAAO,EAAE,QAAQ,IAAI,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IAC5E,MAAM,UAAU,GACZ,eAAe,GAAG,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;;;AAItF,IAAA,IAAI,KAAiC,CAAC;AACtC,IAAA,IAAI,OAAO,EAAE,WAAW,EAAE;;QAExB,KAAK,GAAG,MAAM,CAAC,EAAC,IAAI,EAAmB,CAAA,0BAAC,CAAC,CAAC;KAC3C;SAAM;;AAEL,QAAA,KAAK,GAAG,MAAM,CAAa,EAAC,IAAI,EAAiB,CAAA,wBAAE,KAAK,EAAE,OAAO,EAAE,YAAiB,EAAC,CAAC,CAAC;KACxF;;;;;;;AAQD,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;AAC3B,QAAA,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,GAAG,CAAC,EAAC,IAAI,EAAA,CAAA,wBAAmB,KAAK,EAAC,CAAC;QACxD,KAAK,EAAE,KAAK,IAAG;AACb,YAAA,IAAI,OAAO,EAAE,YAAY,EAAE;;;AAGzB,gBAAA,MAAM,KAAK,CAAC;aACb;YACD,KAAK,CAAC,GAAG,CAAC,EAAC,IAAI,2BAAmB,KAAK,EAAC,CAAC,CAAC;SAC3C;;;AAGF,KAAA,CAAC,CAAC;AAEH,IAAA,IAAI,SAAS,IAAI,OAAO,EAAE,WAAW,IAAI,KAAK,EAAE,CAAC,IAAI,KAAsB,CAAA,0BAAE;AAC3E,QAAA,MAAM,IAAI,aAAa,CAEnB,GAAA,yDAAA,qFAAqF,CAAC,CAAC;KAC5F;;AAGD,IAAA,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;IAIjD,OAAO,QAAQ,CAAC,MAAK;AACnB,QAAA,MAAM,OAAO,GAAG,KAAK,EAAE,CAAC;AACxB,QAAA,QAAQ,OAAO,CAAC,IAAI;AAClB,YAAA,KAAA,CAAA;gBACE,OAAO,OAAO,CAAC,KAAK,CAAC;AACvB,YAAA,KAAA,CAAA;gBACE,MAAM,OAAO,CAAC,KAAK,CAAC;AACtB,YAAA,KAAA,CAAA;;;AAGE,gBAAA,MAAM,IAAI,aAAa,CAEnB,GAAA,yDAAA,qFAAqF,CAAC,CAAC;SAC9F;AACH,KAAC,CAAC,CAAC;AACL;;AChLA;;AAEG;;;;"}