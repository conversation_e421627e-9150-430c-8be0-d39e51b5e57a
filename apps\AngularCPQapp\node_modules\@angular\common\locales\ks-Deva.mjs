/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["ks-Deva", [["AM", "PM"], u, u], u, [["अ", "च", "ब", "ब", "ब", "ज", "ब"], ["आथवार", "चंदिरवार", "बुवार", "बोदवार", "ब्रेसवार", "जुम्मा", "बटवार"], u, u], u, [["ज", "फ़", "म", "अ", "म", "ज", "ज", "अ", "स", "ओ", "न", "द"], ["जनवरी", "फ़रवरी", "मार्च", "अप्रैल", "मे", "जून", "जुलाई", "अगस्त", "सतुंबर", "अकतुम्बर", "नवूमबर", "दसूमबर"], u], u, [["BC", "AD"], u, u], 0, [0, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["a h:mm", "a h:mm:ss", "a h:mm:ss z", "a h:mm:ss zzzz"], ["{1}, {0}", u, "{0} पेठ {1}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "INR", "₹", "इंडियन रूपी", { "JPY": ["JP¥", "¥"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoia3MtRGV2YS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvbW1vbi9sb2NhbGVzL2tzLURldmEudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsMENBQTBDO0FBQzFDLE1BQU0sQ0FBQyxHQUFHLFNBQVMsQ0FBQztBQUVwQixTQUFTLE1BQU0sQ0FBQyxHQUFXO0lBQzNCLE1BQU0sQ0FBQyxHQUFHLEdBQUcsQ0FBQztJQUVkLE9BQU8sQ0FBQyxDQUFDO0FBQ1QsQ0FBQztBQUVELGVBQWUsQ0FBQyxTQUFTLEVBQUMsQ0FBQyxDQUFDLElBQUksRUFBQyxJQUFJLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLE9BQU8sRUFBQyxVQUFVLEVBQUMsT0FBTyxFQUFDLFFBQVEsRUFBQyxVQUFVLEVBQUMsUUFBUSxFQUFDLE9BQU8sQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBQyxJQUFJLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxPQUFPLEVBQUMsUUFBUSxFQUFDLE9BQU8sRUFBQyxRQUFRLEVBQUMsSUFBSSxFQUFDLEtBQUssRUFBQyxPQUFPLEVBQUMsT0FBTyxFQUFDLFFBQVEsRUFBQyxVQUFVLEVBQUMsUUFBUSxFQUFDLFFBQVEsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsU0FBUyxFQUFDLFVBQVUsRUFBQyxnQkFBZ0IsQ0FBQyxFQUFDLENBQUMsUUFBUSxFQUFDLFdBQVcsRUFBQyxhQUFhLEVBQUMsZ0JBQWdCLENBQUMsRUFBQyxDQUFDLFVBQVUsRUFBQyxDQUFDLEVBQUMsYUFBYSxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEtBQUssRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLFdBQVcsRUFBQyxRQUFRLEVBQUMsWUFBWSxFQUFDLEtBQUssQ0FBQyxFQUFDLEtBQUssRUFBQyxHQUFHLEVBQUMsYUFBYSxFQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEVBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIFRISVMgQ09ERSBJUyBHRU5FUkFURUQgLSBETyBOT1QgTU9ESUZZLlxuY29uc3QgdSA9IHVuZGVmaW5lZDtcblxuZnVuY3Rpb24gcGx1cmFsKHZhbDogbnVtYmVyKTogbnVtYmVyIHtcbmNvbnN0IG4gPSB2YWw7XG5cbnJldHVybiA1O1xufVxuXG5leHBvcnQgZGVmYXVsdCBbXCJrcy1EZXZhXCIsW1tcIkFNXCIsXCJQTVwiXSx1LHVdLHUsW1tcIuCkhVwiLFwi4KSaXCIsXCLgpKxcIixcIuCkrFwiLFwi4KSsXCIsXCLgpJxcIixcIuCkrFwiXSxbXCLgpIbgpKXgpLXgpL7gpLBcIixcIuCkmuCkguCkpuCkv+CksOCkteCkvuCksFwiLFwi4KSs4KWB4KS14KS+4KSwXCIsXCLgpKzgpYvgpKbgpLXgpL7gpLBcIixcIuCkrOCljeCksOClh+CkuOCkteCkvuCksFwiLFwi4KSc4KWB4KSu4KWN4KSu4KS+XCIsXCLgpKzgpJ/gpLXgpL7gpLBcIl0sdSx1XSx1LFtbXCLgpJxcIixcIuCkq+CkvFwiLFwi4KSuXCIsXCLgpIVcIixcIuCkrlwiLFwi4KScXCIsXCLgpJxcIixcIuCkhVwiLFwi4KS4XCIsXCLgpJNcIixcIuCkqFwiLFwi4KSmXCJdLFtcIuCknOCkqOCkteCksOClgFwiLFwi4KSr4KS84KSw4KS14KSw4KWAXCIsXCLgpK7gpL7gpLDgpY3gpJpcIixcIuCkheCkquCljeCksOCliOCkslwiLFwi4KSu4KWHXCIsXCLgpJzgpYLgpKhcIixcIuCknOClgeCksuCkvuCkiFwiLFwi4KSF4KSX4KS44KWN4KSkXCIsXCLgpLjgpKTgpYHgpILgpKzgpLBcIixcIuCkheCkleCkpOClgeCkruCljeCkrOCksFwiLFwi4KSo4KS14KWC4KSu4KSs4KSwXCIsXCLgpKbgpLjgpYLgpK7gpKzgpLBcIl0sdV0sdSxbW1wiQkNcIixcIkFEXCJdLHUsdV0sMCxbMCwwXSxbXCJkL00veXlcIixcImQgTU1NIHlcIixcImQgTU1NTSB5XCIsXCJFRUVFLCBkIE1NTU0geVwiXSxbXCJhIGg6bW1cIixcImEgaDptbTpzc1wiLFwiYSBoOm1tOnNzIHpcIixcImEgaDptbTpzcyB6enp6XCJdLFtcInsxfSwgezB9XCIsdSxcInswfSDgpKrgpYfgpKAgezF9XCIsdV0sW1wiLlwiLFwiLFwiLFwiO1wiLFwiJVwiLFwiK1wiLFwiLVwiLFwiRVwiLFwiw5dcIixcIuKAsFwiLFwi4oieXCIsXCJOYU5cIixcIjpcIl0sW1wiIywjIzAuIyMjXCIsXCIjLCMjMCVcIixcIsKkwqAjLCMjMC4wMFwiLFwiI0UwXCJdLFwiSU5SXCIsXCLigrlcIixcIuCkh+CkguCkoeCkv+Ckr+CkqCDgpLDgpYLgpKrgpYBcIix7XCJKUFlcIjpbXCJKUMKlXCIsXCLCpVwiXX0sXCJsdHJcIiwgcGx1cmFsXTtcbiJdfQ==