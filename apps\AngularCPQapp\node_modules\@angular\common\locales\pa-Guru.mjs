/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === Math.floor(n) && (n >= 0 && n <= 1))
        return 1;
    return 5;
}
export default ["pa-Guru", [["ਸ.", "ਸ਼."], ["ਪੂ.ਦੁ.", "ਬਾ.ਦੁ."], u], [["ਪੂ.ਦੁ.", "ਬਾ.ਦੁ."], u, u], [["ਐ", "ਸੋ", "ਮੰ", "ਬੁੱ", "ਵੀ", "ਸ਼ੁੱ", "ਸ਼"], ["ਐਤ", "ਸੋਮ", "ਮੰਗਲ", "ਬੁੱਧ", "ਵੀਰ", "ਸ਼ੁੱਕਰ", "ਸ਼ਨਿੱਚਰ"], ["ਐਤਵਾਰ", "ਸੋਮਵਾਰ", "ਮੰਗਲਵਾਰ", "ਬੁੱਧਵਾਰ", "ਵੀਰਵਾਰ", "ਸ਼ੁੱਕਰਵਾਰ", "ਸ਼ਨਿੱਚਰਵਾਰ"], ["ਐਤ", "ਸੋਮ", "ਮੰਗ", "ਬੁੱਧ", "ਵੀਰ", "ਸ਼ੁੱਕ", "ਸ਼ਨਿੱ"]], u, [["ਜ", "ਫ਼", "ਮਾ", "ਅ", "ਮ", "ਜੂ", "ਜੁ", "ਅ", "ਸ", "ਅ", "ਨ", "ਦ"], ["ਜਨ", "ਫ਼ਰ", "ਮਾਰਚ", "ਅਪ੍ਰੈ", "ਮਈ", "ਜੂਨ", "ਜੁਲਾ", "ਅਗ", "ਸਤੰ", "ਅਕਤੂ", "ਨਵੰ", "ਦਸੰ"], ["ਜਨਵਰੀ", "ਫ਼ਰਵਰੀ", "ਮਾਰਚ", "ਅਪ੍ਰੈਲ", "ਮਈ", "ਜੂਨ", "ਜੁਲਾਈ", "ਅਗਸਤ", "ਸਤੰਬਰ", "ਅਕਤੂਬਰ", "ਨਵੰਬਰ", "ਦਸੰਬਰ"]], u, [["ਈ.ਪੂ.", "ਸੰਨ"], ["ਈ. ਪੂ.", "ਸੰਨ"], ["ਈਸਵੀ ਪੂਰਵ", "ਈਸਵੀ ਸੰਨ"]], 0, [0, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤ #,##,##0.00", "[#E0]"], "INR", "₹", "ਭਾਰਤੀ ਰੁਪਇਆ", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"], "XXX": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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