/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (i === 0 || n === 1)
        return 1;
    return 5;
}
export default ["fa", [["ق", "ب"], ["ق.ظ.", "ب.ظ."], ["قبل‌ازظهر", "بعدازظهر"]], u, [["ی", "د", "س", "چ", "پ", "ج", "ش"], ["یکشنبه", "دوشنبه", "سه‌شنبه", "چهارشنبه", "پنجشنبه", "جمعه", "شنبه"], u, ["۱ش", "۲ش", "۳ش", "۴ش", "۵ش", "ج", "ش"]], u, [["ژ", "ف", "م", "آ", "م", "ژ", "ژ", "ا", "س", "ا", "ن", "د"], ["ژانویه", "فوریه", "مارس", "آوریل", "مه", "ژوئن", "ژوئیه", "اوت", "سپتامبر", "اکتبر", "نوامبر", "دسامبر"], ["ژانویهٔ", "فوریهٔ", "مارس", "آوریل", "مهٔ", "ژوئن", "ژوئیهٔ", "اوت", "سپتامبر", "اکتبر", "نوامبر", "دسامبر"]], [["ژ", "ف", "م", "آ", "م", "ژ", "ژ", "ا", "س", "ا", "ن", "د"], ["ژانویه", "فوریه", "مارس", "آوریل", "مه", "ژوئن", "ژوئیه", "اوت", "سپتامبر", "اکتبر", "نوامبر", "دسامبر"], u], [["ق", "م"], ["ق.م.", "م."], ["قبل از میلاد", "میلادی"]], 6, [5, 5], ["y/M/d", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["H:mm", "H:mm:ss", "H:mm:ss (z)", "H:mm:ss (zzzz)"], ["{1}،‏ {0}", u, "{1}، ساعت {0}", u], [".", ",", ";", "%", "‎+", "‎−", "E", "×", "‰", "∞", "ناعدد", ":"], ["#,##0.###", "#,##0%", "‎¤ #,##0.00", "#E0"], "IRR", "ریال", "ریال ایران", { "AFN": ["؋"], "BYN": [u, "р."], "CAD": ["$CA", "$"], "CNY": ["¥CN", "¥"], "HKD": ["$HK", "$"], "IRR": ["ریال"], "MXN": ["$MX", "$"], "NZD": ["$NZ", "$"], "PHP": [u, "₱"], "THB": ["฿"], "XCD": ["$EC", "$"], "XOF": ["فرانک CFA"] }, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,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