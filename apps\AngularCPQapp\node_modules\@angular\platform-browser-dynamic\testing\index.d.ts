/**
 * @license Angular v17.3.12
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */


import * as i0 from '@angular/core';
import * as i1 from '@angular/platform-browser/testing';
import { PlatformRef } from '@angular/core';
import { StaticProvider } from '@angular/core';
import { TestComponentRenderer } from '@angular/core/testing';

/**
 * NgModule for testing.
 *
 * @publicApi
 */
export declare class BrowserDynamicTestingModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<BrowserDynamicTestingModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<BrowserDynamicTestingModule, never, never, [typeof i1.BrowserTestingModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<BrowserDynamicTestingModule>;
}

/**
 * @publicApi
 */
export declare const platformBrowserDynamicTesting: (extraProviders?: StaticProvider[] | undefined) => PlatformRef;

/**
 * A DOM based implementation of the TestComponentRenderer.
 */
export declare class ɵDOMTestComponentRenderer extends TestComponentRenderer {
    private _doc;
    constructor(_doc: any);
    insertRootElement(rootElId: string): void;
    removeAllRootElements(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ɵDOMTestComponentRenderer, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ɵDOMTestComponentRenderer>;
}

/**
 * Platform for dynamic tests
 *
 * @publicApi
 */
export declare const ɵplatformCoreDynamicTesting: (extraProviders?: any[]) => PlatformRef;

export { }
