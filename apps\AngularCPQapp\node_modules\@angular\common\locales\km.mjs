/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["km", [["a", "p"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["អ", "ច", "អ", "ព", "ព", "ស", "ស"], ["អាទិត្យ", "ចន្ទ", "អង្គារ", "ពុធ", "ព្រហ", "សុក្រ", "សៅរ៍"], ["អាទិត្យ", "ច័ន្ទ", "អង្គារ", "ពុធ", "ព្រហស្បតិ៍", "សុក្រ", "សៅរ៍"], ["អា", "ច", "អ", "ពុ", "ព្រ", "សុ", "ស"]], [["អ", "ច", "អ", "ព", "ព", "ស", "ស"], ["អាទិត្យ", "ចន្ទ", "អង្គារ", "ពុធ", "ព្រហ", "សុក្រ", "សៅរ៍"], ["អាទិត្យ", "ចន្ទ", "អង្គារ", "ពុធ", "ព្រហស្បតិ៍", "សុក្រ", "សៅរ៍"], ["អា", "ច", "អ", "ពុ", "ព្រ", "សុ", "ស"]], [["ម", "ក", "ម", "ម", "ឧ", "ម", "ក", "ស", "ក", "ត", "វ", "ធ"], ["មករា", "កុម្ភៈ", "មីនា", "មេសា", "ឧសភា", "មិថុនា", "កក្កដា", "សីហា", "កញ្ញា", "តុលា", "វិច្ឆិកា", "ធ្នូ"], u], u, [["មុន គ.ស.", "គ.ស."], u, ["មុន​គ្រិស្តសករាជ", "គ្រិស្តសករាជ"]], 0, [6, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} នៅ​ម៉ោង {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "KHR", "៛", "រៀល​កម្ពុជា", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "KHR": ["៛"], "LSL": ["ឡូទី"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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