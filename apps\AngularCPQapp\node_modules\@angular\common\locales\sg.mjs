/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["sg", [["ND", "LK"], u, u], u, [["K", "S", "T", "S", "K", "P", "Y"], ["Bk1", "Bk2", "Bk3", "Bk4", "Bk5", "Lâp", "<PERSON>â<PERSON>"], ["Bikua-ô<PERSON>", "Bïkua-û<PERSON>", "Bïkua-ptâ", "Bïkua-usïö", "Bïkua-okü", "Lâpôsö", "Lâyenga"], ["Bk1", "Bk2", "Bk3", "Bk4", "Bk5", "<PERSON>â<PERSON>", "<PERSON><PERSON><PERSON>"]], u, [["N", "F", "M", "N", "B", "F", "L", "K", "M", "N", "N", "K"], ["Nye", "Ful", "Mbä", "Ngu", "Bêl", "Fön", "Len", "Kük", "Mvu", "Ngb", "Nab", "Kak"], ["Nyenye", "Fulundïgi", "Mbängü", "Ngubùe", "Bêläwü", "Föndo", "Lengua", "Kükürü", "Mvuka", "Ngberere", "Nabändüru", "Kakauka"]], u, [["KnK", "NpK"], u, ["Kôzo na Krîstu", "Na pekô tî Krîstu"]], 1, [6, 0], ["d/M/y", "d MMM, y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00;¤-#,##0.00", "#E0"], "XAF", "FCFA", "farânga CFA (BEAC)", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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