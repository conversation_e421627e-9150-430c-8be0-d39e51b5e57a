/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["kok", [["a", "p"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["आ", "सो", "मं", "बु", "बि", "शु", "शे"], ["आयतार", "सोमार", "मंगळार", "बुधवार", "बिरेस्तार", "शुक्रार", "शेनवार"], u, ["आय", "सोम", "मंगळ", "बुध", "बिरे", "शुक्र", "शेन"]], [["आ", "सो", "मं", "बु", "ब", "शु", "शे"], ["आयतार", "सोमार", "मंगळार", "बुधवार", "बिरेस्तार", "शुक्रार", "शेनवार"], u, ["आय", "सोम", "मंगळ", "बुध", "बिरे", "शुक्र", "शेन"]], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["जानेवारी", "फेब्रुवारी", "मार्च", "एप्रील", "मे", "जून", "जुलय", "ऑगस्ट", "सप्टेंबर", "ऑक्टोबर", "नोव्हेंबर", "डिसेंबर"], u], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["जाने", "फेब्रु", "मार्च", "एप्री", "मे", "जून", "जुल", "ऑग", "सप्टें", "ऑक्टो", "नो", "डिसे"], ["जानेवारी", "फेब्रुवारी", "मार्च", "एप्रील", "मे", "जून", "जुलय", "ऑगस्ट", "सप्टेंबर", "ऑक्टोबर", "नोव्हेंबर", "डिसेंबर"]], [["क्रिस्तपूर्व", "क्रिस्तशखा"], u, u], 0, [0, 0], ["d-M-yy", "d-MMM-y", "d MMMM y", "EEEE d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "INR", "₹", "भारतीय रुपया", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "RON": ["रॉन", "लेई"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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