/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    return 5;
}
export default ["ast", [["a", "p"], ["AM", "PM"], ["de la mañana", "de la tarde"]], [["a", "p"], ["AM", "PM"], ["mañana", "tarde"]], [["D", "L", "M", "M", "X", "V", "S"], ["dom", "llu", "mar", "mié", "xue", "vie", "sáb"], ["domingu", "llunes", "martes", "miércoles", "xueves", "vienres", "sábadu"], ["do", "ll", "ma", "mi", "xu", "vi", "sá"]], u, [["X", "F", "M", "A", "M", "X", "X", "A", "S", "O", "P", "A"], ["xin", "feb", "mar", "abr", "may", "xun", "xnt", "ago", "set", "och", "pay", "avi"], ["de xineru", "de febreru", "de marzu", "d’abril", "de mayu", "de xunu", "de xunetu", "d’agostu", "de setiembre", "d’ochobre", "de payares", "d’avientu"]], [["X", "F", "M", "A", "M", "X", "X", "A", "S", "O", "P", "A"], ["Xin", "Feb", "Mar", "Abr", "May", "Xun", "Xnt", "Ago", "Set", "Och", "Pay", "Avi"], ["xineru", "febreru", "marzu", "abril", "mayu", "xunu", "xunetu", "agostu", "setiembre", "ochobre", "payares", "avientu"]], [["e.C.", "d.C."], u, ["enantes de Cristu", "después de Cristu"]], 1, [6, 0], ["d/M/yy", "d MMM y", "d MMMM 'de' y", "EEEE, d MMMM 'de' y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", "{1}, {0}", "{1} 'a' 'les' {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "ND", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "EUR", "€", "euro", { "DKK": [], "HRK": [], "ISK": [], "NOK": [], "PHP": [u, "₱"], "PLN": [], "SEK": [], "THB": ["฿"], "TWD": ["NT$"], "XXX": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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