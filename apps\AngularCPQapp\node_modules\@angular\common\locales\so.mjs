/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["so", [["h", "d"], ["GH", "GD"], u], [["AM", "GD"], u, ["GH", "GD"]], [["A", "I", "T", "A", "Kh", "J", "S"], ["Axd", "Isn", "Tldo", "Arbc", "Khms", "Jmc", "Sbti"], ["Axad", "Isniin", "<PERSON>laado", "Arb<PERSON>", "Khamiis", "<PERSON><PERSON>", "Sabti"], ["Axd", "Isn", "<PERSON><PERSON>", "<PERSON>rb<PERSON>", "<PERSON>hm<PERSON>", "<PERSON>mc", "<PERSON>bti"]], [["A", "I", "T", "A", "Kh", "J", "S"], ["Axd", "Isn", "Tldo", "Arbc", "Khms", "Jmc", "Sbti"], ["Axad", "Isniin", "Talaado", "Arbaco", "Khamiis", "Jimco", "Sabti"], ["Axd", "Isn", "Tldo", "Arbaco", "Khms", "Jmc", "Sbti"]], [["J", "F", "M", "A", "M", "J", "L", "O", "S", "O", "N", "D"], ["Jan", "Feb", "Mar", "Abr", "May", "Jun", "Lul", "Ogs", "Seb", "Okt", "Nof", "Dis"], ["Bisha Koobaad", "Bisha Labaad", "Bisha Saddexaad", "Bisha Afraad", "Bisha Shanaad", "Bisha Lixaad", "Bisha Todobaad", "Bisha Sideedaad", "Bisha Sagaalaad", "Bisha Tobnaad", "Bisha Kow iyo Tobnaad", "Bisha Laba iyo Tobnaad"]], [["J", "F", "M", "A", "M", "J", "L", "O", "S", "O", "N", "D"], ["Jan", "Feb", "Mar", "Abr", "May", "Jun", "Lul", "Ogs", "Seb", "Okt", "Nof", "Dis"], ["Jannaayo", "Febraayo", "Maarso", "Abriil", "May", "Juun", "Luuliyo", "Ogost", "Sebtembar", "Oktoobar", "Nofembar", "Desembar"]], [["B", "A"], ["BC", "AD"], ["Ciise Hortii", "Ciise Dabadii"]], 1, [6, 0], ["dd/MM/yy", "dd-MMM-y", "MMMM d, y", "EEEE, MMMM d, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", "{1} 'ee' {0}", u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "MaL", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "SOS", "S", "Shilingka Soomaaliya", { "BBD": ["DBB", "$"], "JPY": ["JP¥", "¥"], "SOS": ["S"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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