/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["lkt", [["AM", "PM"], u, u], u, [["A", "W", "N", "Y", "T", "Z", "O"], ["Aŋpétuwakȟaŋ", "Aŋpétuwaŋži", "Aŋp<PERSON>tunuŋpa", "Aŋpétuyamni", "<PERSON>ŋpétutopa", "<PERSON>ŋ<PERSON><PERSON>tuzaptaŋ", "Owáŋgyužažapi"], u, u], [["S", "M", "T", "W", "T", "F", "S"], ["Aŋpétuwakȟaŋ", "Aŋp<PERSON>tuwaŋži", "<PERSON>ŋ<PERSON><PERSON>tunuŋ<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>zapta<PERSON>", "Ow<PERSON>ŋgyužažapi"], u, u], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["Wiótheȟika Wí", "Thiyóȟeyuŋka Wí", "Ištáwičhayazaŋ Wí", "Pȟežítȟo Wí", "Čhaŋwápetȟo Wí", "Wípazukȟa-wašté Wí", "Čhaŋpȟásapa Wí", "Wasútȟuŋ Wí", "Čhaŋwápeǧi Wí", "Čhaŋwápe-kasná Wí", "Waníyetu Wí", "Tȟahékapšuŋ Wí"], u], u, [["BCE", "CE"], u, u], 0, [6, 0], ["M/d/yy", "MMM d, y", "MMMM d, y", "EEEE, MMMM d, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "USD", "$", "USD", { "JPY": ["JP¥", "¥"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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