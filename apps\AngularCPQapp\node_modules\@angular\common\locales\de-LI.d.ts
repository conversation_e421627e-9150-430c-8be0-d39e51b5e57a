/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    ATS: string[];
    AUD: string[];
    BGM: string[];
    BGO: string[];
    BYN: (string | undefined)[];
    CUC: (string | undefined)[];
    DEM: string[];
    EUR: (string | undefined)[];
    FKP: (string | undefined)[];
    GHS: (string | undefined)[];
    GNF: (string | undefined)[];
    KMF: (string | undefined)[];
    PHP: (string | undefined)[];
    RON: (string | undefined)[];
    RUR: (string | undefined)[];
    RWF: (string | undefined)[];
    SYP: never[];
    THB: string[];
    TWD: string[];
    XXX: never[];
    ZMW: (string | undefined)[];
})[];
export default _default;
