/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    BDT: (string | undefined)[];
    BRL: (string | undefined)[];
    CAD: (string | undefined)[];
    CNY: (string | undefined)[];
    CUP: (string | undefined)[];
    EGP: (string | undefined)[];
    EUR: (string | undefined)[];
    GBP: (string | undefined)[];
    HKD: (string | undefined)[];
    ILS: (string | undefined)[];
    INR: (string | undefined)[];
    ISK: (string | undefined)[];
    JPY: (string | undefined)[];
    KRW: (string | undefined)[];
    MXN: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    PYG: (string | undefined)[];
    SCR: string[];
    SEK: (string | undefined)[];
    TWD: (string | undefined)[];
    USD: (string | undefined)[];
    UYU: (string | undefined)[];
    VND: (string | undefined)[];
    XAF: never[];
    XCD: (string | undefined)[];
    XOF: never[];
    XPF: string[];
} | undefined)[];
export default _default;
