"use strict";var __spreadArray=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e,t,r,n,o;function a(){e=Zone.__symbol__,t=Object[e("defineProperty")]=Object.defineProperty,r=Object[e("getOwnPropertyDescriptor")]=Object.getOwnPropertyDescriptor,n=Object.create,o=e("unconfigurables"),Object.defineProperty=function(e,t,r){if(c(e,t))throw new TypeError("Cannot assign to read only property '"+t+"' of "+e);var n=r.configurable;return"prototype"!==t&&(r=l(e,t,r)),s(e,t,r,n)},Object.defineProperties=function(e,t){Object.keys(t).forEach((function(r){Object.defineProperty(e,r,t[r])}));for(var r=0,n=Object.getOwnPropertySymbols(t);r<n.length;r++){var o=n[r],a=Object.getOwnPropertyDescriptor(t,o);(null==a?void 0:a.enumerable)&&Object.defineProperty(e,o,t[o])}return e},Object.create=function(e,t){return"object"!=typeof t||Object.isFrozen(t)||Object.keys(t).forEach((function(r){t[r]=l(e,r,t[r])})),n(e,t)},Object.getOwnPropertyDescriptor=function(e,t){var n=r(e,t);return n&&c(e,t)&&(n.configurable=!1),n}}function i(e,t,r){var n=r.configurable;return s(e,t,r=l(e,t,r),n)}function c(e,t){return e&&e[o]&&e[o][t]}function l(e,r,n){return Object.isFrozen(n)||(n.configurable=!0),n.configurable||(e[o]||Object.isFrozen(e)||t(e,o,{writable:!0,value:{}}),e[o]&&(e[o][r]=!0)),n}function s(e,r,n,o){try{return t(e,r,n)}catch(c){if(!n.configurable)throw c;void 0===o?delete n.configurable:n.configurable=o;try{return t(e,r,n)}catch(t){var a=!1;if("createdCallback"!==r&&"attachedCallback"!==r&&"detachedCallback"!==r&&"attributeChangedCallback"!==r||(a=!0),!a)throw t;var i=null;try{i=JSON.stringify(n)}catch(e){i=n.toString()}console.log("Attempting to configure '".concat(r,"' with descriptor '").concat(i,"' on object '").concat(e,"' and got error, giving up: ").concat(t))}}}function p(e,t){var r=t.getGlobalObjects(),n=r.eventNames,o=r.globalSources,a=r.zoneSymbolEventNames,i=r.TRUE_STR,c=r.FALSE_STR,l=r.ZONE_SYMBOL_PREFIX,s="ApplicationCache,EventSource,FileReader,InputMethodContext,MediaController,MessagePort,Node,Performance,SVGElementInstance,SharedWorker,TextTrack,TextTrackCue,TextTrackList,WebKitNamedFlow,Window,Worker,WorkerGlobalScope,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload,IDBRequest,IDBOpenDBRequest,IDBDatabase,IDBTransaction,IDBCursor,DBIndex,WebSocket".split(","),p="EventTarget",u=[],d=e.wtf,f="Anchor,Area,Audio,BR,Base,BaseFont,Body,Button,Canvas,Content,DList,Directory,Div,Embed,FieldSet,Font,Form,Frame,FrameSet,HR,Head,Heading,Html,IFrame,Image,Input,Keygen,LI,Label,Legend,Link,Map,Marquee,Media,Menu,Meta,Meter,Mod,OList,Object,OptGroup,Option,Output,Paragraph,Pre,Progress,Quote,Script,Select,Source,Span,Style,TableCaption,TableCell,TableCol,Table,TableRow,TableSection,TextArea,Title,Track,UList,Unknown,Video".split(",");d?u=f.map((function(e){return"HTML"+e+"Element"})).concat(s):e[p]?u.push(p):u=s;for(var g=e.__Zone_disable_IE_check||!1,b=e.__Zone_enable_cross_context_check||!1,m=t.isIEOrEdge(),v="[object FunctionWrapper]",h="function __BROWSERTOOLS_CONSOLE_SAFEFUNC() { [native code] }",y={MSPointerCancel:"pointercancel",MSPointerDown:"pointerdown",MSPointerEnter:"pointerenter",MSPointerHover:"pointerhover",MSPointerLeave:"pointerleave",MSPointerMove:"pointermove",MSPointerOut:"pointerout",MSPointerOver:"pointerover",MSPointerUp:"pointerup"},_=0;_<n.length;_++){var O=l+((P=n[_])+c),w=l+(P+i);a[P]={},a[P][c]=O,a[P][i]=w}for(_=0;_<f.length;_++)for(var k=f[_],E=o[k]={},S=0;S<n.length;S++){var P;E[P=n[S]]=k+".addEventListener:"+P}var j=[];for(_=0;_<u.length;_++){var T=e[u[_]];j.push(T&&T.prototype)}return t.patchEventTarget(e,t,j,{vh:function(e,t,r,n){if(!g&&m){if(b)try{var o;if((o=t.toString())===v||o==h)return e.apply(r,n),!1}catch(t){return e.apply(r,n),!1}else if((o=t.toString())===v||o==h)return e.apply(r,n),!1}else if(b)try{t.toString()}catch(t){return e.apply(r,n),!1}return!0},transferEventName:function(e){return y[e]||e}}),Zone[t.symbol("patchEventTarget")]=!!e[p],!0}function u(e,t){var r=e.getGlobalObjects();if((!r.isNode||r.isMix)&&!function n(e,t){var r=e.getGlobalObjects();if((r.isBrowser||r.isMix)&&!e.ObjectGetOwnPropertyDescriptor(HTMLElement.prototype,"onclick")&&"undefined"!=typeof Element){var n=e.ObjectGetOwnPropertyDescriptor(Element.prototype,"onclick");if(n&&!n.configurable)return!1;if(n){e.ObjectDefineProperty(Element.prototype,"onclick",{enumerable:!0,configurable:!0,get:function(){return!0}});var o=!!document.createElement("div").onclick;return e.ObjectDefineProperty(Element.prototype,"onclick",n),o}}var a=t.XMLHttpRequest;if(!a)return!1;var i="onreadystatechange",c=a.prototype,l=e.ObjectGetOwnPropertyDescriptor(c,i);if(l)return e.ObjectDefineProperty(c,i,{enumerable:!0,configurable:!0,get:function(){return!0}}),o=!!(p=new a).onreadystatechange,e.ObjectDefineProperty(c,i,l||{}),o;var s=e.symbol("fake");e.ObjectDefineProperty(c,i,{enumerable:!0,configurable:!0,get:function(){return this[s]},set:function(e){this[s]=e}});var p,u=function(){};return(p=new a).onreadystatechange=u,o=p[s]===u,p.onreadystatechange=null,o}(e,t)){var o="undefined"!=typeof WebSocket;!function r(e){for(var t=e.symbol("unbound"),r=function(r){var n=d[r],o="on"+n;self.addEventListener(n,(function(r){var n,a,i=r.target;for(a=i?i.constructor.name+"."+o:"unknown."+o;i;)i[o]&&!i[o][t]&&((n=e.wrapWithCurrentZone(i[o],a))[t]=i[o],i[o]=n),i=i.parentElement}),!0)},n=0;n<d.length;n++)r(n)}(e),e.patchClass("XMLHttpRequest"),o&&function n(e,t){var r=e.getGlobalObjects(),n=r.ADD_EVENT_LISTENER_STR,o=r.REMOVE_EVENT_LISTENER_STR,a=t.WebSocket;t.EventTarget||e.patchEventTarget(t,e,[a.prototype]),t.WebSocket=function(t,r){var i,c,l=arguments.length>1?new a(t,r):new a(t),s=e.ObjectGetOwnPropertyDescriptor(l,"onmessage");return s&&!1===s.configurable?(i=e.ObjectCreate(l),c=l,[n,o,"send","close"].forEach((function(t){i[t]=function(){var r=e.ArraySlice.call(arguments);if(t===n||t===o){var a=r.length>0?r[0]:void 0;if(a){var c=Zone.__symbol__("ON_PROPERTY"+a);l[c]=i[c]}}return l[t].apply(l,r)}}))):i=l,e.patchOnProperties(i,["close","error","message","open"],c),i};var i=t.WebSocket;for(var c in a)i[c]=a[c]}(e,t),Zone[e.symbol("patchEvents")]=!0}}var d=__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray([],["abort","animationcancel","animationend","animationiteration","auxclick","beforeinput","blur","cancel","canplay","canplaythrough","change","compositionstart","compositionupdate","compositionend","cuechange","click","close","contextmenu","curechange","dblclick","drag","dragend","dragenter","dragexit","dragleave","dragover","drop","durationchange","emptied","ended","error","focus","focusin","focusout","gotpointercapture","input","invalid","keydown","keypress","keyup","load","loadstart","loadeddata","loadedmetadata","lostpointercapture","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","mousewheel","orientationchange","pause","play","playing","pointercancel","pointerdown","pointerenter","pointerleave","pointerlockchange","mozpointerlockchange","webkitpointerlockerchange","pointerlockerror","mozpointerlockerror","webkitpointerlockerror","pointermove","pointout","pointerover","pointerup","progress","ratechange","reset","resize","scroll","seeked","seeking","select","selectionchange","selectstart","show","sort","stalled","submit","suspend","timeupdate","volumechange","touchcancel","touchmove","touchstart","touchend","transitioncancel","transitionend","waiting","wheel"],!0),["webglcontextrestored","webglcontextlost","webglcontextcreationerror"],!0),["autocomplete","autocompleteerror"],!0),["toggle"],!0),["afterscriptexecute","beforescriptexecute","DOMContentLoaded","freeze","fullscreenchange","mozfullscreenchange","webkitfullscreenchange","msfullscreenchange","fullscreenerror","mozfullscreenerror","webkitfullscreenerror","msfullscreenerror","readystatechange","visibilitychange","resume"],!0),["absolutedeviceorientation","afterinput","afterprint","appinstalled","beforeinstallprompt","beforeprint","beforeunload","devicelight","devicemotion","deviceorientation","deviceorientationabsolute","deviceproximity","hashchange","languagechange","message","mozbeforepaint","offline","online","paint","pageshow","pagehide","popstate","rejectionhandled","storage","unhandledrejection","unload","userproximity","vrdisplayconnected","vrdisplaydisconnected","vrdisplaypresentchange"],!0),["beforecopy","beforecut","beforepaste","copy","cut","paste","dragstart","loadend","animationstart","search","transitionrun","transitionstart","webkitanimationend","webkitanimationiteration","webkitanimationstart","webkittransitionend"],!0),["activate","afterupdate","ariarequest","beforeactivate","beforedeactivate","beforeeditfocus","beforeupdate","cellchange","controlselect","dataavailable","datasetchanged","datasetcomplete","errorupdate","filterchange","layoutcomplete","losecapture","move","moveend","movestart","propertychange","resizeend","resizestart","rowenter","rowexit","rowsdelete","rowsinserted","command","compassneedscalibration","deactivate","help","mscontentzoom","msmanipulationstatechanged","msgesturechange","msgesturedoubletap","msgestureend","msgesturehold","msgesturestart","msgesturetap","msgotpointercapture","msinertiastart","mslostpointercapture","mspointercancel","mspointerdown","mspointerenter","mspointerhover","mspointerleave","mspointermove","mspointerout","mspointerover","mspointerup","pointerout","mssitemodejumplistitemremoved","msthumbnailclick","stop","storagecommit"],!0);!function f(){var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=e.__Zone_symbol_prefix||"__zone_symbol__";e[function r(e){return t+e}("legacyPatch")]=function(){var t=e.Zone;t.__load_patch("defineProperty",(function(e,t,r){r._redefineProperty=i,a()})),t.__load_patch("registerElement",(function(e,t,r){!function n(e,t){var r=t.getGlobalObjects();(r.isBrowser||r.isMix)&&"registerElement"in e.document&&t.patchCallbacks(t,document,"Document","registerElement",["createdCallback","attachedCallback","detachedCallback","attributeChangedCallback"])}(e,r)})),t.__load_patch("EventTargetLegacy",(function(e,t,r){p(e,r),u(r,e)}))}}()}));