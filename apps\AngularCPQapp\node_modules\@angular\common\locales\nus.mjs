/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["nus", [["RW", "TŊ"], u, u], u, [["C", "J", "R", "D", "Ŋ", "D", "B"], ["<PERSON>ä<PERSON>", "<PERSON><PERSON>", "Rɛw", "Diɔ̱k", "Ŋuaan", "<PERSON>hieec", "Bäkɛl"], ["<PERSON><PERSON><PERSON> kuɔth", "<PERSON><PERSON> la̱t", "<PERSON>ɛw lätni", "Diɔ̱k lätni", "Ŋuaan lätni", "<PERSON>hieec lätni", "Bäkɛl lätni"], ["<PERSON>äŋ", "Ji<PERSON>", "<PERSON>ɛw", "Diɔ̱k", "Ŋuaan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]], u, [["T", "<PERSON>", "D", "<PERSON>", "D", "K", "P", "T", "T", "L", "K", "T"], ["Tiop", "Pɛt", "Duɔ̱ɔ̱", "<PERSON>uak", "Duä", "Kor", "Pay", "Thoo", "Tɛɛ", "Laa", "Kur", "Tid"], ["Tiop thar pɛt", "Pɛt", "Duɔ̱ɔ̱ŋ", "Guak", "Duät", "Kornyoot", "Pay yie̱tni", "Tho̱o̱r", "Tɛɛr", "Laath", "Kur", "Tio̱p in di̱i̱t"]], u, [["AY", "ƐY"], u, ["A ka̱n Yecu ni dap", "Ɛ ca Yecu dap"]], 1, [6, 0], ["d/MM/y", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["h:mm a", "h:mm:ss a", "z h:mm:ss a", "zzzz h:mm:ss a"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "SSP", "£", "SSP", { "GBP": ["GB£", "£"], "JPY": ["JP¥", "¥"], "SSP": ["£"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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