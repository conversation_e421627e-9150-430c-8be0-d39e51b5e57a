/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    return 5;
}
export default ["fi", [["ap.", "ip."], u, u], u, [["S", "M", "T", "K", "T", "P", "L"], ["su", "ma", "ti", "ke", "to", "pe", "la"], ["sunnuntaina", "maanantaina", "tiistaina", "keskiviikkona", "torstaina", "perjantaina", "lauantaina"], ["su", "ma", "ti", "ke", "to", "pe", "la"]], [["S", "M", "T", "K", "T", "P", "L"], ["su", "ma", "ti", "ke", "to", "pe", "la"], ["sunnuntai", "maanantai", "tiistai", "keskiviikko", "torstai", "perjantai", "lauantai"], ["su", "ma", "ti", "ke", "to", "pe", "la"]], [["T", "H", "M", "H", "T", "K", "H", "E", "S", "L", "M", "J"], ["tammik.", "helmik.", "maalisk.", "huhtik.", "toukok.", "kesäk.", "heinäk.", "elok.", "syysk.", "lokak.", "marrask.", "jouluk."], ["tammikuuta", "helmikuuta", "maaliskuuta", "huhtikuuta", "toukokuuta", "kesäkuuta", "heinäkuuta", "elokuuta", "syyskuuta", "lokakuuta", "marraskuuta", "joulukuuta"]], [["T", "H", "M", "H", "T", "K", "H", "E", "S", "L", "M", "J"], ["tammi", "helmi", "maalis", "huhti", "touko", "kesä", "heinä", "elo", "syys", "loka", "marras", "joulu"], ["tammikuu", "helmikuu", "maaliskuu", "huhtikuu", "toukokuu", "kesäkuu", "heinäkuu", "elokuu", "syyskuu", "lokakuu", "marraskuu", "joulukuu"]], [["eKr", "jKr"], ["eKr.", "jKr."], ["ennen Kristuksen syntymää", "jälkeen Kristuksen syntymän"]], 1, [6, 0], ["d.M.y", u, "d. MMMM y", "cccc d. MMMM y"], ["H.mm", "H.mm.ss", "H.mm.ss z", "H.mm.ss zzzz"], ["{1} {0}", "{1} 'klo' {0}", u, u], [",", " ", ";", "%", "+", "−", "E", "×", "‰", "∞", "epäluku", "."], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "EUR", "€", "euro", { "AOA": [], "ARS": [], "AUD": [], "BAM": [], "BBD": [], "BDT": [], "BMD": [], "BND": [], "BOB": [], "BRL": [], "BSD": [], "BWP": [], "BZD": [], "CAD": [], "CLP": [], "CNY": [], "COP": [], "CRC": [], "CUC": [], "CUP": [], "CZK": [], "DKK": [], "DOP": [], "EGP": [], "ESP": [], "FIM": ["mk"], "FJD": [], "FKP": [], "GEL": [], "GIP": [], "GNF": [], "GTQ": [], "GYD": [], "HKD": [], "HNL": [], "HRK": [], "HUF": [], "IDR": [], "ILS": [], "INR": [], "ISK": [], "JMD": [], "KHR": [], "KMF": [], "KPW": [], "KRW": [], "KYD": [], "KZT": [], "LAK": [], "LBP": [], "LKR": [], "LRD": [], "LTL": [], "LVL": [], "MGA": [], "MMK": [], "MNT": [], "MUR": [], "MXN": [], "MYR": [], "NAD": [], "NGN": [], "NIO": [], "NOK": [], "NPR": [], "NZD": [], "PHP": [], "PKR": [], "PLN": [], "PYG": [], "RON": [], "RWF": [], "SBD": [], "SEK": [], "SGD": [], "SHP": [], "SRD": [], "SSP": [], "STN": [u, "STD"], "SYP": [], "THB": [], "TOP": [], "TRY": [], "TTD": [], "TWD": [], "UAH": [], "UYU": [], "VEF": [], "VND": [], "XCD": [], "XPF": [], "XXX": [], "ZAR": [], "ZMW": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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