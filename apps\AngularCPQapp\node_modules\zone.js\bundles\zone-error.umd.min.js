"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(r){"function"==typeof define&&define.amd?define(r):r()}((function(){!function r(e){e.__load_patch("Error",(function(r,e,n){var t,a,o,i,c,s=n.symbol("zoneJsInternalStackFrames"),u=r[n.symbol("Error")]=r.Error,f={};r.Error=d;var k="stackRewrite",l=r.__Zone_Error_BlacklistedStackFrames_policy||r.__Zone_Error_ZoneJsInternalStackFrames_policy||"default";function p(r,e,n){void 0===n&&(n=!0);for(var s=r.split("\n"),u=0;s[u]!==t&&s[u]!==a&&s[u]!==o&&s[u]!==i&&s[u]!==c&&u<s.length;)u++;for(;u<s.length&&e;u++){var k=s[u];if(k.trim())switch(f[k]){case 0:s.splice(u,1),u--;break;case 1:e=e.parent?e.parent:null,s.splice(u,1),u--;break;default:s[u]+=" [".concat(n?e.zone.name:e.zoneName,"]")}}return s.join("\n")}function d(){var r=this,e=u.apply(this,arguments),t=e.originalStack=e.stack;if(d[k]&&t){var a=n.currentZoneFrame();if("lazy"===l)e[n.symbol("zoneFrameNames")]=function r(e){for(var n={zoneName:e.zone.name},t=n;e.parent;){var a={zoneName:(e=e.parent).zone.name};n.parent=a,n=a}return t}(a);else if("default"===l)try{e.stack=e.zoneAwareStack=p(t,a)}catch(r){}}return this instanceof u&&this.constructor!=u?(Object.keys(e).concat("stack","message").forEach((function(n){var t=e[n];if(void 0!==t)try{r[n]=t}catch(r){}})),this):e}d.prototype=u.prototype,d[s]=f,d[k]=!1;var m=n.symbol("zoneAwareStack");"lazy"===l&&Object.defineProperty(d.prototype,"zoneAwareStack",{configurable:!0,enumerable:!0,get:function(){return this[m]||(this[m]=p(this.originalStack,this[n.symbol("zoneFrameNames")],!1)),this[m]},set:function(r){this.originalStack=r,this[m]=p(this.originalStack,this[n.symbol("zoneFrameNames")],!1)}});var h=["stackTraceLimit","captureStackTrace","prepareStackTrace"],T=Object.keys(u);if(T&&T.forEach((function(r){0===h.filter((function(e){return e===r})).length&&Object.defineProperty(d,r,{get:function(){return u[r]},set:function(e){u[r]=e}})})),u.hasOwnProperty("stackTraceLimit")&&(u.stackTraceLimit=Math.max(u.stackTraceLimit,15),Object.defineProperty(d,"stackTraceLimit",{get:function(){return u.stackTraceLimit},set:function(r){return u.stackTraceLimit=r}})),u.hasOwnProperty("captureStackTrace")&&Object.defineProperty(d,"captureStackTrace",{value:function r(e,n){u.captureStackTrace(e,n)}}),Object.defineProperty(d,"prepareStackTrace",{get:function(){return u.prepareStackTrace},set:function(r){return u.prepareStackTrace=r&&"function"==typeof r?function(e,n){if(n)for(var t=0;t<n.length;t++)if("zoneCaptureStackTrace"===n[t].getFunctionName()){n.splice(t,1);break}return r.call(this,e,n)}:r}}),"disable"!==l){var v=e.current.fork({name:"detect",onHandleError:function(r,e,n,s){if(s.originalStack&&Error===d)for(var u=s.originalStack.split(/\n/),l=!1,p=!1,m=!1;u.length;){var h=u.shift();if(/:\d+:\d+/.test(h)||"ZoneAwareError"===h){var T=h.split("(")[0].split("@")[0],v=1;if(-1!==T.indexOf("ZoneAwareError")&&(-1!==T.indexOf("new ZoneAwareError")?(t=h,a=h.replace("new ZoneAwareError","new Error.ZoneAwareError")):(o=h,i=h.replace("Error.",""),-1===h.indexOf("Error.ZoneAwareError")&&(c=h.replace("ZoneAwareError","Error.ZoneAwareError"))),f[a]=0),-1!==T.indexOf("runGuarded")?p=!0:-1!==T.indexOf("runTask")?m=!0:-1!==T.indexOf("run")?l=!0:v=0,f[h]=v,l&&p&&m){d[k]=!0;break}}}return!1}}).fork({name:"child",onScheduleTask:function(r,e,n,t){return r.scheduleTask(n,t)},onInvokeTask:function(r,e,n,t,a,o){return r.invokeTask(n,t,a,o)},onCancelTask:function(r,e,n,t){return r.cancelTask(n,t)},onInvoke:function(r,e,n,t,a,o,i){return r.invoke(n,t,a,o,i)}}),E=Error.stackTraceLimit;Error.stackTraceLimit=100,v.run((function(){v.runGuarded((function(){var r=function(){};v.scheduleEventTask(s,(function(){v.scheduleMacroTask(s,(function(){v.scheduleMicroTask(s,(function(){throw new Error}),void 0,(function(e){e._transitionTo=r,e.invoke()})),v.scheduleMicroTask(s,(function(){throw Error()}),void 0,(function(e){e._transitionTo=r,e.invoke()}))}),void 0,(function(e){e._transitionTo=r,e.invoke()}),(function(){}))}),void 0,(function(e){e._transitionTo=r,e.invoke()}),(function(){}))}))})),Error.stackTraceLimit=E}}))}(Zone)}));