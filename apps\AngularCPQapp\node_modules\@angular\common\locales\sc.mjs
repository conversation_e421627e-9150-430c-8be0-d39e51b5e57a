/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    return 5;
}
export default ["sc", [["m.", "b."], ["AM", "PM"], u], [["AM", "PM"], u, u], [["D", "L", "M", "M", "G", "C", "S"], ["dom", "lun", "mar", "mèr", "giò", "che", "sàb"], ["dom<PERSON><PERSON><PERSON>", "lunis", "martis", "mè<PERSON><PERSON>s", "gi<PERSON>bia", "chen<PERSON><PERSON>a", "sàbadu"], ["dom", "lun", "mar", "mèr", "giò", "che", "sàb"]], u, [["G", "F", "M", "A", "M", "L", "T", "A", "C", "S", "S", "N"], ["ghe", "fre", "mar", "abr", "maj", "làm", "trì", "aus", "cab", "stG", "stA", "nad"], ["ghennàrgiu", "freàrgiu", "martzu", "abrile", "maju", "làmpadas", "trìulas", "austu", "cabudanni", "santugaine", "santandria", "nadale"]], u, [["a.C.", "p.C."], u, ["in antis de Cristu", "a pustis de Cristu"]], 1, [6, 0], ["dd/MM/y", "d 'de' MMM y", "d 'de' MMMM 'de' 'su' y", "d 'de' MMMM 'de' 'su' y, 'de' EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, "{1} 'a' 'sas' {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "EUR", "€", "èuro", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"], "XDR": ["DIP"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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