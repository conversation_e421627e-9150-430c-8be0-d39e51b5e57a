/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    if (n === 2)
        return 2;
    return 5;
}
export default ["smn", [["ip.", "ep."], u, u], u, [["p", "V", "M", "K", "T", "V", "L"], ["pas", "vuo", "maj", "kos", "tuo", "vás", "láv"], ["pasepeeivi", "vuossaargâ", "majebaargâ", "koskoho", "tuorâstuv", "vástuppeeivi", "lávurduv"], ["pa", "vu", "ma", "ko", "tu", "vá", "lá"]], [["S", "M", "T", "W", "T", "F", "S"], ["pas", "vuo", "maj", "kos", "tuo", "vás", "láv"], ["pasepeivi", "vuossargâ", "majebargâ", "koskokko", "tuorâstâh", "vástuppeivi", "lávurdâh"], ["pa", "vu", "ma", "ko", "tu", "vá", "lá"]], [["U", "K", "NJ", "C", "V", "K", "S", "P", "Č", "R", "S", "J"], ["uđiv", "kuovâ", "njuhčâ", "cuáŋui", "vyesi", "kesi", "syeini", "porge", "čohčâ", "roovvâd", "skammâ", "juovlâ"], ["uđđâivemáánu", "kuovâmáánu", "njuhčâmáánu", "cuáŋuimáánu", "vyesimáánu", "kesimáánu", "syeinimáánu", "porgemáánu", "čohčâmáánu", "roovvâdmáánu", "skammâmáánu", "juovlâmáánu"]], u, [["oKr.", "mKr."], u, ["Ovdil Kristus šoddâm", "maŋa Kristus šoddâm"]], 1, [6, 0], ["d.M.y", "MMM d. y", "MMMM d. y", "cccc, MMMM d. y"], ["H.mm", "H.mm.ss", "H.mm.ss z", "H.mm.ss zzzz"], ["{1} {0}", "{1} 'tme' {0}", u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "epiloho", "."], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "EUR", "€", "euro", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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