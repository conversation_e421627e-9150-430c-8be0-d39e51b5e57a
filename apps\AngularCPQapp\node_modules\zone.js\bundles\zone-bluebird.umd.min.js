"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(n){"function"==typeof define&&define.amd?define(n):n()}((function(){!function n(e){e.__load_patch("bluebird",(function(n,e,o){e[e.__symbol__("bluebird")]=function t(i){["then","spread","finally"].forEach((function(n){o.patchMethod(i.prototype,n,(function(n){return function(o,t){for(var r=e.current,c=function(n){var e=t[n];"function"==typeof e&&(t[n]=function(){var n=this,o=arguments;return new i((function(t,i){r.scheduleMicroTask("Promise.then",(function(){try{t(e.apply(n,o))}catch(n){i(n)}}))}))})},d=0;d<t.length;d++)c(d);return n.apply(o,t)}}))})),"undefined"!=typeof window?window.addEventListener("unhandledrejection",(function(n){var e=n.detail&&n.detail.reason;e&&e.isHandledByZone&&(n.preventDefault(),"function"==typeof n.stopImmediatePropagation&&n.stopImmediatePropagation())})):"undefined"!=typeof process&&process.on("unhandledRejection",(function(n,e){if(n&&n.isHandledByZone){var o=process.listeners("unhandledRejection");o&&(process.removeAllListeners("unhandledRejection"),process.nextTick((function(){o.forEach((function(n){return process.on("unhandledRejection",n)}))})))}})),i.onPossiblyUnhandledRejection((function(n,t){try{e.current.runGuarded((function(){throw n.isHandledByZone=!0,n}))}catch(n){n.isHandledByZone=!1,o.onUnhandledError(n)}})),n.Promise=i}}))}(Zone)}));